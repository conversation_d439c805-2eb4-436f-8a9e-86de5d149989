# Pencairan Bansos - Upload File Updates

## Perubahan yang Dilakukan ✅

### 1. **Logika Upload Sekali Per Data**

- Menambahkan field `file_uploaded` di interface `PencairanBansos`
- Tombol "Unggah File" menjadi disabled setelah file diupload
- <PERSON><PERSON><PERSON> be<PERSON>h menjadi "Sudah Diupload" dengan styling disabled
- Menambahkan kolom "Status Upload" di table untuk tracking

### 2. **Menyederhanakan Pilihan Jenis File**

- Menghapus pilihan "Bukti Transfer" dan "Dokumen Pendukung"
- <PERSON><PERSON> menyisakan "Surat Pencairan" sebagai satu-satunya pilihan
- Mengubah select menjadi input disabled dengan default value "Surat Pencairan"
- Auto-set `jenis_file` ke `'surat_pencairan'` di form data

### 3. **Update UI/UX**

- Menambahkan kolom "Status Upload" dengan badge indicators:
    - 🟢 "✓ Sudah Upload" (hijau) untuk yang sudah upload
    - 🟡 "⏳ Belum Upload" (kuning) untuk yang belum upload
- <PERSON>ol berubah visual ketika file sudah diupload:
    - Aktif: <PERSON>ja<PERSON> dengan "Unggah File"
    - Disabled: <PERSON><PERSON><PERSON><PERSON> dengan "Sudah Diupload"

### 4. **Validasi dan Form Handling**

- Menghapus validasi `jenis_file` karena sudah auto-set
- Hanya validasi `file` yang diperlukan
- Auto-reset `jenis_file` ke default saat modal ditutup

## Kode yang Dimodifikasi

### Interface Update

```tsx
interface PencairanBansos {
    // ...existing fields
    file_uploaded?: boolean; // Tambahan field tracking
}
```

### Form Default Value

```tsx
const { data, setData, post, processing, reset, errors } = useForm({
    pengajuan_bansos_id: '',
    file: null as File | null,
    jenis_file: 'surat_pencairan', // Auto-set default
    keterangan: '',
});
```

### Conditional Button Rendering

```tsx
{
    item.file_uploaded ? (
        <Button disabled className="cursor-not-allowed bg-gray-400">
            <Upload className="h-4 w-4" />
            <span>Sudah Diupload</span>
        </Button>
    ) : (
        <Button className="bg-green-600 hover:bg-green-700" onClick={() => handleUploadFile(item)}>
            <Upload className="h-4 w-4" />
            <span>Unggah File</span>
        </Button>
    );
}
```

### Status Badge Column

```tsx
<TableCell className="text-center">
    {item.file_uploaded ? (
        <span className="rounded-full bg-green-100 px-2.5 py-0.5 text-green-800">✓ Sudah Upload</span>
    ) : (
        <span className="rounded-full bg-yellow-100 px-2.5 py-0.5 text-yellow-800">⏳ Belum Upload</span>
    )}
</TableCell>
```

### Simplified File Type Input

```tsx
<div>
    <Label htmlFor="jenis_file">Jenis File</Label>
    <Input value="Surat Pencairan" disabled className="bg-gray-50" />
    <p className="text-xs text-gray-500">Jenis file telah otomatis diatur ke Surat Pencairan</p>
</div>
```

## Table Structure Baru

| No  | Nama Panti | Kabupaten/Kota | Tanggal Pengajuan | Status Upload   | Aksi          |
| --- | ---------- | -------------- | ----------------- | --------------- | ------------- |
| 1   | Panti A    | Kota A         | 18 Jul 2025       | ✓ Sudah Upload  | [Disabled]    |
| 2   | Panti B    | Kota B         | 17 Jul 2025       | ⏳ Belum Upload | [Unggah File] |

## Hasil Akhir

✅ **User hanya bisa upload file sekali per data**
✅ **Tidak ada pilihan jenis file** - otomatis "Surat Pencairan"
✅ **UI yang jelas** menunjukkan status upload
✅ **Tombol disabled** setelah upload berhasil
✅ **Validasi sederhana** hanya untuk file

## Backend Requirements

⚠️ **Perlu update di backend:**

- Field `file_uploaded` perlu ditambahkan ke response data
- Route upload perlu update field ini ke `true` setelah berhasil upload
- Model/Controller perlu disesuaikan untuk tracking upload status

## Testing Checklist

- [x] UI rendering dengan baik
- [x] TypeScript compilation berhasil
- [x] Form validation sesuai
- [ ] Backend integration test
- [ ] Upload functionality test
- [ ] Status tracking test

File upload sekarang bekerja dengan logika "sekali upload per data" dan hanya fokus pada Surat Pencairan! 🎉
