<?php
require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\CalonOrangTuaAsuh;
use App\Models\Cota;

echo "=== Creating additional CalonOrangTuaAsuh data ===\n";

// Get existing COTA data that we created earlier
$cotaUnverified = Cota::where('email', '<EMAIL>')->first();
$cotaVerified = Cota::where('email', '<EMAIL>')->first();

// Create CalonOrangTuaAsuh for the unverified COTA
if ($cotaUnverified) {
    $existingCalon = CalonOrangTuaAsuh::where('cota_id', $cotaUnverified->id)->first();
    if (!$existingCalon) {
        CalonOrangTuaAsuh::create([
            'cota_id' => $cotaUnverified->id,
            'nama_lengkap' => $cotaUnverified->nama,
            'nik' => $cotaUnverified->nik,
            'kabupaten_kota' => $cotaUnverified->kabupaten_kota,
            'alamat' => 'Jl. Test Unverified No. 123, Pekanbaru',
            'telepon' => $cotaUnverified->telepon,
            'email' => $cotaUnverified->email,
            'status' => 'Diproses',
            'keterangan' => 'Dokumen sedang dalam tahap verifikasi',
            'file_ktp' => 'documents/test_ktp_unverified.pdf',
            'file_kk' => 'documents/test_kk_unverified.pdf',
            'file_surat_pernikahan' => 'documents/test_nikah_unverified.pdf',
            'file_slip_gaji' => 'documents/test_gaji_unverified.pdf',
            'file_surat_keterangan_sehat' => 'documents/test_sehat_unverified.pdf',
        ]);
        echo "Created CalonOrangTuaAsuh for unverified COTA user\n";
    } else {
        echo "CalonOrangTuaAsuh already exists for unverified COTA user\n";
    }
}

// Create CalonOrangTuaAsuh for the verified COTA
if ($cotaVerified) {
    $existingCalon = CalonOrangTuaAsuh::where('cota_id', $cotaVerified->id)->first();
    if (!$existingCalon) {
        CalonOrangTuaAsuh::create([
            'cota_id' => $cotaVerified->id,
            'nama_lengkap' => $cotaVerified->nama,
            'nik' => $cotaVerified->nik,
            'kabupaten_kota' => $cotaVerified->kabupaten_kota,
            'alamat' => 'Jl. Test Verified No. 456, Pekanbaru',
            'telepon' => $cotaVerified->telepon,
            'email' => $cotaVerified->email,
            'status' => 'Diterima',
            'keterangan' => 'Dokumen sudah terverifikasi dan disetujui',
            'file_ktp' => 'documents/test_ktp_verified.pdf',
            'file_kk' => 'documents/test_kk_verified.pdf',
            'file_surat_pernikahan' => 'documents/test_nikah_verified.pdf',
            'file_slip_gaji' => 'documents/test_gaji_verified.pdf',
            'file_surat_keterangan_sehat' => 'documents/test_sehat_verified.pdf',
        ]);
        echo "Created CalonOrangTuaAsuh for verified COTA user\n";
    } else {
        echo "CalonOrangTuaAsuh already exists for verified COTA user\n";
    }
}

// Show current total count
$totalCount = CalonOrangTuaAsuh::count();
echo "\nTotal CalonOrangTuaAsuh records now: $totalCount\n";

// Show sample data
echo "\nLatest 5 records:\n";
$latestRecords = CalonOrangTuaAsuh::orderBy('created_at', 'desc')->take(5)->get();
foreach($latestRecords as $record) {
    echo "- ID: {$record->id}, Nama: {$record->nama_lengkap}, Status: {$record->status}, Kab/Kota: {$record->kabupaten_kota}\n";
}

echo "\nDone!\n";
