<?php
require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\CalonOrangTuaAsuh;

$calon = CalonOrangTuaAsuh::where('status', 'Ditolak')->first();
if($calon) {
    $calon->keterangan = 'Dokumen KTP tidak jelas dan tidak sesuai dengan persyaratan yang telah ditetapkan. Mohon untuk mengunggah ulang dokumen dengan kualitas yang lebih baik.';
    $calon->save();
    echo 'Updated rejection reason for record ID: ' . $calon->id . "\n";
    echo 'Rejection reason: ' . $calon->rejection_reason . "\n";
} else {
    echo 'No rejected record found' . "\n";
}
