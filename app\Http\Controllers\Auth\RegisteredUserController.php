<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Panti;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => 'required|string|in:panti_asuhan,dinsos_provinsi_riau,dinsos_kabupaten_kota,cota',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->input('role'),
        ]);

        // If the user is registering as panti_asuhan, create a Panti entry
        if ($request->input('role') === 'panti_asuhan') {
            \App\Models\Panti::create([
                'user_id' => $user->id,
                'nama' => '', // Will be filled when user completes profile
                'alamat' => '', // Will be filled when user completes profile
                'kabupaten' => '', // Will be filled when user completes profile
                'telepon' => '',
                'email' => '', // Will be filled when user completes profile
                'pimpinan' => '', // Will be filled when user completes profile
                'npwp' => '',
                'status' => 'pending', // Default status for new registrations
                'admin_notes' => 'Panti terdaftar melalui sistem registrasi online',
            ]);
        }

        event(new Registered($user));

        // For panti_asuhan role, don't login automatically - redirect to login page
        if ($request->input('role') === 'panti_asuhan') {
            return redirect()->route('login')->with('success', 'Registrasi berhasil! Silakan login dengan kredensial yang baru saja Anda buat.');
        }

        Auth::login($user);

        // Redirect based on user role
        $redirectRoute = match ($request->input('role')) {
            'dinsos_provinsi_riau' => route('dinsosriau.dashboard'),
            'dinsos_kabupaten_kota' => route('dinsoskota.dashboard'),
            'cota' => route('cota.dashboard'),
            default => route('dashboard', absolute: false),
        };

        return redirect()->intended($redirectRoute);
    }
}
