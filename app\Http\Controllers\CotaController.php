<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CalonOrangTuaAsuh;
use App\Models\Cota;
use App\Models\JadwalSidang;
use Inertia\Inertia;

class CotaController extends Controller
{
    public function dashboard()
    {
        $user = auth()->user();
        
        // Cari data COTA user saat ini
        $cotaData = Cota::where('email', $user->email)->first();
        
        // Inisialisasi variabel
        $calonOrangTuaAsuh = null;
        $applicationStatus = null;
        $progressPercentage = 0;
        
        if ($cotaData) {
            $calonOrangTuaAsuh = CalonOrangTuaAsuh::where('cota_id', $cotaData->id)->first();
            
            if ($calonOrangTuaAsuh) {
                // Tentukan status dan persentase progress berdasarkan data dari Dinsos Provinsi Riau
                switch ($calonOrangTuaAsuh->status) {
                    case 'Diproses':
                        $applicationStatus = [
                            'title' => 'Sedang Diproses',
                            'description' => 'Aplikasi Anda sedang dalam tahap review oleh Dinsos Provinsi Riau',
                            'color' => 'blue',
                            'percentage' => 50
                        ];
                        $progressPercentage = 50;
                        break;
                    case 'Diterima':
                        $applicationStatus = [
                            'title' => 'Diterima',
                            'description' => 'Selamat! Aplikasi Anda telah disetujui oleh Dinsos Provinsi Riau',
                            'color' => 'green',
                            'percentage' => 100
                        ];
                        $progressPercentage = 100;
                        break;
                    case 'Ditolak':
                        $applicationStatus = [
                            'title' => 'Ditolak',
                            'description' => 'Aplikasi Anda tidak dapat diproses. Silakan hubungi petugas untuk informasi lebih lanjut',
                            'color' => 'red',
                            'percentage' => 25
                        ];
                        $progressPercentage = 25;
                        break;
                    default:
                        $applicationStatus = [
                            'title' => 'Menunggu Review',
                            'description' => 'Aplikasi Anda menunggu untuk diproses',
                            'color' => 'yellow',
                            'percentage' => 30
                        ];
                        $progressPercentage = 30;
                }
            } else {
                // COTA terdaftar tapi belum masuk ke sistem Dinsos Provinsi Riau
                $applicationStatus = [
                    'title' => 'Pendaftaran Berhasil',
                    'description' => 'Dokumen Anda sedang dipersiapkan untuk review',
                    'color' => 'blue',
                    'percentage' => 25
                ];
                $progressPercentage = 25;
            }
        } else {
            // User belum terdaftar sebagai COTA
            $applicationStatus = [
                'title' => 'Belum Terdaftar',
                'description' => 'Anda belum terdaftar sebagai Calon Orang Tua Asuh',
                'color' => 'gray',
                'percentage' => 0
            ];
            $progressPercentage = 0;
        }

        // Get upcoming jadwal sidang - only show for verified COTA users
        $jadwalSidang = [];
        if ($cotaData && !in_array($cotaData->status, ['menunggu_verifikasi', 'ditolak'])) {
            $jadwalSidang = JadwalSidang::where('tanggal_sidang', '>=', now())
                                        ->orderBy('tanggal_sidang', 'asc')
                                        ->limit(5)
                                        ->get();
        }

        return Inertia::render('Cota/Dashboard', [
            'user' => $user,
            'modules' => [
                'cota' => $user->canAccessModule('cota'),
            ],
            'applicationStatus' => $applicationStatus,
            'progressPercentage' => $progressPercentage,
            'cotaData' => $cotaData,
            'calonOrangTuaAsuh' => $calonOrangTuaAsuh,
            'jadwalSidang' => $jadwalSidang,
        ]);
    }
}
