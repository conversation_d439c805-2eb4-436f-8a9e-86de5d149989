<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use App\Models\Cota;
use App\Models\CotaDokumenPersyaratan;
use App\Models\CalonOrangTuaAsuh;
use App\Models\User;
use App\Models\Panti;
use App\Models\Anak;
use Inertia\Inertia;

class DinsosKotaController extends Controller
{
    public function dashboard()
    {
        // Hitung statistik COTA secara real-time
        $totalCota = Cota::count();
        $prosesVerifikasi = Cota::where('status', 'proses_verifikasi')->count();
        $tersertifikasi = Cota::where('status', 'tersertifikasi')->count();
        $menungguVerifikasi = Cota::where('status', 'menunggu_verifikasi')->count();
        $terdaftar = Cota::where('status', 'terdaftar')->count();
        
        // Hitung tingkat persetujuan (persentase yang tersertifikasi dari total)
        $tingkatPersetujuan = $totalCota > 0 ? round(($tersertifikasi / $totalCota) * 100) : 0;

        return Inertia::render('DinsosKota/Dashboard', [
            'user' => auth()->user(),
            'modules' => [
                'cota' => auth()->user()->canAccessModule('cota'),
            ],
            'statistics' => [
                'totalCota' => $totalCota,
                'prosesVerifikasi' => $prosesVerifikasi,
                'tersertifikasi' => $tersertifikasi,
                'menungguVerifikasi' => $menungguVerifikasi,
                'terdaftar' => $terdaftar,
                'tingkatPersetujuan' => $tingkatPersetujuan,
            ]
        ]);
    }

    public function daftarCota()
    {
        $cotas = Cota::with('verifiedBy')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($cota) {
                return [
                    'id' => $cota->id,
                    'nama' => $cota->nama,
                    'email' => $cota->email,
                    'alamat' => $cota->kabupaten_kota,
                    'status' => $cota->status_label,
                    'tanggal_daftar' => $cota->created_at->format('Y-m-d'),
                ];
            });

        // Get all pantis for selection (using actual statuses from database)
        $pantis = Panti::whereIn('status', ['pending', 'diverifikasi', 'approved'])
            ->select('id', 'nama', 'alamat', 'pimpinan')
            ->get()
            ->map(function ($panti) {
                return [
                    'id' => $panti->id,
                    'nama' => $panti->nama,
                    'alamat' => $panti->alamat ?? 'Alamat tidak tersedia',
                    'pimpinan' => $panti->pimpinan ?? 'Pimpinan tidak tersedia',
                ];
            });

        // Get all available children - using direct panti relationship from user_id
        $anaks = Anak::with('user')
            ->whereIn('status_anak', ['Tersedia', 'aktif']) // Include 'aktif' status as available
            ->get()
            ->map(function ($anak) {
                // Get panti data from the user who owns this child
                $panti = Panti::where('user_id', $anak->user_id)->first();
                
                return [
                    'id' => $anak->id,
                    'nama_lengkap' => $anak->nama_lengkap,
                    'usia' => $anak->usia ?? 0,
                    'jenis_kelamin' => $anak->jenis_kelamin ?? 'Tidak diketahui',
                    'status_anak' => $anak->status_anak,
                    'panti_id' => $panti->id ?? null,
                    'panti_nama' => $panti->nama ?? 'Tidak diketahui',
                ];
            })->filter(function ($anak) {
                return $anak['panti_id'] !== null;
            })->values();

        return Inertia::render('DinsosKota/DaftarCota', [
            'user' => auth()->user(),
            'cotas' => $cotas,
            'pantis' => $pantis,
            'anaks' => $anaks,
        ]);
    }

    public function registerCota(Request $request)
    {
        // Pastikan limit upload file PHP cukup besar untuk 27 dokumen COTA
        ini_set('upload_max_filesize', '20M');
        ini_set('post_max_size', '800M');
        ini_set('max_file_uploads', '60');
        ini_set('max_execution_time', '1200');
        ini_set('max_input_time', '1200');
        ini_set('memory_limit', '2048M');
        ini_set('max_input_vars', '8000');

        $validationRules = [
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|size:16|unique:cotas,nik',
            'telepon' => 'required|string|max:20',
            'email' => 'required|email|unique:cotas,email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'kabupaten_kota' => 'required|string',
            'pilihan_anak' => 'required|in:dari_panti,luar_panti',
            'ktp' => 'required|file|mimes:pdf,jpg,jpeg,png|max:20480',
            'kartu_keluarga' => 'required|file|mimes:pdf,jpg,jpeg,png|max:20480',
            'surat_pernikahan' => 'required|file|mimes:pdf,jpg,jpeg,png|max:20480',
            'slip_gaji' => 'required|file|mimes:pdf,jpg,jpeg,png|max:20480',
            'surat_keterangan_sehat' => 'required|file|mimes:pdf,jpg,jpeg,png|max:20480',
        ];

        // Tambahkan validasi dinamis untuk dokumen persyaratan (27 dokumen sesuai spesifikasi, opsional, max 12MB per file)
        $dokumenPersyaratanList = [
            'rekomendasi_dinsos', 'permohonan_pemohon', 'surat_sehat_rs', 'surat_kesehatan_jiwa',
            'surat_fungsi_reproduksi', 'akta_kelahiran_cota', 'surat_catatan_kepolisian', 'akta_nikah_cota',
            'kk_ktp_cota', 'akta_kelahiran_caa', 'surat_penghasilan_cota', 'surat_persetujuan_caa',
            'surat_motivasi_cota', 'surat_persetujuan_keluarga', 'surat_domisili', 'surat_keterangan_sehat',
            'surat_tidak_dihukum', 'surat_tidak_narkoba', 'surat_pernyataan_cota', 'surat_pernyataan_caa',
            'surat_pernyataan_keluarga', 'surat_pernyataan_tidak_menelantarkan', 'surat_pernyataan_tidak_memisahkan',
            'surat_pernyataan_tidak_mengeksploitasi', 'surat_pernyataan_tidak_memperjualbelikan',
            'surat_pernyataan_tidak_mengubah_identitas', 'surat_pernyataan_tidak_mengubah_agama',
            'surat_pernyataan_tidak_mengubah_kewarganegaraan', 'surat_pernyataan_tidak_mengubah_nama',
        ];
        foreach ($dokumenPersyaratanList as $field) {
            if ($request->hasFile($field)) {
                $validationRules[$field] = 'file|mimes:pdf,jpg,jpeg,png|max:20480';
            }
        }

        // Add child selection validation if from panti
        if ($request->pilihan_anak === 'dari_panti') {
            $validationRules['panti_id'] = 'required|exists:pantis,id';
            $validationRules['anak_id'] = 'required|exists:anaks,id';
        }

        $request->validate($validationRules);

        // Upload files utama
        $ktpPath = $request->file('ktp')->store('cota/documents', 'public');
        $kartuKeluargaPath = $request->file('kartu_keluarga')->store('cota/documents', 'public');
        $suratPernikahanPath = $request->file('surat_pernikahan')->store('cota/documents', 'public');
        $slipGajiPath = $request->file('slip_gaji')->store('cota/documents', 'public');
        $suratKesehatanPath = $request->file('surat_keterangan_sehat')->store('cota/documents', 'public');

        // Create User account first
        $user = User::create([
            'name' => $request->nama,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => User::ROLE_COTA,
        ]);

        // Create COTA record
        $cota = Cota::create([
            'user_id' => $user->id,
            'nama' => $request->nama,
            'nik' => $request->nik,
            'telepon' => $request->telepon,
            'email' => $request->email,
            'kabupaten_kota' => $request->kabupaten_kota,
            'ktp_path' => $ktpPath,
            'kartu_keluarga_path' => $kartuKeluargaPath,
            'surat_pernikahan_path' => $suratPernikahanPath,
            'slip_gaji_path' => $slipGajiPath,
            'surat_keterangan_sehat_path' => $suratKesehatanPath,
            'status' => 'terdaftar', // Status terdaftar
            'tanggal_verifikasi' => now(),
            'verified_by' => auth()->id(),
        ]);

        // Upload dokumen persyaratan lain (27 dokumen sesuai spesifikasi, opsional)
        foreach ($dokumenPersyaratanList as $field) {
            if ($request->hasFile($field)) {
                $file = $request->file($field);
                $path = $file->store('cota/dokumen_persyaratan', 'public');
                $jenis = $file->getClientOriginalExtension();
                CotaDokumenPersyaratan::create([
                    'cota_id' => $cota->id,
                    'nama_dokumen' => $field,
                    'file_path' => $path,
                    'jenis_berkas' => $jenis,
                ]);
            }
        }

        // Create entry in CalonOrangTuaAsuh for Dinsos Provinsi Riau to review
        $cotaData = [
            'cota_id' => $cota->id,
            'nama_lengkap' => $request->nama,
            'nik' => $request->nik,
            'kabupaten_kota' => $request->kabupaten_kota,
            'alamat' => $request->kabupaten_kota,
            'telepon' => $request->telepon,
            'email' => $request->email,
            'status' => 'Diproses', // Default status for review
            'file_ktp' => $ktpPath,
            'file_kk' => $kartuKeluargaPath,
            'file_surat_pernikahan' => $suratPernikahanPath,
            'file_slip_gaji' => $slipGajiPath,
            'file_surat_keterangan_sehat' => $suratKesehatanPath,
        ];

        // Add anak_id if child from panti is selected
        if ($request->pilihan_anak === 'dari_panti' && $request->anak_id) {
            $cotaData['anak_id'] = $request->anak_id;
        }

        \App\Models\CalonOrangTuaAsuh::create($cotaData);

        // If child from panti is selected, update child status
        if ($request->pilihan_anak === 'dari_panti' && $request->anak_id) {
            $anak = Anak::find($request->anak_id);
            if ($anak) {
                $anak->update([
                    'status_anak' => 'Proses Pengangkatan',
                ]);
            }
        }

        return redirect()->back()->with('success', "Akun COTA atas nama {$request->nama} berhasil didaftarkan! Akun dapat langsung digunakan untuk login dengan email: {$request->email}");
    }

    public function destroyCota($id)
    {
        $cota = Cota::findOrFail($id);
        $user = $cota->user;
        
        // Delete COTA record
        $cota->delete();
        
        // Delete associated user account
        if ($user) {
            $user->delete();
        }
        
        return redirect()->back()->with('success', 'Data COTA berhasil dihapus.');
    }

    public function editCota($id)
    {
        $cota = Cota::findOrFail($id);
        
        return Inertia::render('DinsosKota/EditCota', [
            'user' => auth()->user(),
            'cota' => $cota,
        ]);
    }

    public function showCota($id)
    {
        $cota = Cota::with(['verifiedBy', 'dokumenPersyaratan'])->findOrFail($id);

        $cotaData = [
            'id' => $cota->id,
            'nama' => $cota->nama,
            'nik' => $cota->nik,
            'email' => $cota->email,
            'telepon' => $cota->telepon,
            'alamat' => $cota->kabupaten_kota,
            'status' => $cota->status_label,
            'tanggal_daftar' => $cota->created_at->format('Y-m-d'),
            'verified_by' => $cota->verifiedBy ? $cota->verifiedBy->name : null,
        ];

        // Tambahkan dokumen persyaratan ke data COTA
        foreach ($cota->dokumenPersyaratan as $dokumen) {
            $cotaData[$dokumen->nama_dokumen] = $dokumen->file_path;
        }

        return response()->json($cotaData);
    }

    public function updateCota(Request $request, $id)
    {
        // Pastikan limit upload file PHP cukup besar untuk 27 dokumen COTA
        ini_set('upload_max_filesize', '20M');
        ini_set('post_max_size', '800M');
        ini_set('max_file_uploads', '60');
        ini_set('max_execution_time', '1200');
        ini_set('max_input_time', '1200');
        ini_set('memory_limit', '2048M');
        ini_set('max_input_vars', '8000');

        $cota = Cota::findOrFail($id);

        // Daftar dokumen persyaratan (27 dokumen sesuai spesifikasi)
        $dokumenPersyaratanList = [
            'rekomendasi_dinsos', 'permohonan_pemohon', 'surat_sehat_rs', 'surat_kesehatan_jiwa',
            'surat_fungsi_reproduksi', 'akta_kelahiran_cota', 'surat_catatan_kepolisian', 'akta_nikah_cota',
            'kk_ktp_cota', 'akta_kelahiran_caa', 'surat_penghasilan_cota', 'surat_persetujuan_caa',
            'surat_motivasi_cota', 'surat_persetujuan_keluarga', 'surat_domisili', 'surat_keterangan_sehat',
            'surat_tidak_dihukum', 'surat_tidak_narkoba', 'surat_pernyataan_cota', 'surat_pernyataan_caa',
            'surat_pernyataan_keluarga', 'surat_pernyataan_tidak_menelantarkan', 'surat_pernyataan_tidak_memisahkan',
            'surat_pernyataan_tidak_mengeksploitasi', 'surat_pernyataan_tidak_memperjualbelikan',
            'surat_pernyataan_tidak_mengubah_identitas', 'surat_pernyataan_tidak_mengubah_agama',
            'surat_pernyataan_tidak_mengubah_kewarganegaraan', 'surat_pernyataan_tidak_mengubah_nama',
        ];

        $validationRules = [
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|size:16|unique:cotas,nik,' . $cota->id,
            'telepon' => 'required|string|max:15',
            'email' => 'required|email|unique:users,email,' . $cota->user_id,
            'kabupaten_kota' => 'required|string|max:255',
        ];

        // Tambahkan validasi untuk dokumen persyaratan (opsional, max 20MB per file)
        foreach ($dokumenPersyaratanList as $field) {
            if ($request->hasFile($field)) {
                $validationRules[$field] = 'file|mimes:pdf,jpg,jpeg,png|max:20480';
            }
        }

        $request->validate($validationRules);

        // Update COTA data
        $cota->update([
            'nama' => $request->nama,
            'nik' => $request->nik,
            'telepon' => $request->telepon,
            'kabupaten_kota' => $request->kabupaten_kota,
        ]);

        // Update associated user data
        if ($cota->user) {
            $cota->user->update([
                'name' => $request->nama,
                'email' => $request->email,
            ]);
        }

        // Update dokumen persyaratan jika ada file baru yang diupload
        foreach ($dokumenPersyaratanList as $field) {
            if ($request->hasFile($field)) {
                $file = $request->file($field);
                $path = $file->store('cota/dokumen_persyaratan', 'public');
                $jenis = $file->getClientOriginalExtension();

                // Update atau create dokumen persyaratan
                CotaDokumenPersyaratan::updateOrCreate(
                    [
                        'cota_id' => $cota->id,
                        'nama_dokumen' => $field,
                    ],
                    [
                        'file_path' => $path,
                        'jenis_berkas' => $jenis,
                    ]
                );
            }
        }

        return redirect()->back()->with('success', "Data COTA atas nama {$request->nama} berhasil diperbarui!");
    }

    public function updateCotaStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|string|in:tersertifikasi,ditolak',
            'catatan' => 'nullable|string|max:1000'
        ]);

        $cota = Cota::findOrFail($id);
        
        $cota->update([
            'status' => $request->status,
            'catatan' => $request->catatan
        ]);

        $statusText = $request->status === 'tersertifikasi' ? 'tersertifikasi' : 'ditolak';
        
        return redirect()->back()->with('success', "Status COTA atas nama {$cota->nama} berhasil diubah menjadi {$statusText}!");
    }
}
