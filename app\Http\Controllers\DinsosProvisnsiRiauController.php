<?php

namespace App\Http\Controllers;

use App\Models\CalonOrangTuaAsuh;
use App\Models\DokumenPersyaratanPengangkatan;
use App\Models\JadwalEvaluasi;
use App\Models\JadwalSidang;
use App\Models\DokumenVerifikasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class DinsosProvisnsiRiauController extends Controller
{
    // Menampilkan daftar calon orang tua asuh untuk halaman /dinsosriau/lks/cota/data-calon
    public function lksCotaDataCalon()
    {
        $calonList = CalonOrangTuaAsuh::with(['cota.dokumenPersyaratan'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Transform data untuk menambahkan dokumen persyaratan ke setiap calon
        $transformedData = $calonList->getCollection()->map(function ($calon) {
            $calonArray = $calon->toArray();

            // Jika ada relasi cota dan dokumen persyaratan
            if ($calon->cota && $calon->cota->dokumenPersyaratan) {
                foreach ($calon->cota->dokumenPersyaratan as $dokumen) {
                    $calonArray[$dokumen->nama_dokumen] = $dokumen->file_path;
                }
            }

            return $calonArray;
        });

        return Inertia::render('DinsosRiau/LKS/COTA/DataCalon', [
            'user' => auth()->user(),
            'calonOrangTuaAsuh' => [
                'data' => $transformedData,
                'current_page' => $calonList->currentPage(),
                'last_page' => $calonList->lastPage(),
                'per_page' => $calonList->perPage(),
                'total' => $calonList->total(),
                'from' => $calonList->firstItem() ?? 0,
                'to' => $calonList->lastItem() ?? 0,
            ]
        ]);
    }
    // --- PATCH: Jadwal Kunjungan Panti API ---
    // API: Get all jadwal kunjungan panti
    public function getJadwalKunjunganPanti(Request $request)
    {
        $pantiId = $request->query('panti_id');
        $query = \App\Models\JadwalKunjungan::with(['panti', 'user'])->orderBy('tanggal_kunjungan', 'desc');
        if ($pantiId) {
            $query->where('panti_id', $pantiId);
        }
        $jadwalList = $query->get();
        // Mapping agar field lengkap untuk frontend
        $data = $jadwalList->map(function($jadwal) {
            return [
                'id' => $jadwal->id,
                'panti_id' => $jadwal->panti_id,
                'panti' => $jadwal->panti,
                'tanggal_kunjungan' => $jadwal->tanggal_kunjungan,
                'waktu_kunjungan' => $jadwal->waktu_kunjungan ?? '',
                'tim_anggota_1' => $jadwal->tim_anggota_1 ?? '',
                'tim_anggota_2' => $jadwal->tim_anggota_2 ?? '',
                'tim_anggota_3' => $jadwal->tim_anggota_3 ?? '',
                'surat_perjalanan_dinas' => $jadwal->surat_perjalanan_dinas ?? '',
                'keterangan' => $jadwal->keterangan ?? '',
                'status' => $jadwal->status ?? 'scheduled',
                'created_at' => $jadwal->created_at,
                'updated_at' => $jadwal->updated_at,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    // API: Store new jadwal kunjungan panti
    public function storeJadwalKunjunganPanti(Request $request)
    {
        $request->validate([
            'panti_id' => 'required|exists:pantis,id',
            'tanggal_kunjungan' => 'required|date',
            'waktu_kunjungan' => 'nullable|string',
            'tim_anggota_1' => 'nullable|string',
            'tim_anggota_2' => 'nullable|string',
            'tim_anggota_3' => 'nullable|string',
            'surat_perjalanan_dinas' => 'nullable|string',
            'keterangan' => 'nullable|string|max:255',
        ]);
        $jadwal = new \App\Models\JadwalKunjungan();
        $jadwal->panti_id = $request->input('panti_id');
        $jadwal->tanggal_kunjungan = $request->input('tanggal_kunjungan');
        $jadwal->waktu_kunjungan = $request->input('waktu_kunjungan');
        $jadwal->tim_anggota_1 = $request->input('tim_anggota_1');
        $jadwal->tim_anggota_2 = $request->input('tim_anggota_2');
        $jadwal->tim_anggota_3 = $request->input('tim_anggota_3');
        $jadwal->surat_perjalanan_dinas = $request->input('surat_perjalanan_dinas');
        $jadwal->keterangan = $request->input('keterangan');
        $jadwal->status = 'scheduled';
        $jadwal->created_by = auth()->id();
        $jadwal->save();
        return response()->json([
            'success' => true,
            'data' => $jadwal
        ]);
    }
    // API: Get COTA with status 'terdaftar' (for card 'Diterima')
    public function getCotaTersertifikasiList()
    {
        $cotaList = \App\Models\Cota::where('status', 'terdaftar')->orderBy('created_at', 'desc')->get();
        return response()->json([
            'success' => true,
            'data' => $cotaList
        ]);
    }

    // API: Get COTA with status 'proses_verifikasi' (for card 'Proses')
    public function getCotaProsesVerifikasiList()
    {
        $cotaList = \App\Models\Cota::where('status', 'proses_verifikasi')->orderBy('created_at', 'desc')->get();
        return response()->json([
            'success' => true,
            'data' => $cotaList
        ]);
    }
    // Hapus jadwal sidang
    public function lksCotaJadwalSidangDestroy($id)
    {
        $jadwal = \App\Models\JadwalSidang::findOrFail($id);
        $jadwal->delete();
        return redirect()->back()->with('success', 'Jadwal sidang berhasil dihapus.');
    }
    // Endpoint untuk mengambil semua jadwal evaluasi (untuk tab "Lihat Jadwal Evaluasi")
    public function getJadwalEvaluasiList()
    {
        $jadwalList = JadwalEvaluasi::orderBy('tanggal_evaluasi', 'desc')->get();
        return response()->json([
            'success' => true,
            'data' => $jadwalList
        ]);
    }
    public function dashboard()
    {
        // Filter hanya panti yang berasal dari user dengan role panti_asuhan

        // Ambil data panti yang statusnya approved/diverifikasi (terdaftar resmi)
        // Ambil data panti yang benar-benar tampil di daftar panti terdaftar (halaman daftar)
        $pantiList = \App\Models\Panti::with('user')
            ->whereNotNull('kabupaten')
            ->whereHas('user', function ($query) {
                $query->where('role', 'panti_asuhan');
            })
            ->whereIn('status', ['approved', 'diverifikasi'])
            ->orderBy('created_at', 'desc')
            ->get();

        $pantiPersebaranData = $pantiList->groupBy('kabupaten')->map(function ($group, $kabupaten) {
            return [
                'kabupaten' => $kabupaten,
                'jumlah' => $group->count()
            ];
        })->values();

        $pantiPerTahunData = $pantiList->groupBy(function ($item) {
            return date('Y', strtotime($item->created_at));
        })->map(function ($group, $tahun) {
            return [
                'tahun' => (string) $tahun,
                'jumlah' => $group->count()
            ];
        })->values();

        $pantiLocations = $pantiList->map(function ($panti, $index) {
            $koordinat = [
                'Pekanbaru' => ['lat' => 0.5074, 'lng' => 101.4428],
                'Dumai' => ['lat' => 1.6667, 'lng' => 101.4333],
                'Kampar' => ['lat' => 0.3214, 'lng' => 101.1497],
                'Rokan Hulu' => ['lat' => 0.8547, 'lng' => 100.4681],
                'Rokan Hilir' => ['lat' => 2.0889, 'lng' => 100.8689],
                'Siak' => ['lat' => 1.1167, 'lng' => 101.3167],
                'Pelalawan' => ['lat' => -0.3667, 'lng' => 101.8333],
                'Bengkalis' => ['lat' => 1.4697, 'lng' => 102.1564],
                'Indragiri Hulu' => ['lat' => -0.6167, 'lng' => 102.6000],
                'Indragiri Hilir' => ['lat' => -0.4167, 'lng' => 103.2500],
                'Kuantan Singingi' => ['lat' => -0.4833, 'lng' => 101.4667],
                'Kepulauan Meranti' => ['lat' => 1.0000, 'lng' => 103.0000],
            ];
            $baseCoord = $koordinat[$panti->kabupaten] ?? ['lat' => 0.5074, 'lng' => 101.4428];
            $latOffset = (($index % 10) - 5) * 0.01;
            $lngOffset = (($index % 8) - 4) * 0.01;
            $jumlahAnak = \App\Models\Anak::where('user_id', $panti->user_id)->count();
            return [
                'id' => $panti->id,
                'nama' => $panti->nama,
                'alamat' => $panti->alamat,
                'kabupaten' => $panti->kabupaten,
                'lat' => $baseCoord['lat'] + $latOffset,
                'lng' => $baseCoord['lng'] + $lngOffset,
                'jumlahAnak' => $jumlahAnak,
            ];
        });

        $cotaPendaftarData = \App\Models\CalonOrangTuaAsuh::select(
            DB::raw('YEAR(created_at) as tahun'),
            DB::raw('count(*) as jumlah')
        )
        ->groupBy('tahun')
        ->orderBy('tahun')
        ->get()
        ->map(function ($item) {
            return [
                'tahun' => (string) $item->tahun,
                'jumlah' => $item->jumlah
            ];
        });

        // ...existing code...

        return Inertia::render('DinsosRiau/Dashboard', [
            'user' => auth()->user(),
            'modules' => [
                'panti_asuhan' => auth()->user()->canAccessModule('panti'),
                'cota' => auth()->user()->canAccessModule('cota'),
                'bantuan_sosial' => auth()->user()->canAccessModule('bantuan_sosial'),
            ],
            'dashboardData' => [
                'pantiPersebaranData' => $pantiPersebaranData,
                'cotaPendaftarData' => $cotaPendaftarData,
                'pantiPerTahunData' => $pantiPerTahunData,
                'pantiLocations' => $pantiLocations,
            ]
        ]);
    }

    public function lksPanti()
    {
        return Inertia::render('DinsosRiau/LKS/Panti', [
            'user' => auth()->user(),
        ]);
    }
    
    // Panti Sub-routes methods
    public function lksPantiDaftar()
    {
        $pantiList = \App\Models\Panti::with(['documents', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Panti/Daftar', [
            'user' => auth()->user(),
            'panti' => $pantiList->items(),
            'pagination' => [
                'current_page' => $pantiList->currentPage(),
                'last_page' => $pantiList->lastPage(),
                'per_page' => $pantiList->perPage(),
                'total' => $pantiList->total(),
                'from' => $pantiList->firstItem() ?? 0,
                'to' => $pantiList->lastItem() ?? 0,
            ]
        ]);
    }

    public function lksPantiUpdateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|string|in:pending,diproses,diverifikasi,ditolak',
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        $panti = \App\Models\Panti::findOrFail($id);
        
        $panti->update([
            'status' => $request->status,
            'admin_notes' => $request->admin_notes
        ]);

        return redirect()->back()->with('success', 'Status panti berhasil diperbarui.');
    }
    
    public function lksPantiJadwal()
    {
        $pantiList = \App\Models\Panti::orderBy('nama', 'asc')->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Panti/Jadwal', [
            'user' => auth()->user(),
            'panti' => $pantiList->items(),
            'pagination' => [
                'current_page' => $pantiList->currentPage(),
                'last_page' => $pantiList->lastPage(),
                'per_page' => $pantiList->perPage(),
                'total' => $pantiList->total(),
                'from' => $pantiList->firstItem() ?? 0,
                'to' => $pantiList->lastItem() ?? 0,
            ]
        ]);
    }
    
    public function lksPantiVerif()
    {
        $pengajuanList = \App\Models\PengajuanDana::with(['user', 'panti'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Panti/Verif', [
            'user' => auth()->user(),
            'pengajuan' => $pengajuanList->items(),
            'pagination' => [
                'current_page' => $pengajuanList->currentPage(),
                'last_page' => $pengajuanList->lastPage(),
                'per_page' => $pengajuanList->perPage(),
                'total' => $pengajuanList->total(),
                'from' => $pengajuanList->firstItem() ?? 0,
                'to' => $pengajuanList->lastItem() ?? 0,
            ]
        ]);
    }
    
    public function lksPantiLaporan()
    {
        $laporanList = \App\Models\LaporanKegiatan::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Panti/Laporan', [
            'user' => auth()->user(),
            'laporan' => $laporanList->items(),
            'pagination' => [
                'current_page' => $laporanList->currentPage(),
                'last_page' => $laporanList->lastPage(),
                'per_page' => $laporanList->perPage(),
                'total' => $laporanList->total(),
                'from' => $laporanList->firstItem() ?? 0,
                'to' => $laporanList->lastItem() ?? 0,
            ]
        ]);
    }
    
    public function lksBansos()
    {
        // Hitung jumlah pengajuan bansos yang belum diverifikasi (status = 'diproses' atau 'pending')
        $jumlahBelumVerifikasi = \App\Models\PengajuanBansos::whereIn('status', ['diproses', 'pending'])->count();
        return Inertia::render('DinsosRiau/LKS/Bansos', [
            'user' => auth()->user(),
            'jumlahBelumVerifikasi' => $jumlahBelumVerifikasi,
        ]);
    }
    
    public function lksBansosData()
    {
        // Get bansos application data with pagination
        $pengajuanList = \App\Models\PengajuanBansos::with(['user', 'dokumenVerifikasis'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Bansos/Data', [
            'user' => auth()->user(),
            'pengajuan' => $pengajuanList->items(),
            'pagination' => [
                'current_page' => $pengajuanList->currentPage(),
                'last_page' => $pengajuanList->lastPage(),
                'per_page' => $pengajuanList->perPage(),
                'total' => $pengajuanList->total(),
                'from' => $pengajuanList->firstItem() ?? 0,
                'to' => $pengajuanList->lastItem() ?? 0,
            ]
        ]);
    }
    
    public function lksBansosPencairan()
    {
        // Get approved bansos applications for disbursement
        $pencairanList = \App\Models\PengajuanBansos::with(['user', 'filePencairans'])
            ->where('status', 'diterima')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Map agar setiap item ada file_uploaded dan file_pencairan (ambil file terbaru jika ada)
        $pencairanData = collect($pencairanList->items())->map(function ($item) {
            $latestFile = $item->filePencairans->sortByDesc('created_at')->first();
            return [
                'id' => $item->id,
                'nama_panti' => $item->nama_panti,
                'nama_ketua' => $item->nama_ketua,
                'kabupaten_kota' => $item->kabupaten_kota,
                'no_hp' => $item->no_hp,
                'tanggal_pengajuan' => $item->tanggal_pengajuan,
                'status' => $item->status,
                'user' => $item->user,
                'created_at' => $item->created_at,
                'file_uploaded' => $latestFile ? true : false,
                'file_pencairan' => $latestFile ? [
                    'id' => $latestFile->id,
                    'file_path' => $latestFile->file_path,
                    'nama_file' => $latestFile->nama_file,
                    'created_at' => $latestFile->created_at,
                    'url' => asset('storage/' . $latestFile->file_path),
                ] : null,
            ];
        });

        return Inertia::render('DinsosRiau/LKS/Bansos/Pencairan', [
            'user' => auth()->user(),
            'pencairan' => $pencairanData,
            'pagination' => [
                'current_page' => $pencairanList->currentPage(),
                'last_page' => $pencairanList->lastPage(),
                'per_page' => $pencairanList->perPage(),
                'total' => $pencairanList->total(),
                'from' => $pencairanList->firstItem() ?? 0,
                'to' => $pencairanList->lastItem() ?? 0,
            ]
        ]);
    }
    
    public function lksBansosPertanggungjawaban()
    {
        // Get accountability reports
        $laporanList = \App\Models\LaporanBansos::orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Bansos/Pertanggungjawaban', [
            'user' => auth()->user(),
            'laporan' => $laporanList->items(),
            'pagination' => [
                'current_page' => $laporanList->currentPage(),
                'last_page' => $laporanList->lastPage(),
                'per_page' => $laporanList->perPage(),
                'total' => $laporanList->total(),
                'from' => $laporanList->firstItem() ?? 0,
                'to' => $laporanList->lastItem() ?? 0,
            ]
        ]);
    }
    
    public function lksCota()
    {
        // Get total calon orang tua asuh dari Dinsos Provinsi (CalonOrangTuaAsuh) dan Dinsos Kabupaten/Kota (Cota)
        $totalCalonOrangTuaAsuhProv = CalonOrangTuaAsuh::count();
        $totalCalonOrangTuaAsuhKota = \App\Models\Cota::count();
        $totalCalonOrangTuaAsuh = $totalCalonOrangTuaAsuhProv + $totalCalonOrangTuaAsuhKota;
        
        // Get status breakdown
        $statusBreakdown = CalonOrangTuaAsuh::select('status', DB::raw('count(*) as count'))
                                            ->groupBy('status')
                                            ->get()
                                            ->pluck('count', 'status')
                                            ->toArray();
        
        // Get active jadwal sidang (upcoming and ongoing)
        $jadwalSidangAktif = JadwalSidang::where('tanggal_sidang', '>=', now()->toDateString())
                                        ->whereIn('status', ['final', 'aktif', 'ongoing'])
                                        ->count();
                                        
        // Get upcoming jadwal sidang for today and next 7 days
        $upcomingJadwal = JadwalSidang::whereBetween('tanggal_sidang', [
                                            now()->toDateString(),
                                            now()->addDays(7)->toDateString()
                                        ])
                                        ->whereIn('status', ['final', 'aktif', 'ongoing'])
                                        ->count();

        return Inertia::render('DinsosRiau/LKS/COTA', [
            'user' => auth()->user(),
            'cotaStats' => [
                'totalCalonOrangTuaAsuh' => $totalCalonOrangTuaAsuh,
            ],
        ]);
    }


    public function lksCotaDataCalonShow($id)
    {
        $calonOrangTuaAsuh = CalonOrangTuaAsuh::with(['cota.dokumenPersyaratan'])
            ->findOrFail($id);

        // Transform data untuk menambahkan dokumen persyaratan
        $calonArray = $calonOrangTuaAsuh->toArray();

        // Jika ada relasi cota dan dokumen persyaratan
        if ($calonOrangTuaAsuh->cota && $calonOrangTuaAsuh->cota->dokumenPersyaratan) {
            foreach ($calonOrangTuaAsuh->cota->dokumenPersyaratan as $dokumen) {
                $calonArray[$dokumen->nama_dokumen] = $dokumen->file_path;
            }
        }

        return Inertia::render('DinsosRiau/LKS/COTA/DataCalonDetail', [
            'user' => auth()->user(),
            'calonOrangTuaAsuh' => $calonArray,
        ]);
    }

    public function lksCotaDataCalonUpdateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:Diproses,Diterima,Ditolak'
        ]);

        $calonOrangTuaAsuh = CalonOrangTuaAsuh::findOrFail($id);
        $calonOrangTuaAsuh->update([
            'status' => $request->status
        ]);

        // Update corresponding COTA status if exists
        if ($calonOrangTuaAsuh->cota) {
            $cotaStatus = match($request->status) {
                'Diproses' => 'proses_verifikasi',
                'Diterima' => 'tersertifikasi',
                'Ditolak' => 'ditolak',
                default => 'terdaftar'
            };
            
            $calonOrangTuaAsuh->cota->update([
                'status' => $cotaStatus,
                'verified_by' => auth()->id(),
                'tanggal_verifikasi' => now(),
            ]);
        }

        // Return a redirect back to the same page with success message
        return redirect()->back()->with('success', 'Status berhasil diupdate');
    }

    public function lksCotaDokumenPersyaratan(Request $request)
    {
        $dokumenPersyaratan = DokumenPersyaratanPengangkatan::where('is_active', true)
                                                              ->orderBy('created_at', 'desc')
                                                              ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/COTA/DokumenPersyaratan', [
            'user' => auth()->user(),
            'dokumenPersyaratan' => $dokumenPersyaratan->items(),
            'pagination' => [
                'current_page' => $dokumenPersyaratan->currentPage(),
                'last_page' => $dokumenPersyaratan->lastPage(),
                'per_page' => $dokumenPersyaratan->perPage(),
                'total' => $dokumenPersyaratan->total(),
                'from' => $dokumenPersyaratan->firstItem() ?? 0,
                'to' => $dokumenPersyaratan->lastItem() ?? 0,
            ]
        ]);
    }

    public function lksCotaJadwalSidang(Request $request)
    {
        $jadwalSidang = JadwalSidang::orderBy('tanggal_sidang', 'desc')
                                    ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/COTA/JadwalSidang', [
            'user' => auth()->user(),
            'jadwalSidang' => $jadwalSidang->items(),
            'pagination' => [
                'current_page' => $jadwalSidang->currentPage(),
                'last_page' => $jadwalSidang->lastPage(),
                'per_page' => $jadwalSidang->perPage(),
                'total' => $jadwalSidang->total(),
                'from' => $jadwalSidang->firstItem() ?? 0,
                'to' => $jadwalSidang->lastItem() ?? 0,
            ]
        ]);
    }

    public function lksCotaJadwalSidangStore(Request $request)
    {
        $request->validate([
            'tanggal_sidang' => 'required|date|after:today',
            'waktu_mulai' => 'required',
            'waktu_selesai' => 'required|after:waktu_mulai',
            'tempat_sidang' => 'required|string|max:255',
            'agenda' => 'nullable|string',
            'kapasitas' => 'required|integer|min:1',
            'keterangan' => 'nullable|string',
        ]);

        JadwalSidang::create($request->all());

        return redirect()->back()->with('success', 'Jadwal sidang berhasil dibuat');
    }

    // Update jadwal sidang (PATCH)
    public function lksCotaJadwalSidangUpdate(Request $request, $id)
    {
        $jadwal = JadwalSidang::findOrFail($id);
        $validated = $request->validate([
            'tanggal_sidang' => 'sometimes|date',
            'waktu_mulai' => 'sometimes',
            'waktu_selesai' => 'sometimes',
            'tempat_sidang' => 'sometimes|string|max:255',
            'agenda' => 'nullable|string',
            'kapasitas' => 'sometimes|integer|min:1',
            'keterangan' => 'nullable|string',
            'status' => 'sometimes|string',
        ]);

        $oldStatus = $jadwal->status;
        $jadwal->update($validated);

        // Jika status berubah menjadi 'selesai', trigger update status anak
        if (isset($validated['status']) && $validated['status'] === 'selesai' && $oldStatus !== 'selesai') {
            // Cari semua CalonOrangTuaAsuh yang terkait dengan jadwal sidang ini
            $calonOrangTuaAsuhs = CalonOrangTuaAsuh::where('jadwal_sidang_id', $jadwal->id)->get();

            foreach ($calonOrangTuaAsuhs as $calon) {
                if ($calon->anak) {
                    // Update status anak menjadi tidak aktif
                    $calon->anak->update([
                        'status_anak' => 'tidak aktif',
                        'alasan_tidak_aktif' => 'Proses pengangkatan telah selesai'
                    ]);
                }
            }
        }

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'data' => $jadwal]);
        }
        return redirect()->back()->with('success', 'Jadwal sidang berhasil diupdate');
    }

    // Assign jadwal sidang ke COTA
    public function assignJadwalSidangToCota(Request $request)
    {
        $request->validate([
            'jadwal_sidang_id' => 'required|exists:jadwal_sidangs,id',
            'calon_orang_tua_asuh_ids' => 'required|array',
            'calon_orang_tua_asuh_ids.*' => 'exists:calon_orang_tua_asuhs,id'
        ]);

        $jadwalSidang = JadwalSidang::findOrFail($request->jadwal_sidang_id);

        // Check kapasitas
        $currentAssigned = CalonOrangTuaAsuh::where('jadwal_sidang_id', $jadwalSidang->id)->count();
        $newAssignments = count($request->calon_orang_tua_asuh_ids);

        if (($currentAssigned + $newAssignments) > $jadwalSidang->kapasitas) {
            return back()->withErrors(['error' => 'Kapasitas jadwal sidang tidak mencukupi']);
        }

        // Assign jadwal sidang ke COTA
        foreach ($request->calon_orang_tua_asuh_ids as $cotaId) {
            $cota = CalonOrangTuaAsuh::findOrFail($cotaId);
            $cota->update(['jadwal_sidang_id' => $jadwalSidang->id]);
        }

        return redirect()->back()->with('success', 'Jadwal sidang berhasil di-assign ke COTA');
    }

    // Admin action methods
    public function updatePantiStatus(Request $request, $id)
    {
        $panti = \App\Models\Panti::findOrFail($id);
        
        $validated = $request->validate([
            'status' => 'required|in:pending,approved,rejected',
            'admin_notes' => 'nullable|string',
        ]);

        $panti->update($validated);

        return redirect()->back()->with('success', 'Status panti berhasil diperbarui');
    }

    public function updatePengajuanStatus(Request $request, $id)
    {
        try {
            $pengajuan = \App\Models\PengajuanDana::findOrFail($id);

            $validated = $request->validate([
                'status' => 'required|in:pending,diterima,ditolak',
                'catatan_admin' => 'nullable|string',
            ]);

            $validated['reviewed_by'] = auth()->user()->name;
            $validated['reviewed_at'] = now();

            // Jika status diubah menjadi diterima, set status_laporan ke belum_upload
            if ($validated['status'] === 'diterima') {
                $validated['status_laporan'] = 'belum_upload';
            }

            $pengajuan->update($validated);

            return redirect()->back()->with('success', 'Status pengajuan berhasil diperbarui');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Gagal memperbarui status: ' . $e->getMessage()]);
        }
    }

    public function updateStatusLaporan(Request $request, $id)
    {
        try {
            $pengajuan = \App\Models\PengajuanDana::findOrFail($id);

            $validated = $request->validate([
                'status_laporan' => 'required|in:menunggu_verifikasi,diterima,ditolak',
                'catatan_laporan' => 'nullable|string',
            ]);

            $pengajuan->update($validated);

            return redirect()->back()->with('success', 'Status laporan berhasil diperbarui');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Gagal memperbarui status laporan: ' . $e->getMessage()]);
        }
    }

    public function updateLaporanFeedback(Request $request, $id)
    {
        $laporan = \App\Models\LaporanKegiatan::findOrFail($id);
        
        $validated = $request->validate([
            'admin_feedback' => 'required|string',
        ]);

        $validated['reviewed_by'] = auth()->user()->name;
        $validated['reviewed_at'] = now();

        $laporan->update($validated);

        return redirect()->back()->with('success', 'Masukan berhasil disimpan');
    }
    
    public function updatePengajuanBansosStatus(Request $request, $id)
    {
        // Log untuk debugging
        \Log::info('Update status request received', [
            'id' => $id,
            'request_data' => $request->all()
        ]);

        $request->validate([
            'status' => 'required|in:pending,diproses,diterima,ditolak',
            'catatan' => 'nullable|string|max:1000',
            'dokumen_verifikasi' => 'nullable|array', // Izinkan dokumen_verifikasi
        ]);

        $pengajuan = \App\Models\PengajuanBansos::findOrFail($id);
        
        // Update status pengajuan
        $pengajuan->update([
            'status' => $request->status,
            'keterangan' => $request->catatan,
            'updated_at' => now(),
        ]);

        // Simpan status verifikasi dokumen jika ada
        if ($request->has('dokumen_verifikasi') && is_array($request->dokumen_verifikasi)) {
            // Jika frontend mengirim array ['dokumen_id' => ['status' => ..., 'nama_dokumen' => ...]], gunakan nama dari frontend
            foreach ($request->dokumen_verifikasi as $dokumenId => $value) {
                $status = is_array($value) && isset($value['status']) ? $value['status'] : $value;
                $nama = is_array($value) && isset($value['nama_dokumen']) ? $value['nama_dokumen'] : null;
                // fallback lama jika tidak ada nama dari frontend
                if (!$nama) {
                    $dokumenList = [
                        1 => 'Proposal Bantuan Sosial',
                        2 => 'RAB (Rencana Anggaran Biaya)',
                        3 => 'Surat Pengesahan Kemenkumham',
                        4 => 'Data Anak',
                        5 => 'Surat Pernyataan Tanggung Jawab',
                        6 => 'Sarana dan Prasarana',
                        7 => 'Izin Operasional',
                        8 => 'NPWP',
                        9 => 'Akta Notaris',
                        10 => 'SK Pengurus',
                        11 => 'Tanda Daftar LKS',
                        12 => 'Foto Rekening Bank',
                        13 => 'Pakta Integritas',
                        14 => 'Surat Keterangan Domisili',
                        15 => 'Foto KTP Pengurus (Ketua, Sekretaris, Bendahara)',
                        16 => 'Surat Rekomendasi Kabupaten/Kota',
                    ];
                    $nama = $dokumenList[$dokumenId] ?? 'Dokumen Lain';
                }
                \App\Models\DokumenVerifikasi::updateOrCreate(
                    [
                        'pengajuan_bansos_id' => $id,
                        'dokumen_id' => $dokumenId,
                    ],
                    [
                        'nama_dokumen' => $nama,
                        'status_verifikasi' => $status,
                    ]
                );
            }
        }

        // Log hasil update
        \Log::info('Status updated successfully', [
            'id' => $id,
            'new_status' => $request->status,
            'catatan' => $request->catatan
        ]);

        return back()->with('success', 'Status pengajuan berhasil diperbarui.');
    }
    
    public function updateLaporanBansosStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:diproses,diterima,ditolak,revisi',
            'catatan' => 'nullable|string|max:1000',
        ]);

        $laporan = \App\Models\LaporanBansos::findOrFail($id);
        
        $laporan->update([
            'status' => $request->status,
            'catatan_admin' => $request->catatan,
            'updated_at' => now(),
        ]);

        return back()->with('success', 'Status laporan berhasil diperbarui.');
    }

    public function lksBansosDataShow($id)
    {
        $pengajuan = \App\Models\PengajuanBansos::findOrFail($id);
        
        return response()->json($pengajuan);
    }

    public function lksBansosPertanggungjawabanShow($id)
    {
        $laporan = \App\Models\LaporanBansos::findOrFail($id);
        
        return \Inertia\Inertia::render('DinsosRiau/LKS/Bansos/PertanggungjawabanDetail', [
            'laporan' => $laporan
        ]);
    }

    public function uploadFilePencairan(Request $request)
    {
        $request->validate([
            'pengajuan_bansos_id' => 'required|exists:pengajuan_bansos,id',
            'file' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
            'jenis_file' => 'required|string',
            'keterangan' => 'nullable|string',
        ]);

        $file = $request->file('file');
        $filePath = $file->store('pencairan', 'public');

        \App\Models\FilePencairan::create([
            'pengajuan_bansos_id' => $request->pengajuan_bansos_id,
            'nama_file' => $file->getClientOriginalName(),
            'file_path' => $filePath,
            'jenis_file' => $request->jenis_file,
            'keterangan' => $request->keterangan,
        ]);

        return back()->with('success', 'File pencairan berhasil diupload');
    }

    public function storeJadwalEvaluasi(Request $request)
    {
        $request->validate([
            'nama_tim' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120', // 5MB max
            'tanggal_evaluasi' => 'required|date|after_or_equal:today',
            'jam_evaluasi' => 'required|date_format:H:i',
        ]);

        // Store the uploaded file
        $filePath = null;
        if ($request->hasFile('nama_tim')) {
            $file = $request->file('nama_tim');
            $filename = 'tim_evaluasi_' . time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('tim_evaluasi', $filename, 'public');
        }

        \App\Models\JadwalEvaluasi::create([
            'panti_id' => null, // Set default atau bisa diambil dari session user
            'nama_tim' => $request->file('nama_tim')?->getClientOriginalName(),
            'file_tim' => $filePath,
            'nama_evaluator' => null, // Field opsional
            'nip' => null, // Field opsional
            'bidang' => null, // Field opsional
            'tanggal_evaluasi' => $request->tanggal_evaluasi,
            'jam_evaluasi' => $request->jam_evaluasi,
            'deskripsi_kegiatan' => null, // Field opsional
            'status' => 'terjadwal'
        ]);

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => 'Jadwal evaluasi berhasil disimpan']);
        }
        return back()->with('success', 'Jadwal evaluasi berhasil disimpan');
    }

    public function updateJadwalEvaluasi(Request $request, $id)
    {
        $jadwal = JadwalEvaluasi::findOrFail($id);

        // Debug: Log request data
        \Log::info('Update Jadwal Evaluasi Request Data:', [
            'all_data' => $request->all(),
            'tanggal_evaluasi' => $request->tanggal_evaluasi,
            'jam_evaluasi' => $request->jam_evaluasi,
            'has_file' => $request->hasFile('nama_tim')
        ]);

        $request->validate([
            'nama_tim' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120', // 5MB max
            'tanggal_evaluasi' => 'required|date|after_or_equal:today',
            'jam_evaluasi' => 'required|date_format:H:i',
        ]);

        $updateData = [
            'tanggal_evaluasi' => $request->tanggal_evaluasi,
            'jam_evaluasi' => $request->jam_evaluasi,
        ];

        // Handle file upload if new file is provided
        if ($request->hasFile('nama_tim')) {
            // Delete old file if exists
            if ($jadwal->file_tim && \Storage::disk('public')->exists($jadwal->file_tim)) {
                \Storage::disk('public')->delete($jadwal->file_tim);
            }

            $file = $request->file('nama_tim');
            $filename = 'tim_evaluasi_' . time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('tim_evaluasi', $filename, 'public');

            $updateData['nama_tim'] = $file->getClientOriginalName();
            $updateData['file_tim'] = $filePath;
        }

        $jadwal->update($updateData);

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => 'Jadwal evaluasi berhasil diperbarui']);
        }
        return back()->with('success', 'Jadwal evaluasi berhasil diperbarui');
    }

    public function getPantiAnakAsuh($id)
    {
        // Cari panti berdasarkan ID
        $panti = \App\Models\Panti::findOrFail($id);
        
        // Ambil data anak asuh berdasarkan user_id dari panti
        $anakAsuhList = \App\Models\Anak::where('user_id', $panti->user_id)
            ->get()
            ->map(function ($anak) {
                $tanggalLahir = $anak->tanggal_lahir ? \Carbon\Carbon::parse((string)$anak->tanggal_lahir) : null;
                $usia = $tanggalLahir ? $tanggalLahir->diffInYears(\Carbon\Carbon::now()) : null;
                return [
                    'nama_lengkap' => $anak->nama_lengkap ?? '-',
                    'nik' => $anak->nik ?? '-',
                    'tempat_lahir' => $anak->tempat_lahir ?? '-',
                    'tanggal_lahir' => $anak->tanggal_lahir ? (\Carbon\Carbon::parse((string)$anak->tanggal_lahir)->format('d/m/Y')) : '-',
                    'usia' => $usia ?? '-',
                    'jenis_kelamin' => $anak->jenis_kelamin ?? '-',
                    'pendidikan' => $anak->pendidikan ?? '-',
                    'status_anak' => $anak->status_anak ?? '-',
                    'alasan_tidak_aktif' => $anak->alasan_tidak_aktif ?? '-',
                    'foto_anak' => $anak->foto_anak ?? null,
                    'nama_ayah' => $anak->nama_ayah ?? '-',
                    'usia_ayah' => $anak->usia_ayah ?? '-',
                    'pekerjaan_ayah' => $anak->pekerjaan_ayah ?? '-',
                    'alamat_ayah' => $anak->alamat_ayah ?? '-',
                    'nama_ibu' => $anak->nama_ibu ?? '-',
                    'usia_ibu' => $anak->usia_ibu ?? '-',
                    'pekerjaan_ibu' => $anak->pekerjaan_ibu ?? '-',
                    'alamat_ibu' => $anak->alamat_ibu ?? '-',
                    // Untuk kebutuhan tabel daftar
                    'nama' => $anak->nama_lengkap ?? '-',
                    'status' => $anak->status_anak ?? '-',
                    'asal_daerah' => $anak->tempat_lahir ?? '-',
                    'tanggal_masuk' => '-', // Field ini tidak ada di tabel
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $anakAsuhList
        ]);
    }
    // API: Get info identitas dan dokumen panti untuk modal info
    public function getPantiInfo($id)
    {
        $panti = \App\Models\Panti::with(['user'])->find($id);
        if (!$panti) {
            return response()->json(['success' => false, 'message' => 'Panti tidak ditemukan'], 404);
        }
        $documents = \App\Models\PantiDocument::where('panti_id', $panti->id)->get();
        return response()->json([
            'success' => true,
            'data' => [
                'panti' => $panti,
                'documents' => $documents
            ]
        ]);
    }
}
