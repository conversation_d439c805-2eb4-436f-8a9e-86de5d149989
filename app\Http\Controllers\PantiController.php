<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Panti;
use App\Models\PantiDocument;
use App\Models\Anak;
use App\Models\PengajuanDana;
use App\Models\LaporanKegiatan;
use App\Models\PengajuanBansos;
use App\Models\LaporanBansos;
use App\Models\JadwalEvaluasi;
use App\Models\JadwalKunjungan;
use App\Models\UserActivity;
use App\Models\FilePencairan;
use Illuminate\Support\Facades\Storage;

class PantiController extends Controller
{
    public function bansos()
    {
        // Get jadwal evaluasi yang aktif (terjadwal)
        $jadwalEvaluasi = JadwalEvaluasi::where('status', 'terjadwal')
            ->orderBy('tanggal_evaluasi', 'asc')
            ->orderBy('jam_evaluasi', 'asc')
            ->get();

        // Cek apakah panti bisa melakukan pengajuan baru
        // Panti bisa melakukan pengajuan jika:
        // 1. Belum pernah ada pengajuan, ATAU
        // 2. Tidak ada pengajuan yang masih dalam status 'Diproses'

        $userId = auth()->id();
        $canCreatePengajuan = true;

        // Cari pengajuan bansos yang masih dalam proses (belum selesai)
        $pendingPengajuan = PengajuanBansos::where('user_id', $userId)
            ->where('status', 'Diproses')
            ->exists();

        if ($pendingPengajuan) {
            // Jika masih ada pengajuan yang sedang diproses, tidak boleh buat pengajuan baru
            $canCreatePengajuan = false;
        }

        // Ambil panti_id dari user yang sedang login
        $panti = Panti::where('user_id', $userId)->first();
        $pantiId = $panti ? $panti->id : null;

        return Inertia::render('Panti/Bansos', [
            'user' => auth()->user(),
            'jadwalEvaluasi' => $jadwalEvaluasi,
            'canCreatePengajuan' => $canCreatePengajuan,
            'pantiId' => $pantiId,
        ]);
    }
    
    // Verifikasi Pengajuan Dana untuk DinsosRiau (tampilkan nama panti, bukan nama admin)
    public function verifikasiPengajuanDana(Request $request)
    {
        // Ambil semua pengajuan dana, eager load relasi user dan panti
        $pengajuans = \App\Models\PengajuanDana::with(['user', 'panti'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('DinsosRiau/LKS/Panti/Verif', [
            'user' => auth()->user(),
            'pengajuans' => $pengajuans->items(),
            'pagination' => [
                'current_page' => $pengajuans->currentPage(),
                'last_page' => $pengajuans->lastPage(),
                'per_page' => $pengajuans->perPage(),
                'total' => $pengajuans->total(),
                'from' => $pengajuans->firstItem() ?? 0,
                'to' => $pengajuans->lastItem() ?? 0,
            ]
        ]);
    }
    public function dashboard()
    {
        $userId = auth()->id();
        $isBaru = false;
        $panti = \App\Models\Panti::where('user_id', $userId)->first();
        // Cek apakah panti baru mendaftar (belum ada anak, bansos, jadwal, dsb)
        $jumlahAnak = $panti ? \App\Models\Anak::where('user_id', $userId)->count() : 0;
        $jumlahBansos = $panti ? \App\Models\PengajuanBansos::where('user_id', $userId)->count() : 0;
        // Hapus pengecekan jadwal karena tidak ada kolom user_id di jadwal_evaluasis
        if ($jumlahAnak === 0 && $jumlahBansos === 0) {
            $isBaru = true;
        }

        // Ambil data anak asuh untuk statistik
        $anakList = \App\Models\Anak::where('user_id', $userId)->get();

        // Hitung distribusi pendidikan (array of { name, jumlah })
        $pendidikanLabels = ['Belum sekolah', 'TK', 'SD', 'SMP', 'SMA'];
        $pendidikanData = [];
        foreach ($pendidikanLabels as $label) {
            $pendidikanData[] = [
                'name' => $label,
                'jumlah' => $anakList->where('pendidikan', $label)->count(),
            ];
        }

        // Hitung distribusi usia (array of { name, jumlah })
        $usiaRanges = [
            '0-5' => [0, 5],
            '6-12' => [6, 12],
            '13-15' => [13, 15],
            '16-18' => [16, 18],
            '>18' => [19, 200],
        ];
        $usiaData = [];
        foreach ($usiaRanges as $label => [$min, $max]) {
            $count = $anakList->filter(function($anak) use ($min, $max) {
                $usia = (int) $anak->usia;
                return $usia >= $min && $usia <= $max;
            })->count();
            $usiaData[] = [
                'name' => $label,
                'jumlah' => $count,
            ];
        }

        // Hitung jumlah anak aktif, proses pengangkatan, dan tidak aktif (selalu didefinisikan)
        $anak_aktif = $anakList->where('status_anak', 'aktif')->count();
        $proses_pengangkatan = $anakList->where('status_anak', 'proses pengangkatan')->count();
        $tidak_aktif = $anakList->where('status_anak', 'tidak aktif')->count();

        if ($isBaru) {
            // Kosongkan data bantuan sosial dan status bantuan sosial
            return Inertia::render('Panti/Dashboard', [
                'user' => auth()->user(),
                'modules' => [
                    'panti_asuhan' => auth()->user()->canAccessModule('panti'),
                    'bantuan_sosial' => auth()->user()->canAccessModule('bantuan_sosial'),
                ],
                'statsData' => [
                    'totalAnak' => 0,
                    'anakAktif' => 0,
                    'prosesPengangkatan' => 0,
                    'tidakAktif' => 0,
                ],
                'pendidikanData' => array_fill_keys($pendidikanLabels, 0),
                'usiaData' => [
                    '0-5' => 0,
                    '6-12' => 0,
                    '13-15' => 0,
                    '16-18' => 0,
                    '>18' => 0,
                ],
                'bansosStats' => [
                    'totalBansosThisYear' => null,
                    'totalNominalBansos' => null,
                    'bansosStatus' => null,
                    'currentYear' => date('Y'),
                ],
                'userActivities' => []
            ]);
        }

        // Statistik lama (jika bukan panti baru)
        $anak_aktif = $anakList->where('status_anak', 'aktif')->count();
        $proses_pengangkatan = $anakList->where('status_anak', 'proses pengangkatan')->count();
        $tidak_aktif = $anakList->where('status_anak', 'tidak aktif')->count();


        // Hitung total nominal bantuan sosial 2025 yang sudah diterima dan ditolak oleh dinas sosial provinsi riau untuk panti ini
        // Hitung jumlah pengajuan diterima/ditolak
        $bansosDiterimaCount = \App\Models\PengajuanDana::where('user_id', $userId)
            ->whereYear('tanggal_pengajuan', 2025)
            ->where('status', 'Diterima')
            ->count();
        $bansosDitolakCount = \App\Models\PengajuanDana::where('user_id', $userId)
            ->whereYear('tanggal_pengajuan', 2025)
            ->where('status', 'Ditolak')
            ->count();

        // Hitung total nominal bantuan sosial 2025 yang sudah diterima
        $totalNominalBansos2025 = \App\Models\PengajuanDana::where('user_id', $userId)
            ->whereYear('tanggal_pengajuan', 2025)
            ->where('status', 'Diterima')
            ->sum('total_dana');


        // Ambil riwayat aktivitas user (max 10 terbaru) dan map ke struktur frontend
        $userActivities = UserActivity::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($activity) {
                return [
                    'id' => $activity->id,
                    'title' => $this->getActivityDescription($activity),
                    'description' => $activity->description ?? $this->getActivityDescription($activity),
                    'date' => $activity->created_at,
                    'progress' => 100,
                    'status' => in_array($activity->activity_type, ['add', 'delete', 'print', 'confirm']) ? 'completed' : 'in_progress',
                    'type' => $activity->module,
                ];
            });
        return Inertia::render('Panti/Dashboard', [
            'user' => auth()->user(),
            'modules' => [
                'panti_asuhan' => auth()->user()->canAccessModule('panti'),
                'bantuan_sosial' => auth()->user()->canAccessModule('bantuan_sosial'),
            ],
            'statsData' => [
                'totalAnak' => $jumlahAnak,
                'anakAktif' => $anak_aktif,
                'prosesPengangkatan' => $proses_pengangkatan,
                'tidakAktif' => $tidak_aktif,
            ],
            'pendidikanData' => $pendidikanData,
            'usiaData' => $usiaData,
            'bansosStats' => [
                'totalBansosThisYear' => null,
                'totalNominalBansos' => $totalNominalBansos2025,
                'bansosStatus' => [
                    'Diterima' => $bansosDiterimaCount,
                    'Ditolak' => $bansosDitolakCount,
                ],
                'currentYear' => 2025,
            ],
            'userActivities' => $userActivities,
        ]);
    }

    /**
     * Redirect panti asuhan user to dashboard after login
     */
    public function redirectAfterLogin()
    {
        $user = auth()->user();
        if ($user && $user->role === 'panti') {
            // Optionally, you can refresh stats here if needed
            return redirect('/panti/dashboard');
        }
        // Default: redirect to home or other role-based page
        return redirect('/');
    }

    public function dataPanti()
    {
        $user = auth()->user();
        $panti = Panti::where('user_id', $user->id)->first();
        $documents = [];
        
        if ($panti) {
            $documents = PantiDocument::where('panti_id', $panti->id)->get()->keyBy('nama_dokumen');
        }
        
        return Inertia::render('Panti/DataPanti', [
            'user' => $user,
            'panti' => $panti,
            'documents' => $documents,
        ]);
    }

    public function dataAnak(Request $request)
    {
        $query = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])->where('user_id', auth()->id());

        // Filter status
        if ($request->filled('status') && $request->status !== 'all') {
            $query->where('status_anak', $request->status);
        }

        // Search nama
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%$search%")
                  ->orWhere('nik', 'like', "%$search%")
                  ->orWhere('nama_ayah', 'like', "%$search%")
                  ->orWhere('nama_ibu', 'like', "%$search%") ;
            });
        }

        $anakList = $query->orderBy('created_at', 'desc')->paginate(10)->withQueryString();

        return Inertia::render('Panti/DataAnak', [
            'user' => auth()->user(),
            'anaks' => $anakList->items(),
            'pagination' => [
                'current_page' => $anakList->currentPage(),
                'last_page' => $anakList->lastPage(),
                'per_page' => $anakList->perPage(),
                'total' => $anakList->total(),
                'from' => $anakList->firstItem() ?? 0,
                'to' => $anakList->lastItem() ?? 0,
            ],
            'filters' => [
                'status' => $request->status ?? 'all',
                'search' => $request->search ?? '',
            ]
        ]);
    }

    public function pendanaan()
    {
        $pengajuans = PengajuanDana::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Panti/Pendanaan', [
            'user' => auth()->user(),
            'pengajuans' => $pengajuans->items(),
            'pagination' => [
                'current_page' => $pengajuans->currentPage(),
                'last_page' => $pengajuans->lastPage(),
                'per_page' => $pengajuans->perPage(),
                'total' => $pengajuans->total(),
                'from' => $pengajuans->firstItem() ?? 0,
                'to' => $pengajuans->lastItem() ?? 0,
            ]
        ]);
    }

    public function laporanKegiatan()
    {
        $laporans = LaporanKegiatan::where('user_id', auth()->id())
            ->orderBy('tanggal_pelaksanaan', 'desc')
            ->paginate(10);

        return Inertia::render('Panti/LaporanKegiatan', [
            'user' => auth()->user(),
            'laporans' => $laporans->items(),
            'pagination' => [
                'current_page' => $laporans->currentPage(),
                'last_page' => $laporans->lastPage(),
                'per_page' => $laporans->perPage(),
                'total' => $laporans->total(),
                'from' => $laporans->firstItem() ?? 0,
                'to' => $laporans->lastItem() ?? 0,
            ]
        ]);
    }

    public function jadwalKunjungan()
    {
        // Ambil data jadwal kunjungan yang terkait dengan panti ini
        $user = auth()->user();
        $panti = Panti::where('user_id', $user->id)->first();
        $jadwalKunjungan = [];

        if ($panti) {
            $jadwalKunjungan = JadwalKunjungan::with(['panti'])
                ->where('panti_id', $panti->id)
                ->orderBy('tanggal_kunjungan', 'desc')
                ->get();
        }

        // Create pagination object (dummy for now since we're not paginating)
        $pagination = [
            'current_page' => 1,
            'per_page' => 10,
            'total' => count($jadwalKunjungan),
            'last_page' => 1,
            'from' => 1,
            'to' => count($jadwalKunjungan),
        ];

        return Inertia::render('Panti/JadwalKunjungan', [
            'user' => $user,
            'jadwalKunjungan' => $jadwalKunjungan,
            'pagination' => $pagination,
        ]);
    }

    public function confirmJadwalKunjungan($id)
    {
        try {
            $user = auth()->user();
            $panti = Panti::where('user_id', $user->id)->first();

            if (!$panti) {
                return redirect()->back()->with('error', 'Data panti tidak ditemukan');
            }

            $jadwal = JadwalKunjungan::where('id', $id)
                ->where('panti_id', $panti->id)
                ->first();

            if (!$jadwal) {
                return redirect()->back()->with('error', 'Jadwal kunjungan tidak ditemukan');
            }

            $jadwal->update(['status' => 'confirmed']);

            return redirect()->back()->with('success', 'Jadwal kunjungan berhasil dikonfirmasi');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal mengkonfirmasi jadwal kunjungan');
        }
    }

    public function rejectJadwalKunjungan($id)
    {
        try {
            $user = auth()->user();
            $panti = Panti::where('user_id', $user->id)->first();

            if (!$panti) {
                return redirect()->back()->with('error', 'Data panti tidak ditemukan');
            }

            $jadwal = JadwalKunjungan::where('id', $id)
                ->where('panti_id', $panti->id)
                ->first();

            if (!$jadwal) {
                return redirect()->back()->with('error', 'Jadwal kunjungan tidak ditemukan');
            }

            $jadwal->update(['status' => 'cancelled']);

            return redirect()->back()->with('success', 'Jadwal kunjungan berhasil ditolak');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal menolak jadwal kunjungan');
        }
    }

    public function storeIdentitas(Request $request)
    {
        $request->validate([
            'nama' => 'required',
            'alamat' => 'required',
            'kabupaten' => 'required',
            'telp' => 'required',
            'email' => 'required|email',
            'pimpinan' => 'required',
            'npwp' => 'required',
        ]);
        $user = $request->user();
        $panti = Panti::updateOrCreate(
            ['user_id' => $user->id],
            [
                'nama' => $request->nama,
                'alamat' => $request->alamat,
                'kabupaten' => $request->kabupaten,
                'telepon' => $request->telp,
                'email' => $request->email,
                'pimpinan' => $request->pimpinan,
                'npwp' => $request->npwp,
            ]
        );
        return back()->with('success', 'Identitas panti berhasil disimpan.');
    }

    public function uploadDokumen(Request $request)
    {
        $request->validate([
            'nama_dokumen' => 'required',
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240', // 10MB max
        ]);
        
        $user = $request->user();
        $panti = Panti::where('user_id', $user->id)->first();
        
        if (!$panti) {
            return back()->withErrors(['error' => 'Data panti belum tersedia. Silakan lengkapi identitas panti terlebih dahulu.']);
        }
        
        $path = $request->file('file')->store('dokumen_panti/' . $panti->id, 'public');
        
        $doc = PantiDocument::updateOrCreate(
            [
                'panti_id' => $panti->id,
                'nama_dokumen' => $request->nama_dokumen,
            ],
            [
                'file_path' => $path,
                'status' => 'lengkap',
            ]
        );
        
        return back()->with('success', 'Dokumen ' . $request->nama_dokumen . ' berhasil diupload.');
    }

    public function storeAnak(Request $request)
    {
        $request->validate([
            'nama_lengkap' => 'required|string|max:255',
            'tempat_lahir' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'nik' => 'required|string|size:16|unique:anaks,nik',
            'usia' => 'required|integer|min:0',
            'jenis_kelamin' => 'required|in:laki-laki,perempuan',
            'pendidikan' => 'required|in:Belum sekolah,TK,SD,SMP,SMA',
            'status_anak' => 'required|in:aktif,proses pengangkatan,tidak aktif',
            'alasan_tidak_aktif' => 'required_if:status_anak,tidak aktif|nullable|string',
            'foto_anak' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
            'nama_ayah' => 'nullable|string|max:255',
            'usia_ayah' => 'nullable|integer|min:0',
            'pekerjaan_ayah' => 'nullable|string|max:255',
            'alamat_ayah' => 'nullable|string',
            'nama_ibu' => 'nullable|string|max:255',
            'usia_ibu' => 'nullable|integer|min:0',
            'pekerjaan_ibu' => 'nullable|string|max:255',
            'alamat_ibu' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['user_id'] = auth()->id();

        if ($request->hasFile('foto_anak')) {
            $path = $request->file('foto_anak')->store('foto_anak', 'public');
            $data['foto_anak'] = $path;
        }

        $anak = Anak::create($data);

        // Log activity
        UserActivity::log('add', 'data_anak', "Menambah data anak: {$anak->nama_lengkap}", [
            'anak_id' => $anak->id,
            'nama_anak' => $anak->nama_lengkap
        ]);

        // Jika request expects JSON/Inertia, kembalikan response Inertia agar frontend langsung update
        if ($request->wantsJson() || $request->header('X-Inertia')) {
            $anaks = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])->where('user_id', auth()->id())->orderBy('created_at', 'desc')->get();
            // Anda bisa menyesuaikan pagination jika perlu
            $pagination = [
                'current_page' => 1,
                'per_page' => count($anaks),
                'total' => count($anaks),
                'last_page' => 1,
                'from' => 1,
                'to' => count($anaks),
            ];
            return inertia('Panti/DataAnak', [
                'user' => $request->user(),
                'anaks' => $anaks,
                'pagination' => $pagination,
                'success' => 'Data anak berhasil ditambahkan.'
            ]);
        }

        return back()->with('success', 'Data anak berhasil ditambahkan.');
    }

    public function showAnak($id)
    {
        $anak = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])
                    ->where('user_id', auth()->id())
                    ->findOrFail($id);

        return inertia('Panti/DetailAnak', [
            'user' => auth()->user(),
            'anak' => $anak
        ]);
    }

    public function editAnak($id)
    {
        $anak = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])
                    ->where('user_id', auth()->id())
                    ->findOrFail($id);

        return inertia('Panti/EditAnak', [
            'user' => auth()->user(),
            'anak' => $anak
        ]);
    }

    public function updateAnak(Request $request, $id)
    {
        $anak = Anak::where('user_id', auth()->id())->findOrFail($id);

        $request->validate([
            'nama_lengkap' => 'sometimes|required|string|max:255',
            'tempat_lahir' => 'sometimes|required|string|max:255',
            'tanggal_lahir' => 'sometimes|required|date',
            'nik' => 'sometimes|required|string|size:16|unique:anaks,nik,' . $id,
            'usia' => 'sometimes|required|integer|min:0',
            'jenis_kelamin' => 'sometimes|required|in:laki-laki,perempuan',
            'pendidikan' => 'sometimes|required|in:Belum sekolah,TK,SD,SMP,SMA',
            'status_anak' => 'sometimes|required|in:aktif,proses pengangkatan,tidak aktif',
            'alasan_tidak_aktif' => 'required_if:status_anak,tidak aktif|nullable|string',
            'foto_anak' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
            'nama_ayah' => 'nullable|string|max:255',
            'usia_ayah' => 'nullable|integer|min:0',
            'pekerjaan_ayah' => 'nullable|string|max:255',
            'alamat_ayah' => 'nullable|string',
            'nama_ibu' => 'nullable|string|max:255',
            'usia_ibu' => 'nullable|integer|min:0',
            'pekerjaan_ibu' => 'nullable|string|max:255',
            'alamat_ibu' => 'nullable|string',
        ]);

        $data = $request->all();

        if ($request->hasFile('foto_anak')) {
            // Delete old photo if exists
            if ($anak->foto_anak && Storage::disk('public')->exists($anak->foto_anak)) {
                Storage::disk('public')->delete($anak->foto_anak);
            }
            $path = $request->file('foto_anak')->store('foto_anak', 'public');
            $data['foto_anak'] = $path;
        }

        $anak->update($data);

        // Catat ke riwayat kegiatan (UserActivity)
        UserActivity::create([
            'user_id' => auth()->id(),
            'activity_type' => 'edit',
            'module' => 'anak',
            'description' => 'Mengedit data anak: ' . ($anak->nama_lengkap ?? '-') . ' (ID: ' . $anak->id . ')',
        ]);

        // Jika request expects JSON/Inertia, kembalikan response Inertia agar frontend langsung update
        if ($request->wantsJson() || $request->header('X-Inertia')) {
            $anaks = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])->where('user_id', auth()->id())->orderBy('created_at', 'desc')->get();
            // Anda bisa menyesuaikan pagination jika perlu
            $pagination = [
                'current_page' => 1,
                'per_page' => count($anaks),
                'total' => count($anaks),
                'last_page' => 1,
                'from' => 1,
                'to' => count($anaks),
            ];
            return inertia('Panti/DataAnak', [
                'user' => $request->user(),
                'anaks' => $anaks,
                'pagination' => $pagination,
                'success' => 'Data anak berhasil diperbarui.'
            ]);
        }
        // Jika request biasa, tetap redirect
        return redirect('/panti/dataanak')->with('success', 'Data anak berhasil diperbarui.');
    }

    public function destroyAnak($id)
    {
        $anak = Anak::where('user_id', auth()->id())->findOrFail($id);

        // Log activity before deletion
        UserActivity::log('delete', 'data_anak', "Menghapus data anak: {$anak->nama_lengkap}", [
            'anak_id' => $anak->id,
            'nama_anak' => $anak->nama_lengkap
        ]);

        // Delete photo if exists
        if ($anak->foto_anak && Storage::disk('public')->exists($anak->foto_anak)) {
            Storage::disk('public')->delete($anak->foto_anak);
        }

        $anak->delete();

        // Jika request expects JSON/Inertia, kembalikan response Inertia agar frontend langsung update
        if (request()->wantsJson() || request()->header('X-Inertia')) {
            $anaks = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])->where('user_id', auth()->id())->orderBy('created_at', 'desc')->get();
            // Anda bisa menyesuaikan pagination jika perlu
            $pagination = [
                'current_page' => 1,
                'per_page' => count($anaks),
                'total' => count($anaks),
                'last_page' => 1,
                'from' => 1,
                'to' => count($anaks),
            ];
            return inertia('Panti/DataAnak', [
                'user' => auth()->user(),
                'anaks' => $anaks,
                'pagination' => $pagination,
                'success' => 'Data anak berhasil dihapus.'
            ]);
        }

        return back()->with('success', 'Data anak berhasil dihapus.');
    }

    /**
     * Get real-time progress status for child adoption process
     */
    public function getProgressStatus($anakId)
    {
        $anak = Anak::with(['calonOrangTuaAsuh.jadwalSidang'])
                    ->where('user_id', auth()->id())
                    ->findOrFail($anakId);

        return response()->json([
            'tahap_pengangkatan' => $anak->tahap_pengangkatan,
            'status_anak' => $anak->status_anak,
            'updated_at' => $anak->updated_at
        ]);
    }

    public function storePengajuanDana(Request $request)
    {
        $request->validate([
            'tanggal_pengajuan' => 'required|date',
            'tujuan_penggunaan' => 'required|in:pendidikan,kesehatan,makanan pokok,renovasi,kegiatan sosial,lainnya',
            'periode_mulai' => 'required|string|regex:/^\d{2}\/\d{2}$/',
            'periode_selesai' => 'required|string|regex:/^\d{2}\/\d{2}$/',
            'total_dana' => 'required|numeric|min:0',
            'deskripsi_kebutuhan' => 'required|string|max:1000',
            'file_proposal' => 'required|file|mimes:pdf,doc,docx|max:5120',
            'file_rekening' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'file_ktp' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'file_foto_kegiatan' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'file_rab' => 'required|file|mimes:pdf,doc,docx,xls,xlsx|max:5120',
        ]);

        $data = $request->all();
        $data['user_id'] = auth()->id();

        // Upload files
        if ($request->hasFile('file_proposal')) {
            $data['file_proposal'] = $request->file('file_proposal')->store('pengajuan_dana/proposal', 'public');
        }
        if ($request->hasFile('file_rekening')) {
            $data['file_rekening'] = $request->file('file_rekening')->store('pengajuan_dana/rekening', 'public');
        }
        if ($request->hasFile('file_ktp')) {
            $data['file_ktp'] = $request->file('file_ktp')->store('pengajuan_dana/ktp', 'public');
        }
        if ($request->hasFile('file_foto_kegiatan')) {
            $data['file_foto_kegiatan'] = $request->file('file_foto_kegiatan')->store('pengajuan_dana/foto_kegiatan', 'public');
        }
        if ($request->hasFile('file_rab')) {
            $data['file_rab'] = $request->file('file_rab')->store('pengajuan_dana/rab', 'public');
        }

        $pengajuan = PengajuanDana::create($data);

        // Log activity
        UserActivity::log('add', 'pengajuan_dana', "Mengajukan dana untuk {$pengajuan->tujuan_penggunaan}", [
            'pengajuan_id' => $pengajuan->id,
            'tujuan' => $pengajuan->tujuan_penggunaan,
            'nominal' => $pengajuan->nominal_pengajuan
        ]);

        return back()->with('success', 'Pengajuan dana berhasil disubmit.');
    }

    public function showPengajuanDana($id)
    {
        $pengajuan = PengajuanDana::where('user_id', auth()->id())->findOrFail($id);
        
        return response()->json($pengajuan);
    }

    public function destroyPengajuanDana($id)
    {
        $pengajuan = PengajuanDana::where('user_id', auth()->id())->findOrFail($id);

        // Delete files if exists
        $files = ['file_proposal', 'file_rekening', 'file_ktp', 'file_foto_kegiatan', 'file_rab', 'file_laporan_penggunaan'];
        foreach ($files as $file) {
            if ($pengajuan->$file && Storage::disk('public')->exists($pengajuan->$file)) {
                Storage::disk('public')->delete($pengajuan->$file);
            }
        }

        $pengajuan->delete();

        return back()->with('success', 'Pengajuan dana berhasil dihapus.');
    }

    public function uploadLaporanPenggunaan(Request $request, $id)
    {
        try {
            $pengajuan = PengajuanDana::where('user_id', auth()->id())->findOrFail($id);

            // Validasi bahwa pengajuan sudah diterima
            if ($pengajuan->status !== 'diterima') {
                return back()->withErrors(['error' => 'Laporan hanya dapat diupload untuk pengajuan yang sudah diterima.']);
            }

            $request->validate([
                'file_laporan_penggunaan' => 'required|file|mimes:pdf,doc,docx|max:20480', // 20MB
            ]);

            // Hapus file laporan lama jika ada
            if ($pengajuan->file_laporan_penggunaan && Storage::disk('public')->exists($pengajuan->file_laporan_penggunaan)) {
                Storage::disk('public')->delete($pengajuan->file_laporan_penggunaan);
            }

            // Upload file laporan baru
            $filePath = $request->file('file_laporan_penggunaan')->store('laporan_penggunaan_dana', 'public');

            // Update data pengajuan
            $pengajuan->update([
                'file_laporan_penggunaan' => $filePath,
                'tanggal_upload_laporan' => now(),
                'status_laporan' => 'menunggu_verifikasi',
                'catatan_laporan' => null, // Reset catatan laporan
            ]);

            return back()->with('success', 'Laporan penggunaan dana berhasil diupload dan sedang menunggu verifikasi.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors());
        } catch (\Exception $e) {
            \Log::error('Error uploading laporan: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat mengupload laporan. Silakan coba lagi.']);
        }
    }

    public function storeLaporanKegiatan(Request $request)
    {
        $request->validate([
            'nama_kegiatan' => 'required|string|max:255',
            'jenis_kegiatan' => 'required|in:pendidikan dan pelatihan,kegiatan sosial,kesehatan,olahraga & rekreasi,keagamaan,seni & budaya,keterampilan,lainnya',
            'tanggal_pelaksanaan' => 'required|date',
            'waktu_mulai' => 'required|date_format:H:i',
            'waktu_selesai' => 'required|date_format:H:i|after:waktu_mulai',
            'lokasi_pelaksanaan' => 'required|string|max:255',
            'jumlah_peserta' => 'required|integer|min:1',
            'penanggung_jawab' => 'required|string|max:255',
            'deskripsi_kegiatan' => 'required|string|max:1000',
            'hasil_kegiatan' => 'required|string|max:1000',
            'kendala_tantangan' => 'nullable|string|max:1000',
            'foto_kegiatan' => 'nullable|image|mimes:jpeg,jpg,png|max:2048',
            'laporan_kegiatan' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
        ]);

        $data = $request->all();
        $data['user_id'] = auth()->id();

        // Upload files
        if ($request->hasFile('foto_kegiatan')) {
            $data['foto_kegiatan'] = $request->file('foto_kegiatan')->store('laporan_kegiatan/foto', 'public');
        }
        if ($request->hasFile('laporan_kegiatan')) {
            $data['laporan_kegiatan'] = $request->file('laporan_kegiatan')->store('laporan_kegiatan/dokumen', 'public');
        }

        $laporan = LaporanKegiatan::create($data);
        // Log aktivitas tambah laporan kegiatan
        \App\Models\UserActivity::log('add', 'laporan_kegiatan', "Menambah laporan kegiatan: {$laporan->nama_kegiatan}", [
            'laporan_id' => $laporan->id,
            'nama_kegiatan' => $laporan->nama_kegiatan
        ]);
        return back()->with('success', 'Laporan kegiatan berhasil ditambahkan.');
    }

    public function showLaporanKegiatan($id)
    {
        $laporan = LaporanKegiatan::where('user_id', auth()->id())->findOrFail($id);
        
        return response()->json($laporan);
    }

    public function destroyLaporanKegiatan($id)
    {
        $laporan = LaporanKegiatan::where('user_id', auth()->id())->findOrFail($id);

        // Delete files if exists
        if ($laporan->foto_kegiatan && Storage::disk('public')->exists($laporan->foto_kegiatan)) {
            Storage::disk('public')->delete($laporan->foto_kegiatan);
        }
        if ($laporan->laporan_kegiatan && Storage::disk('public')->exists($laporan->laporan_kegiatan)) {
            Storage::disk('public')->delete($laporan->laporan_kegiatan);
        }

        $namaKegiatan = $laporan->nama_kegiatan;
        $laporanId = $laporan->id;
        $laporan->delete();
        // Log aktivitas hapus laporan kegiatan
        \App\Models\UserActivity::log('delete', 'laporan_kegiatan', "Menghapus laporan kegiatan: {$namaKegiatan}", [
            'laporan_id' => $laporanId,
            'nama_kegiatan' => $namaKegiatan
        ]);
        return back()->with('success', 'Laporan kegiatan berhasil dihapus.');
    }

    /**
     * Log aktivitas cetak laporan kegiatan
     */
    public function logPrintLaporanKegiatan($id)
    {
        $laporan = LaporanKegiatan::where('user_id', auth()->id())->findOrFail($id);
        \App\Models\UserActivity::log('print', 'laporan_kegiatan', "Mencetak laporan kegiatan: {$laporan->nama_kegiatan}", [
            'laporan_id' => $laporan->id,
            'nama_kegiatan' => $laporan->nama_kegiatan
        ]);
        return response()->json(['success' => true]);
    }
    public function bansosCreate()
    {
        // Cek apakah panti bisa melakukan pengajuan baru
        $userId = auth()->id();
        
        // Cari pengajuan bansos yang paling baru dengan status 'Diterima'
        $latestApprovedPengajuan = PengajuanBansos::where('user_id', $userId)
            ->where('status', 'Diterima')
            ->orderBy('created_at', 'desc')
            ->first();
            
        if ($latestApprovedPengajuan) {
            // Jika ada pengajuan yang diterima, cek apakah ada file pencairan
            $hasPencairan = FilePencairan::where('pengajuan_bansos_id', $latestApprovedPengajuan->id)
                ->exists();
                
            // Jika pengajuan diterima tapi belum ada file pencairan, 
            // redirect kembali ke bansos dengan pesan error
            if (!$hasPencairan) {
                return redirect('/panti/bansos')->with('error', 'Anda tidak dapat membuat pengajuan baru sampai ada pencairan dana dari pengajuan sebelumnya.');
            }
        }

        // Ambil semua data panti yang sudah terdaftar untuk dropdown
        $pantiList = Panti::with('user')->get()->map(function ($panti) {
            return [
                'id' => $panti->id,
                'nama' => $panti->nama,
                'pimpinan' => $panti->pimpinan,
                'email' => $panti->email,
                'telepon' => $panti->telepon,
                'user_name' => $panti->user->name ?? null,
            ];
        });

        // Ambil data panti milik user yang sedang login
        $userPanti = Panti::where('user_id', auth()->id())->first();

        return Inertia::render('Panti/BansosNew/Pengajuan', [
            'user' => auth()->user(),
            'pantiList' => $pantiList,
            'userPanti' => $userPanti,
        ]);
    }

    public function bansosStore(Request $request)
    {
        $request->validate([
            'nama_panti' => 'required|string|max:255',
            'nama_ketua' => 'required|string|max:255',
            'kabupaten_kota' => 'required|string|max:255',
            'no_hp' => 'required|string|max:20',
            'tanggal_pengajuan' => 'required|date',
            'proposal' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'sk_pengurus' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'rencana_anggaran' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'akta_notaris' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'surat_pengesahan' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'tanda_daftar_lks' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'data_anak' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'sarana_prasarana' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'surat_pernyataan' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'pakta_integritas' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'npwp' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'surat_domisil' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'izin_operasional' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'foto_ktp' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'foto_rekening' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
        ]);

        $data = $request->only([
            'nama_panti', 'nama_ketua', 'kabupaten_kota', 'no_hp', 'tanggal_pengajuan'
        ]);
        $data['user_id'] = auth()->id();

        // Handle file uploads
        $fileFields = [
            'proposal', 'sk_pengurus', 'rencana_anggaran', 'akta_notaris', 'surat_pengesahan',
            'tanda_daftar_lks', 'data_anak', 'sarana_prasarana', 'surat_pernyataan',
            'pakta_integritas', 'npwp', 'surat_domisil', 'izin_operasional', 'foto_ktp', 'foto_rekening',
            'surat_rekomendasi'
        ];

        foreach ($fileFields as $field) {
            if ($request->hasFile($field)) {
                $data[$field] = $request->file($field)->store('bansos/' . $field, 'public');
            }
        }

        PengajuanBansos::create($data);

        return redirect()->route('panti.bansos')->with('success', 'Pengajuan bantuan sosial berhasil dikirim!');
    }

    public function bansosStatus(Request $request)
    {
        $query = PengajuanBansos::where('user_id', auth()->id())
                                ->orderBy('created_at', 'desc');

        // Apply status filter if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $pengajuanList = $query->paginate(5);

        return Inertia::render('Panti/BansosNew/StatusPengajuan', [
            'user' => auth()->user(),
            'pengajuanList' => $pengajuanList->items(),
            'pagination' => [
                'current_page' => $pengajuanList->currentPage(),
                'last_page' => $pengajuanList->lastPage(),
                'per_page' => $pengajuanList->perPage(),
                'total' => $pengajuanList->total(),
                'from' => $pengajuanList->firstItem() ?? 0,
                'to' => $pengajuanList->lastItem() ?? 0,
            ],
            'filters' => [
                'status' => $request->status ?? '',
            ]
        ]);
    }

    public function statusPengajuan(Request $request)
    {

        $pengajuanList = PengajuanBansos::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->with(['user', 'filePencairans', 'dokumenVerifikasis'])
            ->get();

        return Inertia::render('Panti/StatusPengajuan', [
            'user' => auth()->user(),
            'pengajuanList' => $pengajuanList,
        ]);
    }

    public function bansosEdit($id)
    {
        $pengajuan = PengajuanBansos::where('user_id', auth()->id())->findOrFail($id);
        
        return Inertia::render('Panti/BansosNew/Edit', [
            'user' => auth()->user(),
            'pengajuan' => $pengajuan
        ]);
    }

    public function bansosUpdate(Request $request, $id)
    {
        $pengajuan = PengajuanBansos::where('user_id', auth()->id())->findOrFail($id);
        
        $request->validate([
            'nama_panti' => 'required|string|max:255',
            'nama_ketua' => 'required|string|max:255',
            'kabupaten_kota' => 'required|string|max:255',
            'no_hp' => 'required|string|max:20',
            'tanggal_pengajuan' => 'required|date',
            'proposal' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'sk_pengurus' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'rencana_anggaran' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'akta_notaris' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'surat_pengesahan' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'tanda_daftar_lks' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'data_anak' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'sarana_prasarana' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'surat_pernyataan' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'pakta_integritas' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'npwp' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'surat_domisil' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'izin_operasional' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'foto_ktp' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
            'foto_rekening' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
        ]);

        $data = $request->only([
            'nama_panti', 'nama_ketua', 'kabupaten_kota', 'no_hp', 'tanggal_pengajuan'
        ]);

        // Handle file uploads
        $fileFields = [
            'proposal', 'sk_pengurus', 'rencana_anggaran', 'akta_notaris', 'surat_pengesahan',
            'tanda_daftar_lks', 'data_anak', 'sarana_prasarana', 'surat_pernyataan',
            'pakta_integritas', 'npwp', 'surat_domisil', 'izin_operasional', 'foto_ktp', 'foto_rekening'
        ];

        foreach ($fileFields as $field) {
            if ($request->hasFile($field)) {
                // Delete old file if exists
                if ($pengajuan->$field && Storage::disk('public')->exists($pengajuan->$field)) {
                    Storage::disk('public')->delete($pengajuan->$field);
                }
                $data[$field] = $request->file($field)->store('bansos/' . $field, 'public');
            }
        }

        $pengajuan->update($data);

        return redirect()->route('panti.bansos.status')->with('success', 'Pengajuan bantuan sosial berhasil diperbarui!');
    }

    // Laporan methods
    public function laporanIndex(Request $request)
    {
        $user = auth()->user();
        
        // Get laporan bansos for this user/panti
        $query = LaporanBansos::where('user_id', $user->id)
                    ->orderBy('created_at', 'desc');

        // Apply status filter if provided
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        $laporanList = $query->paginate(10);

        return Inertia::render('Panti/BansosNew/Laporan', [
            'user' => $user,
            'laporanList' => $laporanList->items(),
            'pagination' => [
                'current_page' => $laporanList->currentPage(),
                'last_page' => $laporanList->lastPage(),
                'per_page' => $laporanList->perPage(),
                'total' => $laporanList->total(),
                'from' => $laporanList->firstItem() ?? 0,
                'to' => $laporanList->lastItem() ?? 0,
            ],
            'filters' => [
                'status' => $request->status ?? '',
            ]
        ]);
    }

    public function laporanCreate()
    {
        // Get list of panti (id & nama) yang sudah disetujui Dinsos Provinsi Riau
        $pantiList = Panti::where('status', 'approved')
            ->select('id', 'nama')
            ->orderBy('nama')
            ->get()
            ->map(function($p) {
                return [
                    'id' => $p->id,
                    'nama' => $p->nama
                ];
            })
            ->values()
            ->toArray();

        // List of kabupaten/kota in Riau province
        $kabupatenKotaRiau = [
            'Kota Pekanbaru',
            'Kota Dumai',
            'Kabupaten Kampar',
            'Kabupaten Rokan Hulu',
            'Kabupaten Rokan Hilir',
            'Kabupaten Siak',
            'Kabupaten Pelalawan',
            'Kabupaten Bengkalis',
            'Kabupaten Indragiri Hulu',
            'Kabupaten Indragiri Hilir',
            'Kabupaten Kuantan Singingi',
            'Kabupaten Kepulauan Meranti'
        ];

        return Inertia::render('Panti/BansosNew/LaporanUpload', [
            'user' => auth()->user(),
            'pantiList' => $pantiList,
            'kabupatenKotaList' => $kabupatenKotaRiau,
        ]);
    }

    public function laporanStore(Request $request)
    {
        $validated = $request->validate([
            'nama_panti' => 'required|string|max:255',
            'kabupaten_kota' => 'required|string|max:255',
            'tanggal_upload' => 'required|date',
            'file_laporan' => 'required|file|mimes:pdf,doc,docx|max:10240', // 10MB max
        ]);

        // Handle file upload
        if ($request->hasFile('file_laporan')) {
            $filePath = $request->file('file_laporan')->store('laporan-bansos', 'public');
            $validated['file_laporan'] = $filePath;
        }

        $validated['status'] = 'diproses';
        $validated['user_id'] = auth()->id();

        LaporanBansos::create($validated);

        return redirect()->route('panti.bansos.laporan')
            ->with('message', 'Laporan berhasil diupload');
    }

    public function laporanEdit($id)
    {
        $laporan = LaporanBansos::findOrFail($id);
        
        return Inertia::render('Panti/BansosNew/LaporanEdit', [
            'user' => auth()->user(),
            'laporan' => $laporan
        ]);
    }

    public function laporanUpdate(Request $request, $id)
    {
        $laporan = LaporanBansos::findOrFail($id);
        
        $validated = $request->validate([
            'nama_panti' => 'required|string|max:255',
            'kabupaten_kota' => 'required|string|max:255',
            'tanggal_upload' => 'required|date',
            'file_laporan' => 'nullable|file|mimes:pdf,doc,docx|max:10240', // 10MB max
        ]);

        // Handle file upload if new file is provided
        if ($request->hasFile('file_laporan')) {
            // Delete old file if exists
            if ($laporan->file_laporan) {
                Storage::disk('public')->delete($laporan->file_laporan);
            }
            
            $filePath = $request->file('file_laporan')->store('laporan-bansos', 'public');
            $validated['file_laporan'] = $filePath;
        }

        $laporan->update($validated);

        return redirect()->route('panti.bansos.laporan')
            ->with('message', 'Laporan berhasil diperbarui');
    }

    /**
     * Helper methods for activity processing
     */
    private function getActivityDescription($activity)
    {
        $moduleNames = [
            'data_panti' => 'Data Panti',
            'data_anak' => 'Data Anak Asuh',
            'pengajuan_dana' => 'Pengajuan Dana',
            'laporan_kegiatan' => 'Laporan Kegiatan',
            'bantuan_sosial' => 'Bantuan Sosial'
        ];

        $moduleName = $moduleNames[$activity->module] ?? $activity->module;
        
        switch ($activity->activity_type) {
            case 'add':
                return "Menambah data baru di {$moduleName}";
            case 'edit':
                return "Mengubah data di {$moduleName}";
            case 'delete':
                return "Menghapus data di {$moduleName}";
            case 'view':
                return "Melihat data di {$moduleName}";
            case 'print':
                return "Mencetak laporan {$moduleName}";
            case 'confirm':
                return "Mengkonfirmasi {$moduleName}";
            default:
                return "Aktivitas di {$moduleName}";
        }
    }

    private function calculateActivityProgress($activity)
    {
        // Calculate progress based on activity type and how recent it is
        $now = now();
        $activityDate = $activity->completed_at;
        $daysDiff = $now->diffInDays($activityDate);

        // Base progress based on activity type
        $baseProgress = match($activity->activity_type) {
            'add' => 100,
            'edit' => 85,
            'delete' => 100,
            'view' => 30,
            'print' => 100,
            'confirm' => 100,
            default => 50
        };

        // Reduce progress for older activities to show "freshness"
        if ($daysDiff > 7) {
            $baseProgress = max(25, $baseProgress - ($daysDiff * 2));
        }

        return min(100, $baseProgress);
    }

    private function getActivityStatus($activity)
    {
        $completedTypes = ['add', 'delete', 'print', 'confirm'];
        
        if (in_array($activity->activity_type, $completedTypes)) {
            return 'completed';
        }
        
        return 'in_progress';
    }

    public function bansosPencairan()
    {
        $user = auth()->user();
        
        // Get pengajuan yang sudah diterima dan memiliki file pencairan
        $pengajuanList = PengajuanBansos::where('user_id', $user->id)
            ->where('status', 'diterima')
            ->with(['filePencairans'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($pengajuan) {
                return [
                    'id' => $pengajuan->id,
                    'nama_panti' => $pengajuan->nama_panti,
                    'kabupaten_kota' => $pengajuan->kabupaten_kota,
                    'tanggal_pengajuan' => $pengajuan->tanggal_pengajuan,
                    'status' => $pengajuan->status,
                    'created_at' => $pengajuan->created_at,
                    'file_pencairans' => $pengajuan->filePencairans->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'nama_file' => $file->nama_file,
                            'file_path' => $file->file_path,
                            'jenis_file' => $file->jenis_file,
                            'keterangan' => $file->keterangan,
                            'created_at' => $file->created_at,
                        ];
                    })->toArray(),
                ];
            });

        return Inertia::render('Panti/BansosNew/Pencairan', [
            'user' => $user,
            'pengajuanList' => $pengajuanList,
        ]);
    }
    // Endpoint untuk mengambil detail file pencairan berdasarkan ID (untuk modal view di frontend)
    public function showFilePencairan($id)
    {
        $file = FilePencairan::findOrFail($id);
        return response()->json([
            'id' => $file->id,
            'nama_file' => $file->nama_file,
            'file_path' => $file->file_path,
            'jenis_file' => $file->jenis_file,
            'keterangan' => $file->keterangan,
            'created_at' => $file->created_at,
            'url' => $file->file_path ? asset('storage/' . $file->file_path) : null,
        ]);
    }
    // API: Info identitas & dokumen panti untuk DinsosRiau modal info
    public function getPantiInfo($id)
    {
        $panti = \App\Models\Panti::with(['user', 'documents'])->findOrFail($id);
        $dokumenResmi = $panti->documents->map(function($doc) {
            return [
                'id' => $doc->id,
                'nama_dokumen' => $doc->nama_dokumen,
                'file_path' => $doc->file_path,
                'status' => $doc->status,
                'url' => $doc->file_path ? asset('storage/' . $doc->file_path) : null,
            ];
        });
        return response()->json([
            'identitas' => [
                'nama' => $panti->nama,
                'alamat' => $panti->alamat,
                'kabupaten' => $panti->kabupaten,
                'telepon' => $panti->telepon,
                'email' => $panti->email,
                'pimpinan' => $panti->pimpinan,
                'npwp' => $panti->npwp,
                'status' => $panti->status,
                'admin_notes' => $panti->admin_notes,
                'user' => $panti->user ? [
                    'name' => $panti->user->name,
                    'email' => $panti->user->email,
                ] : null,
            ],
            'dokumen' => $dokumenResmi,
        ]);
    }
}