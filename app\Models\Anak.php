<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Anak extends Model
{
    protected $fillable = [
        'user_id',
        'nama_lengkap',
        'tempat_lahir',
        'tanggal_lahir',
        'nik',
        'usia',
        'jenis_kelamin',
        'pendidikan',
        'status_anak',
        'alasan_tidak_aktif',
        'foto_anak',
        'nama_ayah',
        'usia_ayah',
        'pekerjaan_ayah',
        'alamat_ayah',
        'nama_ibu',
        'usia_ibu',
        'pekerjaan_ibu',
        'alamat_ibu',
    ];

    protected $casts = [
        'tanggal_lahir' => 'date',
    ];





    protected $appends = [
        'tahap_pengangkatan',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Relasi ke COTA (Calon Orang Tua Asuh) jika ada
    public function calonOrangTuaAsuh()
    {
        return $this->hasOne(\App\Models\CalonOrangTuaAsuh::class, 'anak_id');
    }

    public function getTahapPengangkatanAttribute()
    {
        if (strtolower($this->status_anak) !== 'proses pengangkatan') {
            return null;
        }

        $calon = $this->calonOrangTuaAsuh;
        if (!$calon) {
            return 'menunggu_verifikasi';
        }

        $status = strtolower($calon->status ?? '');

        // Jika status ditolak, kembali ke menunggu verifikasi
        if ($status === 'ditolak') {
            return 'menunggu_verifikasi';
        }

        // Jika status diproses, berada di tahap verifikasi berkas
        if ($status === 'diproses') {
            return 'proses_verifikasi';
        }

        // Jika status diterima, cek apakah ada jadwal sidang
        if ($status === 'diterima') {
            $jadwalSidang = $calon->jadwalSidang;

            if (!$jadwalSidang) {
                // Belum ada jadwal sidang, masih di tahap sidang
                return 'sidang';
            }

            // Cek status jadwal sidang
            $statusSidang = strtolower($jadwalSidang->status ?? '');
            if ($statusSidang === 'selesai') {
                // Otomatis ubah status anak menjadi tidak aktif jika sidang selesai
                if ($this->status_anak !== 'tidak aktif') {
                    $this->update([
                        'status_anak' => 'tidak aktif',
                        'alasan_tidak_aktif' => 'Proses pengangkatan telah selesai'
                    ]);
                }
                return 'selesai';
            } else {
                return 'sidang';
            }
        }

        return 'menunggu_verifikasi';
    }

}
