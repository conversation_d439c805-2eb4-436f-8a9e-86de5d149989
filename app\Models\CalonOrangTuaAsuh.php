<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CalonOrangTuaAsuh extends Model
{
    use HasFactory;

    protected $fillable = [
        'cota_id',
        'anak_id',
        'jadwal_sidang_id',
        'nama_lengkap',
        'nik',
        'kabupaten_kota',
        'alamat',
        'telepon',
        'email',
        'status',
        'keterangan',
        'file_ktp',
        'file_kk',
        'file_surat_pernikahan',
        'file_slip_gaji',
        'file_surat_keterangan_sehat',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'status_badge',
        'rejection_reason',
    ];

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'Diproses' => ['label' => 'Diproses', 'class' => 'bg-yellow-100 text-yellow-800'],
            'Diterima' => ['label' => 'Diterima', 'class' => 'bg-green-100 text-green-800'],
            'Ditolak' => ['label' => 'Ditolak', 'class' => 'bg-red-100 text-red-800'],
        ];

        return $badges[$this->status] ?? $badges['Diproses'];
    }

    public function getRejectionReasonAttribute()
    {
        return $this->keterangan;
    }

    public function cota()
    {
        return $this->belongsTo(Cota::class);
    }

    // Relasi ke Anak
    public function anak()
    {
        return $this->belongsTo(Anak::class, 'anak_id');
    }

    // Relasi ke JadwalSidang
    public function jadwalSidang()
    {
        return $this->belongsTo(JadwalSidang::class, 'jadwal_sidang_id');
    }
}
