<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Cota extends Model
{
    protected $fillable = [
        'user_id',
        'nama',
        'nik',
        'telepon',
        'email',
        'kabupaten_kota',
        'ktp_path',
        'kartu_keluarga_path',
        'surat_pernikahan_path',
        'slip_gaji_path',
        'surat_keterangan_sehat_path',
        'status',
        'catatan',
        'tanggal_verifikasi',
        'verified_by',
    ];

    protected $casts = [
        'tanggal_verifikasi' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function dokumenPersyaratan()
    {
        return $this->hasMany(CotaDokumenPersyaratan::class);
    }

    public function calonOrangTuaAsuh()
    {
        return $this->hasOne(CalonOrangTuaAsuh::class);
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'menunggu_verifikasi' => 'Menunggu Verifikasi',
            'proses_verifikasi' => 'Proses Verifikasi',
            'terdaftar' => 'Terdaftar',
            'tersertifikasi' => 'Tersertifikasi',
            'ditolak' => 'Ditolak',
            default => 'Unknown'
        };
    }
}
