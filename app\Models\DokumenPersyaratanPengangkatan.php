<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DokumenPersyaratanPengangkatan extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_dokumen',
        'jenis_berkas',
        'file_path',
        'deskripsi',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'file_extension',
        'file_icon',
    ];

    public function getFileExtensionAttribute()
    {
        return pathinfo($this->file_path, PATHINFO_EXTENSION);
    }

    public function getFileIconAttribute()
    {
        $icons = [
            'pdf' => 'file-text',
            'doc' => 'file-text',
            'docx' => 'file-text',
            'xls' => 'file-text',
            'xlsx' => 'file-text',
            'jpg' => 'image',
            'jpeg' => 'image',
            'png' => 'image',
            'gif' => 'image',
            'default' => 'file',
        ];

        $extension = strtolower($this->file_extension);
        return $icons[$extension] ?? $icons['default'];
    }
}
