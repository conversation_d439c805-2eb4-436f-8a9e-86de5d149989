<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JadwalKunjungan extends Model
{
    protected $table = 'jadwal_kunjungans';
    protected $fillable = [
        'panti_id',
        'tanggal_kunjungan',
        'waktu_kunjungan',
        'tim_anggota_1',
        'tim_anggota_2',
        'tim_anggota_3',
        'surat_perjalanan_dinas',
        'keterangan',
        'status',
        'created_by',
        // Tambahan field untuk kebutuhan frontend
        'deskripsi_kegiatan',
        'catatan_hasil_kunjungan',
        'upload_dokumentasi',
    ];

    public function panti()
    {
        return $this->belongsTo(Panti::class, 'panti_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
