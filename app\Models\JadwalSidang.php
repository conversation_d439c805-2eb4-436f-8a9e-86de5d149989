<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JadwalSidang extends Model
{
    use HasFactory;

    protected $fillable = [
        'tanggal_sidang',
        'waktu_mulai',
        'waktu_selesai',
        'tempat_sidang',
        'agenda',
        'status',
        'kapasitas',
        'keterangan',
    ];

    protected $casts = [
        'tanggal_sidang' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'status_badge',
    ];

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'draft' => ['label' => 'Draft', 'class' => 'bg-yellow-100 text-yellow-800'],
            'final' => ['label' => 'Final', 'class' => 'bg-blue-100 text-blue-800'],
            'selesai' => ['label' => 'Selesai', 'class' => 'bg-green-100 text-green-800'],
            'batal' => ['label' => 'Batal', 'class' => 'bg-red-100 text-red-800'],
        ];

        return $badges[$this->status] ?? $badges['draft'];
    }
}
