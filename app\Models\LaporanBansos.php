<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LaporanBansos extends Model
{
    protected $table = 'laporan_bansos';
    
    protected $fillable = [
        'user_id',
        'nama_panti',
        'kabupaten_kota', 
        'tanggal_upload',
        'status',
        'keterangan',
        'catatan_admin',
        'file_laporan'
    ];

    protected $casts = [
        'tanggal_upload' => 'date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
