<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LaporanKegiatan extends Model
{
    protected $fillable = [
        'user_id',
        'nama_kegiatan',
        'jenis_kegiatan',
        'tanggal_pelaksanaan',
        'waktu_mulai',
        'waktu_selesai',
        'lokasi_pelaksanaan',
        'jumlah_peserta',
        'penanggung_jawab',
        'deskripsi_kegiatan',
        'hasil_kegiatan',
        'kendala_tantangan',
        'foto_kegiatan',
        'laporan_kegiatan',
        'admin_feedback',
        'reviewed_by',
        'reviewed_at',
    ];

    protected $casts = [
        'tanggal_pelaksanaan' => 'date',
        'waktu_mulai' => 'datetime:H:i',
        'waktu_selesai' => 'datetime:H:i',
        'jumlah_peserta' => 'integer',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
