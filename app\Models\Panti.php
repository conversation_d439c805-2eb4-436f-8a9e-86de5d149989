<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Panti extends Model
{
    protected $fillable = [
        'user_id', 'nama', 'alamat', 'kabupaten', 'telepon', 'email', 'pimpinan', 'npwp', 'status', 'admin_notes'
    ];

    public function documents()
    {
        return $this->hasMany(PantiDocument::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
