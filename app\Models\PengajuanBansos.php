<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PengajuanBansos extends Model
{
    use HasFactory;

    protected $table = 'pengajuan_bansos';

    protected $fillable = [
        'user_id',
        'nama_panti',
        'nama_ketua',
        'kabupaten_kota',
        'no_hp',
        'tanggal_pengajuan',
        'proposal',
        'sk_pengurus',
        'rencana_anggaran',
        'akta_notaris',
        'surat_pengesahan',
        'tanda_daftar_lks',
        'data_anak',
        'sarana_prasarana',
        'surat_pernyataan',
        'pakta_integritas',
        'npwp',
        'surat_domisil',
        'izin_operasional',
        'foto_ktp',
        'foto_rekening',
        'surat_rekomendasi',
        'status',
        'keterangan',
    ];

    protected $casts = [
        'tanggal_pengajuan' => 'date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function filePencairans()
    {
        return $this->hasMany(FilePencairan::class, 'pengajuan_bansos_id');
    }

    public function dokumenVerifikasis()
    {
        return $this->hasMany(DokumenVerifikasi::class, 'pengajuan_bansos_id');
    }
}
