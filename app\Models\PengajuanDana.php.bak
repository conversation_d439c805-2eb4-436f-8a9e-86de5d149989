<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PengajuanDana extends Model
{
    protected $fillable = [
        'user_id',
        'tanggal_pengajuan',
        'tujuan_penggunaan',
        'periode_mulai',
        'periode_selesai',
        'total_dana',
        'deskripsi_kebutuhan',
        'file_proposal',
        'file_rekening',
        'file_ktp',
        'file_foto_kegiatan',
        'file_rab',
        'status',
        'catatan_admin',
        'reviewed_by',
        'reviewed_at',
    ];

    protected $casts = [
        'tanggal_pengajuan' => 'date',
        'total_dana' => 'decimal:2',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
