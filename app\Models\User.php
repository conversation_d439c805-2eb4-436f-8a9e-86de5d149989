<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    // Role constants
    const ROLE_PANTI_ASUHAN = 'panti_asuhan';
    const ROLE_DINSOS_PROVINSI_RIAU = 'dinsos_provinsi_riau';
    const ROLE_DINSOS_KABUPATEN_KOTA = 'dinsos_kabupaten_kota';
    const ROLE_COTA = 'cota';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get dashboard URL based on user role
     */
    public function getDashboardUrlAttribute(): string
    {
        return match($this->role) {
            self::ROLE_PANTI_ASUHAN => '/panti/dashboard',
            self::ROLE_DINSOS_PROVINSI_RIAU => '/dinsosriau/dashboard',
            self::ROLE_DINSOS_KABUPATEN_KOTA => '/dinsoskota/dashboard',
            self::ROLE_COTA => '/cota/dashboard',
            default => '/dashboard'
        };
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user can access module
     */
    public function canAccessModule(string $module): bool
    {
        return match($module) {
            'panti' => in_array($this->role, [self::ROLE_PANTI_ASUHAN, self::ROLE_DINSOS_PROVINSI_RIAU]),
            'cota' => in_array($this->role, [self::ROLE_DINSOS_PROVINSI_RIAU, self::ROLE_DINSOS_KABUPATEN_KOTA, self::ROLE_COTA]),
            'bantuan_sosial' => in_array($this->role, [self::ROLE_PANTI_ASUHAN, self::ROLE_DINSOS_PROVINSI_RIAU]),
            default => false
        };
    }
}
