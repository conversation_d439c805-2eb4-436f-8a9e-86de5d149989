<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserActivity extends Model
{
    protected $fillable = [
        'user_id',
        'activity_type',
        'module',
        'description',
        'metadata',
        'completed_at'
    ];

    protected $casts = [
        'metadata' => 'array',
        'completed_at' => 'datetime'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Helper method to log activity
    public static function log(string $activityType, string $module, string $description, ?array $metadata = null): void
    {
        self::create([
            'user_id' => auth()->id(),
            'activity_type' => $activityType,
            'module' => $module,
            'description' => $description,
            'metadata' => $metadata,
            'completed_at' => now()
        ]);
    }
}
