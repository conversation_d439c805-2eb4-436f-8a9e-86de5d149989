<?php
require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\CalonOrangTuaAsuh;
use App\Models\Cota;

echo "=== Checking CalonOrangTuaAsuh data ===\n";
$count = CalonOrangTuaAsuh::count();
echo "Total CalonOrangTuaAsuh records: $count\n\n";

if ($count > 0) {
    $cotas = CalonOrangTuaAsuh::take(5)->get();
    foreach($cotas as $cota) {
        echo "ID: {$cota->id}, Nama: {$cota->nama_lengkap}, Status: {$cota->status}\n";
    }
} else {
    echo "No CalonOrangTuaAsuh records found.\n";
    echo "Let's check Cota records to see if we can create CalonOrangTuaAsuh records:\n\n";
    
    $cotaCount = Cota::count();
    echo "Total Cota records: $cotaCount\n";
    
    if ($cotaCount > 0) {
        $cotaData = Cota::take(3)->get();
        foreach($cotaData as $cota) {
            echo "ID: {$cota->id}, Nama: {$cota->nama}, Email: {$cota->email}, Status: {$cota->status}\n";
        }
    }
}
