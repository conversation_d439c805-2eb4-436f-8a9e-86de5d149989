<?php
require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\UserActivity;
use App\Models\User;

echo "=== Creating UserActivity data for testing pagination ===\n";

// Find a panti user
$pantiUser = User::where('role', 'panti_asuhan')->first();

if (!$pantiUser) {
    echo "No panti user found. Creating one...\n";
    $pantiUser = User::create([
        'name' => 'Test Panti User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role' => 'panti_asuhan'
    ]);
    echo "Created panti user: {$pantiUser->email}\n";
}

echo "Using panti user: {$pantiUser->name} ({$pantiUser->email})\n\n";

// Create sample activities
$activities = [
    [
        'activity_type' => 'add',
        'module' => 'data_anak',
        'description' => 'Menambahkan data anak asuh baru - Ahmad Rizki',
        'metadata' => ['action' => 'create', 'child_name' => 'Ahmad Rizki'],
        'completed_at' => now()->subDays(1)
    ],
    [
        'activity_type' => 'confirm',
        'module' => 'data_anak',
        'description' => 'Memperbarui status pendidikan anak asuh',
        'metadata' => ['action' => 'update', 'field' => 'education_status'],
        'completed_at' => now()->subDays(2)
    ],
    [
        'activity_type' => 'add',
        'module' => 'data_panti',
        'description' => 'Mengunggah dokumen akreditasi panti',
        'metadata' => ['action' => 'upload', 'document_type' => 'accreditation'],
        'completed_at' => now()->subDays(3)
    ],
    [
        'activity_type' => 'confirm',
        'module' => 'pengajuan_dana',
        'description' => 'Mengajukan bantuan dana operasional',
        'metadata' => ['action' => 'submit', 'amount' => 5000000],
        'completed_at' => now()->subDays(4)
    ],
    [
        'activity_type' => 'print',
        'module' => 'laporan_kegiatan',
        'description' => 'Menyerahkan laporan kegiatan bulanan',
        'metadata' => ['action' => 'submit', 'month' => date('Y-m', strtotime('-1 month'))],
        'completed_at' => now()->subDays(5)
    ],
    [
        'activity_type' => 'confirm',
        'module' => 'bantuan_sosial',
        'description' => 'Mengajukan bantuan sosial untuk anak asuh',
        'metadata' => ['action' => 'submit', 'beneficiary_count' => 15],
        'completed_at' => now()->subDays(6)
    ],
    [
        'activity_type' => 'add',
        'module' => 'data_anak',
        'description' => 'Menambahkan data anak asuh baru - Siti Fatimah',
        'metadata' => ['action' => 'create', 'child_name' => 'Siti Fatimah'],
        'completed_at' => now()->subDays(7)
    ],
    [
        'activity_type' => 'confirm',
        'module' => 'data_panti',
        'description' => 'Memperbarui profil lengkap panti asuhan',
        'metadata' => ['action' => 'update', 'section' => 'profile'],
        'completed_at' => now()->subDays(8)
    ],
    [
        'activity_type' => 'print',
        'module' => 'pengajuan_dana',
        'description' => 'Mengajukan bantuan dana pendidikan',
        'metadata' => ['action' => 'submit', 'amount' => 3000000],
        'completed_at' => now()->subDays(9)
    ],
    [
        'activity_type' => 'add',
        'module' => 'data_anak',
        'description' => 'Menambahkan data anak asuh baru - Budi Santoso',
        'metadata' => ['action' => 'create', 'child_name' => 'Budi Santoso'],
        'completed_at' => now()->subDays(10)
    ]
];

// Delete existing activities for this user
UserActivity::where('user_id', $pantiUser->id)->delete();
echo "Deleted existing activities for user.\n";

// Create new activities
foreach ($activities as $activity) {
    UserActivity::create([
        'user_id' => $pantiUser->id,
        'activity_type' => $activity['activity_type'],
        'module' => $activity['module'],
        'description' => $activity['description'],
        'metadata' => $activity['metadata'],
        'completed_at' => $activity['completed_at']
    ]);
}

echo "Created " . count($activities) . " activities for testing.\n";
echo "Total UserActivity records: " . UserActivity::count() . "\n";
echo "Activities for panti user: " . UserActivity::where('user_id', $pantiUser->id)->count() . "\n\n";

echo "You can now login with:\n";
echo "Email: {$pantiUser->email}\n";
echo "Password: password\n";
echo "Then visit: http://127.0.0.1:8000/panti/dashboard\n";

echo "\nDone!\n";
