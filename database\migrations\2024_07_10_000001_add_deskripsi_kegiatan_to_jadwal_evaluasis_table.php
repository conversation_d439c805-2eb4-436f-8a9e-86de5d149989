<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->text('deskripsi_kegiatan')->nullable()->after('jam_evaluasi');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->dropColumn('deskripsi_kegiatan');
        });
    }
};
