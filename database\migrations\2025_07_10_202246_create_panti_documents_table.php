<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('panti_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('panti_id');
            $table->string('nama_dokumen');
            $table->string('file_path')->nullable();
            $table->enum('status', ['belum_lengkap', 'lengkap'])->default('belum_lengkap');
            $table->timestamps();

            $table->foreign('panti_id')->references('id')->on('pantis')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('panti_documents');
    }
};
