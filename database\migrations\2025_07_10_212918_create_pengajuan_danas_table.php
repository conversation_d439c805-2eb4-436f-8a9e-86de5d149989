<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pengajuan_danas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('tanggal_pengajuan');
            $table->enum('tujuan_penggunaan', [
                'pendidikan', 
                'kesehatan', 
                'makanan pokok', 
                'renovasi', 
                'kegiatan sosial', 
                'lainnya'
            ]);
            $table->string('periode_mulai', 5); // Format MM/YY
            $table->string('periode_selesai', 5); // Format MM/YY
            $table->decimal('total_dana', 15, 2);
            $table->text('deskripsi_kebutuhan');
            $table->string('file_proposal')->nullable();
            $table->string('file_rekening')->nullable();
            $table->string('file_ktp')->nullable();
            $table->string('file_foto_kegiatan')->nullable();
            $table->enum('status', ['pending', 'diterima', 'ditolak'])->default('pending');
            $table->text('catatan_admin')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pengajuan_danas');
    }
};
