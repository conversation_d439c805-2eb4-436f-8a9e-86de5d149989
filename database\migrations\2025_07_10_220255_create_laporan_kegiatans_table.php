<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('laporan_kegiatans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('nama_kegiatan');
            $table->enum('jenis_kegiatan', [
                'pendidikan dan pelatihan',
                'kegiatan sosial', 
                'kesehatan',
                'olahraga & rekreasi',
                'keagamaan',
                'seni & budaya',
                'keterampilan',
                'lainnya'
            ]);
            $table->date('tanggal_pelaksanaan');
            $table->time('waktu_mulai');
            $table->time('waktu_selesai');
            $table->string('lokasi_pelaksanaan');
            $table->integer('jumlah_peserta');
            $table->string('penanggung_jawab');
            $table->text('deskripsi_kegiatan');
            $table->text('hasil_kegiatan');
            $table->text('kendala_tantangan')->nullable();
            $table->string('foto_kegiatan')->nullable();
            $table->string('laporan_kegiatan')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('laporan_kegiatans');
    }
};
