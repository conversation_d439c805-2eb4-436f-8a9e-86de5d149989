<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calon_orang_tua_asuhs', function (Blueprint $table) {
            $table->id();
            $table->string('nama_lengkap');
            $table->string('nik', 16)->unique();
            $table->string('kabupaten_kota');
            $table->string('alamat');
            $table->string('telepon');
            $table->string('email')->nullable();
            $table->enum('status', ['draft', 'terima', 'tolak'])->default('draft');
            $table->text('keterangan')->nullable();
            $table->string('file_ktp')->nullable();
            $table->string('file_kk')->nullable();
            $table->string('file_surat_pernikahan')->nullable();
            $table->string('file_slip_gaji')->nullable();
            $table->string('file_surat_keterangan_sehat')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('calon_orang_tua_asuhs');
    }
};
