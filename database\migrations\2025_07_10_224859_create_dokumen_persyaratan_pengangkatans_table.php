<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dokumen_persyaratan_pengangkatans', function (Blueprint $table) {
            $table->id();
            $table->string('nama_dokumen');
            $table->string('jenis_berkas'); // pdf, doc, docx, jpg, png, etc
            $table->string('file_path');
            $table->text('deskripsi')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dokumen_persyaratan_pengangkatans');
    }
};
