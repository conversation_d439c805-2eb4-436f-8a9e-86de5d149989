<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jadwal_sidangs', function (Blueprint $table) {
            $table->id();
            $table->date('tanggal_sidang');
            $table->time('waktu_mulai');
            $table->time('waktu_selesai');
            $table->string('tempat_sidang');
            $table->text('agenda')->nullable();
            $table->enum('status', ['draft', 'final', 'selesai', 'batal'])->default('draft');
            $table->integer('kapasitas')->default(10);
            $table->text('keterangan')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jadwal_sidangs');
    }
};
