<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pengajuan_bansos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('nama_panti');
            $table->string('nama_ketua');
            $table->string('kabupaten_kota');
            $table->string('no_hp');
            $table->date('tanggal_pengajuan');
            
            // File uploads
            $table->string('proposal')->nullable();
            $table->string('sk_pengurus')->nullable();
            $table->string('rencana_anggaran')->nullable();
            $table->string('akta_notaris')->nullable();
            $table->string('surat_pengesahan')->nullable();
            $table->string('tanda_daftar_lks')->nullable();
            $table->string('data_anak')->nullable();
            $table->string('sarana_prasarana')->nullable();
            $table->string('surat_pernyataan')->nullable();
            $table->string('pakta_integritas')->nullable();
            $table->string('npwp')->nullable();
            $table->string('surat_domisil')->nullable();
            $table->string('izin_operasional')->nullable();
            $table->string('foto_ktp')->nullable();
            $table->string('foto_rekening')->nullable();
            
            // Status
            $table->enum('status', ['Diproses', 'Diterima', 'Ditolak'])->default('Diproses');
            $table->text('keterangan')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pengajuan_bansos');
    }
};
