<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('laporan_bansos', function (Blueprint $table) {
            $table->id();
            $table->string('nama_panti');
            $table->string('kabupaten_kota');
            $table->date('tanggal_upload');
            $table->enum('status', ['diterima', 'ditolak', 'diproses'])->default('diproses');
            $table->text('keterangan')->nullable();
            $table->string('file_laporan')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('laporan_bansos');
    }
};
