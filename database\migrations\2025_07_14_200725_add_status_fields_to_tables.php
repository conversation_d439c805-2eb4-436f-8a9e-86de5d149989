<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add status to panti table
        Schema::table('pantis', function (Blueprint $table) {
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
        });

        // Add admin fields to pengajuan_dana table
        Schema::table('pengajuan_danas', function (Blueprint $table) {
            $table->string('reviewed_by')->nullable();
            $table->timestamp('reviewed_at')->nullable();
        });

        // Add admin feedback to laporan_kegiatan table  
        Schema::table('laporan_kegiatans', function (Blueprint $table) {
            $table->text('admin_feedback')->nullable();
            $table->string('reviewed_by')->nullable();
            $table->timestamp('reviewed_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pantis', function (Blueprint $table) {
            $table->dropColumn(['status', 'admin_notes']);
        });

        Schema::table('pengajuan_danas', function (Blueprint $table) {
            $table->dropColumn(['reviewed_by', 'reviewed_at']);
        });

        Schema::table('laporan_kegiatans', function (Blueprint $table) {
            $table->dropColumn(['admin_feedback', 'reviewed_by', 'reviewed_at']);
        });
    }
};
