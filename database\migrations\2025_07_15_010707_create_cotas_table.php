<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cotas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Informasi Personal
            $table->string('nama');
            $table->string('nik', 16)->unique();
            $table->string('telepon');
            $table->string('email')->unique();
            $table->string('kabupaten_kota');
            
            // Dokumen Persyaratan
            $table->string('ktp_path');
            $table->string('kartu_keluarga_path');
            $table->string('surat_pernikahan_path');
            $table->string('slip_gaji_path');
            $table->string('surat_keterangan_sehat_path');
            
            // Status dan metadata
            $table->enum('status', ['menunggu_verifikasi', 'proses_verifikasi', 'tersertifikasi', 'ditolak'])
                ->default('menunggu_verifikasi');
            $table->text('catatan')->nullable();
            $table->timestamp('tanggal_verifikasi')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cotas');
    }
};
