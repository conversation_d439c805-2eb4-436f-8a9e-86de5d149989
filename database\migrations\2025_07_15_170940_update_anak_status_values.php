<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing records to use new status values
        // Since we're changing from specific conditions to general status,
        // we'll set all existing records to 'aktif' by default
        // Admin can later change them individually as needed
        DB::table('anaks')->update([
            'status_anak' => 'aktif'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // In case of rollback, we can't accurately restore the old values
        // So we'll just set them to a default value
        DB::table('anaks')->update([
            'status_anak' => 'Yatim (Ayah Meninggal)'
        ]);
    }
};
