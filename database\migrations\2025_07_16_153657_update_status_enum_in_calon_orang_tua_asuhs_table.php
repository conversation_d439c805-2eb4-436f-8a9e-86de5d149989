<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to modify the column directly
        DB::statement("ALTER TABLE calon_orang_tua_asuhs MODIFY COLUMN status ENUM('draft', 'terima', 'tolak', 'Diproses', 'Diterima', 'Ditolak') DEFAULT 'draft'");
        
        // Update existing data to new values
        DB::statement("UPDATE calon_orang_tua_asuhs SET status = 'Diproses' WHERE status = 'draft'");
        DB::statement("UPDATE calon_orang_tua_asuhs SET status = 'Diterima' WHERE status = 'terima'");
        DB::statement("UPDATE calon_orang_tua_asuhs SET status = 'Ditolak' WHERE status = 'tolak'");
        
        // Finally, remove old enum values and set new default
        DB::statement("ALTER TABLE calon_orang_tua_asuhs MODIFY COLUMN status ENUM('Diproses', 'Diterima', 'Ditolak') DEFAULT 'Diproses'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add old values back to enum
        DB::statement("ALTER TABLE calon_orang_tua_asuhs MODIFY COLUMN status ENUM('draft', 'terima', 'tolak', 'Diproses', 'Diterima', 'Ditolak') DEFAULT 'Diproses'");
        
        // Revert data back to old values
        DB::statement("UPDATE calon_orang_tua_asuhs SET status = 'draft' WHERE status = 'Diproses'");
        DB::statement("UPDATE calon_orang_tua_asuhs SET status = 'terima' WHERE status = 'Diterima'");
        DB::statement("UPDATE calon_orang_tua_asuhs SET status = 'tolak' WHERE status = 'Ditolak'");
        
        // Remove new enum values
        DB::statement("ALTER TABLE calon_orang_tua_asuhs MODIFY COLUMN status ENUM('draft', 'terima', 'tolak') DEFAULT 'draft'");
    }
};
