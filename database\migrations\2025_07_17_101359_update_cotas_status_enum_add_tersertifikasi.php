<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the status enum to include 'tersertifikasi' alongside 'terdaftar'
        Schema::table('cotas', function (Blueprint $table) {
            $table->enum('status', ['menunggu_verifikasi', 'proses_verifikasi', 'terdaftar', 'tersertifikasi', 'ditolak'])
                ->default('menunggu_verifikasi')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to the previous enum values
        Schema::table('cotas', function (Blueprint $table) {
            $table->enum('status', ['menunggu_verifikasi', 'proses_verifikasi', 'terdaftar', 'ditolak'])
                ->default('menunggu_verifikasi')
                ->change();
        });
    }
};
