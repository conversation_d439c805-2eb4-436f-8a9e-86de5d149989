<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('file_pencairans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pengajuan_bansos_id')->constrained('pengajuan_bansos')->onDelete('cascade');
            $table->string('nama_file');
            $table->string('file_path');
            $table->string('jenis_file'); // bukti_transfer, surat_pencairan, dll
            $table->text('keterangan')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('file_pencairans');
    }
};
