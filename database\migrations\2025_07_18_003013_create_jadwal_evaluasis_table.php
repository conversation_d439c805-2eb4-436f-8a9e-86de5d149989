<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jadwal_evaluasis', function (Blueprint $table) {
            $table->id();
            $table->string('nama_tim');
            $table->string('nama_evaluator');
            $table->string('nip');
            $table->string('bidang');
            $table->date('tanggal_evaluasi');
            $table->time('jam_evaluasi');
            $table->enum('status', ['terjadwal', 'selesai', 'dibatalkan'])->default('terjadwal');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jadwal_evaluasis');
    }
};
