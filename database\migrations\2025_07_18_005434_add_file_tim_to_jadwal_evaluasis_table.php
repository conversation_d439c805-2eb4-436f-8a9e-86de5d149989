<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->string('file_tim')->nullable()->after('nama_tim');
            $table->string('nama_tim')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->dropColumn('file_tim');
            $table->string('nama_tim')->nullable(false)->change();
        });
    }
};
