<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dokumen_verifikasis', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pengajuan_bansos_id')->constrained('pengajuan_bansos')->onDelete('cascade');
            $table->integer('dokumen_id'); // ID dokumen berdasarkan array di frontend
            $table->string('nama_dokumen');
            $table->enum('status_verifikasi', ['pending', 'diterima', 'ditolak'])->default('pending');
            $table->text('catatan')->nullable();
            $table->timestamps();
            
            // Unique constraint to prevent duplicate verification for same document in same pengajuan
            $table->unique(['pengajuan_bansos_id', 'dokumen_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dokumen_verifikasis');
    }
};
