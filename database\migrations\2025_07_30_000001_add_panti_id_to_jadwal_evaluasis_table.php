<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->unsignedBigInteger('panti_id')->nullable()->after('id');
            $table->foreign('panti_id')->references('id')->on('pantis')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->dropForeign(['panti_id']);
            $table->dropColumn('panti_id');
        });
    }
};
