<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cota_dokumen_persyaratans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cota_id')->constrained('cotas')->onDelete('cascade');
            $table->string('nama_dokumen');
            $table->string('file_path');
            $table->string('jenis_berkas')->nullable(); // pdf, jpg, etc
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cota_dokumen_persyaratans');
    }
};
