<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('jadwal_kunjungans', function (Blueprint $table) {
            $table->string('waktu_kunjungan')->nullable();
            $table->string('tim_anggota_1')->nullable();
            $table->string('tim_anggota_2')->nullable();
            $table->string('tim_anggota_3')->nullable();
            $table->string('surat_perjalanan_dinas')->nullable();
            $table->string('status')->default('scheduled');
        });
    }

    public function down(): void
    {
        Schema::table('jadwal_kunjungans', function (Blueprint $table) {
            $table->dropColumn([
                'waktu_kunjungan',
                'tim_anggota_1',
                'tim_anggota_2',
                'tim_anggota_3',
                'surat_perjalanan_dinas',
                'status',
            ]);
        });
    }
};
