<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('jadwal_kunjungans', function (Blueprint $table) {
            $table->text('deskripsi_kegiatan')->nullable()->after('keterangan');
            $table->text('catatan_hasil_kunjungan')->nullable()->after('deskripsi_kegiatan');
            $table->string('upload_dokumentasi')->nullable()->after('catatan_hasil_kunjungan');
        });
    }

    public function down(): void
    {
        Schema::table('jadwal_kunjungans', function (Blueprint $table) {
            $table->dropColumn([
                'deskripsi_kegiatan',
                'catatan_hasil_kunjungan',
                'upload_dokumentasi',
            ]);
        });
    }
};
