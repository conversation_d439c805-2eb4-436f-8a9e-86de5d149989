<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->string('nama_evaluator')->nullable()->change();
            $table->string('nip')->nullable()->change();
            $table->string('bidang')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jadwal_evaluasis', function (Blueprint $table) {
            $table->string('nama_evaluator')->nullable(false)->change();
            $table->string('nip')->nullable(false)->change();
            $table->string('bidang')->nullable(false)->change();
        });
    }
};
