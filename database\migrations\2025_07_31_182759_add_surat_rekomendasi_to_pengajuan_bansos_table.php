<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pengajuan_bansos', function (Blueprint $table) {
            $table->string('surat_rekomendasi')->nullable()->after('foto_rekening');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pengajuan_bansos', function (Blueprint $table) {
            $table->dropColumn('surat_rekomendasi');
        });
    }
};
