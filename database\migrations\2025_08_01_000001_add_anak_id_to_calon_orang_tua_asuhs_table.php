<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calon_orang_tua_asuhs', function (Blueprint $table) {
            $table->foreignId('anak_id')->nullable()->constrained('anaks')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calon_orang_tua_asuhs', function (Blueprint $table) {
            $table->dropForeign(['anak_id']);
            $table->dropColumn('anak_id');
        });
    }
};
