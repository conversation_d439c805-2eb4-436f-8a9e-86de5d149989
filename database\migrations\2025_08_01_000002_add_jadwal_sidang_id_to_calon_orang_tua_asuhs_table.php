<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calon_orang_tua_asuhs', function (Blueprint $table) {
            $table->foreignId('jadwal_sidang_id')->nullable()->constrained('jadwal_sidangs')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calon_orang_tua_asuhs', function (Blueprint $table) {
            $table->dropForeign(['jadwal_sidang_id']);
            $table->dropColumn('jadwal_sidang_id');
        });
    }
};
