<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pengajuan_danas', function (Blueprint $table) {
            $table->string('file_laporan_penggunaan')->nullable()->after('file_rab');
            $table->timestamp('tanggal_upload_laporan')->nullable()->after('file_laporan_penggunaan');
            $table->enum('status_laporan', ['belum_upload', 'menunggu_verifikasi', 'diterima', 'ditolak'])->default('belum_upload')->after('tanggal_upload_laporan');
            $table->text('catatan_laporan')->nullable()->after('status_laporan');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pengajuan_danas', function (Blueprint $table) {
            $table->dropColumn([
                'file_laporan_penggunaan',
                'tanggal_upload_laporan',
                'status_laporan',
                'catatan_laporan'
            ]);
        });
    }
};
