<?php

namespace Database\Seeders;

use App\Models\CalonOrangTuaAsuh;
use App\Models\DokumenPersyaratanPengangkatan;
use App\Models\JadwalSidang;
use Illuminate\Database\Seeder;

class CotaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Calon Orang Tua Asuh
        $calonOrangTuaAsuh = [
            [
                'nama_lengkap' => 'Ahmad Santoso',
                'nik' => '1471010101850001',
                'kabupaten_kota' => 'Pekanbaru',
                'alamat' => 'Jl. Sudirman No. 123, Pekanbaru',
                'telepon' => '081234567890',
                'email' => '<EMAIL>',
                'status' => 'terima',
                'keterangan' => 'Sudah memenuhi semua persyaratan',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_lengkap' => 'Siti Aminah',
                'nik' => '1471010101860002',
                'kabupaten_kota' => 'Dumai',
                'alamat' => 'Jl. Datuk Laksamana No. 45, Dumai',
                'telepon' => '081234567891',
                'email' => '<EMAIL>',
                'status' => 'draft',
                'keterangan' => 'Masih dalam proses verifikasi dokumen',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_lengkap' => 'Budi Hartono',
                'nik' => '1471010101870003',
                'kabupaten_kota' => 'Kampar',
                'alamat' => 'Jl. Lintas Timur No. 67, Kampar',
                'telepon' => '081234567892',
                'email' => '<EMAIL>',
                'status' => 'terima',
                'keterangan' => 'Telah lulus wawancara dan home visit',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_lengkap' => 'Rina Sari',
                'nik' => '1471010101880004',
                'kabupaten_kota' => 'Rokan Hulu',
                'alamat' => 'Jl. Lintas Sumatera No. 89, Rokan Hulu',
                'telepon' => '081234567893',
                'email' => '<EMAIL>',
                'status' => 'draft',
                'keterangan' => 'Sedang menunggu kelengkapan berkas',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_lengkap' => 'Joko Widodo',
                'nik' => '1471010101890005',
                'kabupaten_kota' => 'Bengkalis',
                'alamat' => 'Jl. Bengkalis Raya No. 12, Bengkalis',
                'telepon' => '081234567894',
                'email' => '<EMAIL>',
                'status' => 'terima',
                'keterangan' => 'Sudah disetujui untuk proses sidang',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($calonOrangTuaAsuh as $data) {
            CalonOrangTuaAsuh::create($data);
        }

        // Seed Dokumen Persyaratan Pengangkatan
        $dokumenPersyaratan = [
            [
                'nama_dokumen' => 'Rekomendasi Dinas Sosial Kabupaten/Kota',
                'jenis_berkas' => 'pdf',
                'file_path' => 'dokumen-persyaratan/rekomendasi-dinsos.pdf',
                'deskripsi' => 'Surat rekomendasi dari Dinas Sosial setempat yang menyatakan kelayakan calon orang tua asuh',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_dokumen' => 'Surat Keterangan Kelakuan Baik',
                'jenis_berkas' => 'pdf',
                'file_path' => 'dokumen-persyaratan/skck.pdf',
                'deskripsi' => 'Surat Keterangan Catatan Kepolisian (SKCK) dari Kepolisian setempat',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_dokumen' => 'Surat Keterangan Sehat',
                'jenis_berkas' => 'pdf',
                'file_path' => 'dokumen-persyaratan/surat-sehat.pdf',
                'deskripsi' => 'Surat keterangan sehat jasmani dan rohani dari dokter yang berwenang',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_dokumen' => 'Surat Keterangan Penghasilan',
                'jenis_berkas' => 'pdf',
                'file_path' => 'dokumen-persyaratan/surat-penghasilan.pdf',
                'deskripsi' => 'Surat keterangan penghasilan atau slip gaji sebagai bukti kemampuan finansial',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_dokumen' => 'Fotocopy KTP dan Kartu Keluarga',
                'jenis_berkas' => 'pdf',
                'file_path' => 'dokumen-persyaratan/ktp-kk.pdf',
                'deskripsi' => 'Fotocopy KTP dan Kartu Keluarga yang masih berlaku',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'nama_dokumen' => 'Surat Pernyataan Kesanggupan',
                'jenis_berkas' => 'pdf',
                'file_path' => 'dokumen-persyaratan/surat-kesanggupan.pdf',
                'deskripsi' => 'Surat pernyataan kesanggupan merawat, mendidik dan membesarkan anak asuh',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($dokumenPersyaratan as $data) {
            DokumenPersyaratanPengangkatan::create($data);
        }

        // Seed Jadwal Sidang
        $jadwalSidang = [
            [
                'tanggal_sidang' => '2025-07-15',
                'waktu_mulai' => '09:00:00',
                'waktu_selesai' => '12:00:00',
                'tempat_sidang' => 'Ruang Sidang Dinsos Provinsi Riau',
                'agenda' => 'Sidang penetapan calon orang tua asuh periode Juli 2025',
                'status' => 'final',
                'kapasitas' => 15,
                'keterangan' => 'Sidang terbuka untuk umum',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tanggal_sidang' => '2025-07-20',
                'waktu_mulai' => '13:00:00',
                'waktu_selesai' => '16:00:00',
                'tempat_sidang' => 'Ruang Rapat Utama Dinsos Provinsi Riau',
                'agenda' => 'Evaluasi dan penetapan pengangkatan anak asuh batch 2',
                'status' => 'draft',
                'kapasitas' => 20,
                'keterangan' => 'Masih dalam tahap persiapan',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tanggal_sidang' => '2025-07-25',
                'waktu_mulai' => '10:00:00',
                'waktu_selesai' => '15:00:00',
                'tempat_sidang' => 'Aula Dinsos Provinsi Riau',
                'agenda' => 'Sidang pengangkatan anak asuh dan sosialisasi program COTA',
                'status' => 'final',
                'kapasitas' => 30,
                'keterangan' => 'Termasuk sesi sosialisasi untuk masyarakat',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($jadwalSidang as $data) {
            JadwalSidang::create($data);
        }
    }
}
