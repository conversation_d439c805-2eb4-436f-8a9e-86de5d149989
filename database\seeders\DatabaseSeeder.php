<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create test users for each role
        User::factory()->create([
            'name' => 'Admin <PERSON>',
            'email' => '<EMAIL>',
            'role' => User::ROLE_PANTI_ASUHAN,
            'password' => bcrypt('password123'),
        ]);

        User::factory()->create([
            'name' => 'Admin Dinsos <PERSON>',
            'email' => '<EMAIL>',
            'role' => User::ROLE_DINSOS_PROVINSI_RIAU,
            'password' => bcrypt('password123'),
        ]);

        User::factory()->create([
            'name' => 'Admin Dinsos Kabupaten/Kota',
            'email' => '<EMAIL>',
            'role' => User::ROLE_DINSOS_KABUPATEN_KOTA,
            'password' => bcrypt('password123'),
        ]);

        User::factory()->create([
            'name' => 'Calon Orang Tua Asuh',
            'email' => '<EMAIL>',
            'role' => User::ROLE_COTA,
            'password' => bcrypt('password123'),
        ]);

        // Call other seeders
        $this->call([
            CotaSeeder::class,
            TestCotaDataSeeder::class,
        ]);
    }
}
