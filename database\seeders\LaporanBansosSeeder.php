<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LaporanBansosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\LaporanBansos::create([
            'nama_panti' => 'Panti <PERSON>',
            'kabupaten_kota' => 'Pekanbaru',
            'tanggal_upload' => '2024-01-20',
            'status' => 'diterima',
            'keterangan' => 'Laporan telah diterima dan sesuai dengan standar yang ditetapkan.',
            'file_laporan' => null,
        ]);

        \App\Models\LaporanBansos::create([
            'nama_panti' => 'Panti <PERSON>',
            'kabupaten_kota' => 'Dumai',
            'tanggal_upload' => '2024-02-15',
            'status' => 'diproses',
            'keterangan' => 'Laporan sedang dalam tahap review dan verifikasi.',
            'file_laporan' => null,
        ]);

        \App\Models\LaporanBansos::create([
            'nama_panti' => 'Panti <PERSON>',
            'kabupaten_kota' => 'Kampar',
            'tanggal_upload' => '2024-03-10',
            'status' => 'ditolak',
            'keterangan' => 'Laporan ditolak karena beberapa dokumen pendukung belum lengkap. Silakan lengkapi dan upload ulang.',
            'file_laporan' => null,
        ]);

        \App\Models\LaporanBansos::create([
            'nama_panti' => 'Panti Asuhan Cahaya Hati',
            'kabupaten_kota' => 'Rokan Hulu',
            'tanggal_upload' => '2024-03-25',
            'status' => 'diterima',
            'keterangan' => 'Laporan pertanggungjawaban telah diterima dengan baik.',
            'file_laporan' => null,
        ]);

        \App\Models\LaporanBansos::create([
            'nama_panti' => 'Panti Asuhan Bintang Timur',
            'kabupaten_kota' => 'Bengkalis',
            'tanggal_upload' => '2024-04-05',
            'status' => 'diproses',
            'keterangan' => 'Laporan dalam proses review oleh tim ahli.',
            'file_laporan' => null,
        ]);
    }
}
