<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PantiIntegrationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample Panti data
        \App\Models\Panti::create([
            'user_id' => 1,
            'nama' => '<PERSON><PERSON>',
            'alamat' => 'Jl. Sudirman No. 123, Pekanbaru',
            'telepon' => '0761-123456',
            'email' => '<EMAIL>',
            'pimpinan' => '<PERSON>. <PERSON>',
            'npwp' => '12.345.678.9-123.000',
            'status' => 'approved',
            'admin_notes' => 'Panti sudah memenuhi semua persyaratan administrasi.',
        ]);

        \App\Models\Panti::create([
            'user_id' => 2,
            'nama' => '<PERSON><PERSON>',
            'alamat' => 'Jl. Diponegoro No. 45, Du<PERSON>i',
            'telepon' => '0765-789012',
            'email' => 'harapan<PERSON><EMAIL>',
            'pimpinan' => 'Hj. Siti <PERSON>',
            'npwp' => '98.765.432.1-987.000',
            'status' => 'pending',
            'admin_notes' => null,
        ]);

        // Create sample PengajuanDana data
        \App\Models\PengajuanDana::create([
            'user_id' => 1,
            'tanggal_pengajuan' => '2024-07-01',
            'tujuan_penggunaan' => 'renovasi',
            'periode_mulai' => '08/24',
            'periode_selesai' => '10/24',
            'total_dana' => 50000000,
            'deskripsi_kebutuhan' => 'Perbaikan atap bocor dan cat ulang asrama.',
            'status' => 'diterima',
            'catatan_admin' => 'Proposal sangat baik, dana disetujui.',
            'reviewed_by' => 'Admin Dinsos',
            'reviewed_at' => now(),
        ]);

        \App\Models\PengajuanDana::create([
            'user_id' => 2,
            'tanggal_pengajuan' => '2024-07-10',
            'tujuan_penggunaan' => 'makanan pokok',
            'periode_mulai' => '07/24',
            'periode_selesai' => '12/24',
            'total_dana' => 25000000,
            'deskripsi_kebutuhan' => 'Bantuan makanan untuk 50 anak asuh.',
            'status' => 'pending',
            'catatan_admin' => null,
        ]);

        // Create sample LaporanKegiatan data
        \App\Models\LaporanKegiatan::create([
            'user_id' => 1,
            'nama_kegiatan' => 'Pelatihan Keterampilan Menjahit',
            'jenis_kegiatan' => 'keterampilan',
            'tanggal_pelaksanaan' => '2024-07-05',
            'waktu_mulai' => '08:00',
            'waktu_selesai' => '12:00',
            'lokasi_pelaksanaan' => 'Ruang Keterampilan Panti',
            'jumlah_peserta' => 15,
            'penanggung_jawab' => 'Ibu Rina',
            'deskripsi_kegiatan' => 'Pelatihan menjahit untuk anak-anak perempuan.',
            'hasil_kegiatan' => 'Semua peserta berhasil membuat tas sederhana.',
            'kendala_tantangan' => 'Mesin jahit kurang, hanya ada 5 unit.',
            'admin_feedback' => 'Kegiatan sangat positif, saran tambah mesin jahit.',
            'reviewed_by' => 'Admin Dinsos',
            'reviewed_at' => now(),
        ]);

        \App\Models\LaporanKegiatan::create([
            'user_id' => 2,
            'nama_kegiatan' => 'Gotong Royong Kebersihan',
            'jenis_kegiatan' => 'kegiatan sosial',
            'tanggal_pelaksanaan' => '2024-07-12',
            'waktu_mulai' => '07:00',
            'waktu_selesai' => '10:00',
            'lokasi_pelaksanaan' => 'Halaman Panti',
            'jumlah_peserta' => 25,
            'penanggung_jawab' => 'Bapak Joko',
            'deskripsi_kegiatan' => 'Membersihkan halaman dan taman panti.',
            'hasil_kegiatan' => 'Halaman panti menjadi bersih dan rapi.',
            'kendala_tantangan' => 'Cuaca mendung, sempat turun hujan.',
        ]);
    }
}
