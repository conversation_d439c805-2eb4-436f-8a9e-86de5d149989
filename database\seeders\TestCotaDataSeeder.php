<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Cota;
use App\Models\CalonOrangTuaAsuh;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TestCotaDataSeeder extends Seeder
{
    /**
     * Run the database seeds untuk testing integrasi COTA dashboard.
     */
    public function run(): void
    {
        // Buat data COTA dengan berbagai status untuk testing
        $cotaUser = User::where('email', '<EMAIL>')->first();
        
        if ($cotaUser) {
            // Buat data COTA
            $cota = Cota::create([
                'user_id' => $cotaUser->id,
                'nama' => $cotaUser->name,
                'nik' => '1471010101900001',
                'telepon' => '081234567890',
                'email' => $cotaUser->email,
                'kabupaten_kota' => 'Pekanbaru',
                'ktp_path' => 'documents/ktp_test.pdf',
                'kartu_keluarga_path' => 'documents/kk_test.pdf',
                'surat_pernikahan_path' => 'documents/nikah_test.pdf',
                'slip_gaji_path' => 'documents/gaji_test.pdf',
                'surat_keterangan_sehat_path' => 'documents/sehat_test.pdf',
                'status' => 'terdaftar',
                'catatan' => 'Data lengkap untuk testing',
                'created_at' => now()->subDays(10),
                'updated_at' => now()->subDays(10),
            ]);

            // Buat data CalonOrangTuaAsuh yang terkait
            CalonOrangTuaAsuh::create([
                'cota_id' => $cota->id,
                'nama_lengkap' => $cotaUser->name,
                'nik' => '1471010101900001',
                'kabupaten_kota' => 'Pekanbaru',
                'alamat' => 'Jl. Test No. 123, Pekanbaru',
                'telepon' => '081234567890',
                'email' => $cotaUser->email,
                'status' => 'Diproses',
                'keterangan' => 'Sedang dalam tahap review oleh Dinsos Provinsi Riau',
                'file_ktp' => 'documents/ktp_test.pdf',
                'file_kk' => 'documents/kk_test.pdf',
                'file_surat_pernikahan' => 'documents/nikah_test.pdf',
                'file_slip_gaji' => 'documents/gaji_test.pdf',
                'file_surat_keterangan_sehat' => 'documents/sehat_test.pdf',
                'created_at' => now()->subDays(9),
                'updated_at' => now()->subDays(2),
            ]);
        }

        // Buat user COTA tambahan untuk testing dengan status berbeda
        $cotaUser2 = User::create([
            'name' => 'COTA Diterima',
            'email' => '<EMAIL>',
            'role' => User::ROLE_COTA,
            'password' => Hash::make('password123'),
        ]);

        $cota2 = Cota::create([
            'user_id' => $cotaUser2->id,
            'nama' => $cotaUser2->name,
            'nik' => '1471010101900002',
            'telepon' => '081234567891',
            'email' => $cotaUser2->email,
            'kabupaten_kota' => 'Dumai',
            'ktp_path' => 'documents/ktp_test2.pdf',
            'kartu_keluarga_path' => 'documents/kk_test2.pdf',
            'surat_pernikahan_path' => 'documents/nikah_test2.pdf',
            'slip_gaji_path' => 'documents/gaji_test2.pdf',
            'surat_keterangan_sehat_path' => 'documents/sehat_test2.pdf',
            'status' => 'tersertifikasi',
            'catatan' => 'Data disetujui dan tersertifikasi',
            'created_at' => now()->subDays(20),
            'updated_at' => now()->subDays(1),
        ]);

        CalonOrangTuaAsuh::create([
            'cota_id' => $cota2->id,
            'nama_lengkap' => $cotaUser2->name,
            'nik' => '1471010101900002',
            'kabupaten_kota' => 'Dumai',
            'alamat' => 'Jl. Approved No. 456, Dumai',
            'telepon' => '081234567891',
            'email' => $cotaUser2->email,
            'status' => 'Diterima',
            'keterangan' => 'Selamat! Aplikasi Anda telah disetujui',
            'file_ktp' => 'documents/ktp_test2.pdf',
            'file_kk' => 'documents/kk_test2.pdf',
            'file_surat_pernikahan' => 'documents/nikah_test2.pdf',
            'file_slip_gaji' => 'documents/gaji_test2.pdf',
            'file_surat_keterangan_sehat' => 'documents/sehat_test2.pdf',
            'created_at' => now()->subDays(19),
            'updated_at' => now()->subDays(1),
        ]);

        // Buat user COTA dengan status ditolak
        $cotaUser3 = User::create([
            'name' => 'COTA Ditolak',
            'email' => '<EMAIL>',
            'role' => User::ROLE_COTA,
            'password' => Hash::make('password123'),
        ]);

        $cota3 = Cota::create([
            'user_id' => $cotaUser3->id,
            'nama' => $cotaUser3->name,
            'nik' => '1471010101900003',
            'telepon' => '081234567892',
            'email' => $cotaUser3->email,
            'kabupaten_kota' => 'Kampar',
            'ktp_path' => 'documents/ktp_test3.pdf',
            'kartu_keluarga_path' => 'documents/kk_test3.pdf',
            'surat_pernikahan_path' => 'documents/nikah_test3.pdf',
            'slip_gaji_path' => 'documents/gaji_test3.pdf',
            'surat_keterangan_sehat_path' => 'documents/sehat_test3.pdf',
            'status' => 'ditolak',
            'catatan' => 'Dokumen tidak memenuhi persyaratan',
            'created_at' => now()->subDays(15),
            'updated_at' => now()->subDays(3),
        ]);

        CalonOrangTuaAsuh::create([
            'cota_id' => $cota3->id,
            'nama_lengkap' => $cotaUser3->name,
            'nik' => '1471010101900003',
            'kabupaten_kota' => 'Kampar',
            'alamat' => 'Jl. Rejected No. 789, Kampar',
            'telepon' => '081234567892',
            'email' => $cotaUser3->email,
            'status' => 'Ditolak',
            'keterangan' => 'Dokumen tidak lengkap, silakan lengkapi dan ajukan kembali',
            'file_ktp' => 'documents/ktp_test3.pdf',
            'file_kk' => 'documents/kk_test3.pdf',
            'file_surat_pernikahan' => 'documents/nikah_test3.pdf',
            'file_slip_gaji' => 'documents/gaji_test3.pdf',
            'file_surat_keterangan_sehat' => 'documents/sehat_test3.pdf',
            'created_at' => now()->subDays(14),
            'updated_at' => now()->subDays(3),
        ]);
    }
}
