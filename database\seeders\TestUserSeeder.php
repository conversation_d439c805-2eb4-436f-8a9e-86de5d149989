<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test user for Dinsos Provinsi Riau
        User::create([
            'name' => 'Admin Dinsos Riau',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_DINSOS_PROVINSI_RIAU,
            'email_verified_at' => now(),
        ]);

        // Create test user for Panti
        User::create([
            'name' => 'Ad<PERSON>ti',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_PANTI_ASUHAN,
            'email_verified_at' => now(),
        ]);

        // Create test user for COTA
        User::create([
            'name' => 'COTA User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_COTA,
            'email_verified_at' => now(),
        ]);
    }
}
