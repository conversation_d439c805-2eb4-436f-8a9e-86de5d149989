<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserActivity;
use App\Models\User;

class UserActivitySeeder extends Seeder
{
    public function run()
    {
        $pantiUser = User::where('role', 'panti')->first();
        
        if (!$pantiUser) {
            $this->command->info('No panti user found. Creating sample activities for user ID 1.');
            $userId = 1;
        } else {
            $userId = $pantiUser->id;
        }

        $activities = [
            [
                'user_id' => $userId,
                'activity_type' => 'view',
                'module' => 'data_anak',
                'description' => 'Melihat daftar anak asuh',
                'metadata' => ['page' => 'data_anak'],
                'completed_at' => now()->subHours(2)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'add',
                'module' => 'data_anak',
                'description' => 'Menambah data anak: Siti Aminah',
                'metadata' => ['nama_anak' => 'Siti Aminah', 'anak_id' => 1],
                'completed_at' => now()->subDays(1)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'edit',
                'module' => 'data_panti',
                'description' => 'Mengubah profil panti asuhan',
                'metadata' => ['section' => 'identitas_panti'],
                'completed_at' => now()->subDays(2)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'add',
                'module' => 'pengajuan_dana',
                'description' => 'Mengajukan dana untuk pendidikan',
                'metadata' => ['tujuan' => 'pendidikan', 'nominal' => 10000000],
                'completed_at' => now()->subDays(3)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'print',
                'module' => 'laporan_kegiatan',
                'description' => 'Mencetak laporan kegiatan bulanan',
                'metadata' => ['bulan' => 'Juni 2025'],
                'completed_at' => now()->subDays(4)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'add',
                'module' => 'bantuan_sosial',
                'description' => 'Mengajukan bantuan sosial 2025',
                'metadata' => ['tahun' => 2025],
                'completed_at' => now()->subDays(5)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'confirm',
                'module' => 'data_anak',
                'description' => 'Konfirmasi jadwal kunjungan anak',
                'metadata' => ['jadwal' => '2025-07-20'],
                'completed_at' => now()->subDays(6)
            ],
            [
                'user_id' => $userId,
                'activity_type' => 'edit',
                'module' => 'data_anak',
                'description' => 'Mengubah data anak: Ahmad Rizki',
                'metadata' => ['nama_anak' => 'Ahmad Rizki', 'anak_id' => 2],
                'completed_at' => now()->subDays(7)
            ]
        ];

        foreach ($activities as $activity) {
            UserActivity::create($activity);
        }

        $this->command->info('Sample user activities created successfully!');
    }
}
