<?php

require 'vendor/autoload.php';

// Bootstrap Laravel application properly
$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\DokumenVerifikasi;

echo "=== MIGRATING EXISTING DATA ===" . PHP_EOL;

// Cari semua record dengan KTP yang terpisah
$ktpKetua = DokumenVerifikasi::where('dokumen_id', 15)->get();
$ktpSekretaris = DokumenVerifikasi::where('dokumen_id', 16)->get();
$ktpBendahara = DokumenVerifikasi::where('dokumen_id', 17)->get();

echo "Found KTP Ketua: " . $ktpKetua->count() . PHP_EOL;
echo "Found KTP Sekretaris: " . $ktpSekretaris->count() . PHP_EOL;
echo "Found KTP Bendahara: " . $ktpBendahara->count() . PHP_EOL;

// Untuk setiap pengajuan, gabungkan status KTP
$pengajuanIds = DokumenVerifikasi::whereIn('dokumen_id', [15, 16, 17])
    ->distinct()
    ->pluck('pengajuan_bansos_id');

echo PHP_EOL . "Processing " . $pengajuanIds->count() . " pengajuan..." . PHP_EOL;

foreach ($pengajuanIds as $pengajuanId) {
    echo "Processing pengajuan ID: {$pengajuanId}" . PHP_EOL;
    
    // Ambil semua KTP untuk pengajuan ini
    $ktpRecords = DokumenVerifikasi::where('pengajuan_bansos_id', $pengajuanId)
        ->whereIn('dokumen_id', [15, 16, 17])
        ->get();
    
    // Tentukan status gabungan
    $allApproved = $ktpRecords->every(fn($record) => $record->status_verifikasi === 'diterima');
    $anyRejected = $ktpRecords->contains(fn($record) => $record->status_verifikasi === 'ditolak');
    
    $newStatus = 'pending';
    if ($allApproved) {
        $newStatus = 'diterima';
    } elseif ($anyRejected) {
        $newStatus = 'ditolak';
    }
    
    echo "  KTP records found: " . $ktpRecords->count() . PHP_EOL;
    echo "  Combined status: {$newStatus}" . PHP_EOL;
    
    // Hapus record lama
    DokumenVerifikasi::where('pengajuan_bansos_id', $pengajuanId)
        ->whereIn('dokumen_id', [15, 16, 17])
        ->delete();
    
    // Buat record baru yang gabungan
    DokumenVerifikasi::create([
        'pengajuan_bansos_id' => $pengajuanId,
        'dokumen_id' => 15,
        'nama_dokumen' => 'Foto KTP Pengurus (Ketua, Sekretaris, Bendahara)',
        'status_verifikasi' => $newStatus,
    ]);
    
    echo "  ✓ Migrated to combined KTP record" . PHP_EOL;
}

echo PHP_EOL . "=== MIGRATION COMPLETED ===" . PHP_EOL;

// Verifikasi hasil
$newKtpRecords = DokumenVerifikasi::where('dokumen_id', 15)->get();
echo "New combined KTP records: " . $newKtpRecords->count() . PHP_EOL;

foreach ($newKtpRecords as $record) {
    echo "  Pengajuan {$record->pengajuan_bansos_id}: {$record->status_verifikasi}" . PHP_EOL;
}
