import L from 'leaflet';
import { useMemo, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface PantiLocation {
    id: number;
    nama: string;
    alamat: string;
    kabupaten: string;
    lat: number;
    lng: number;
    jumlahAnak: number;
}

interface KabupatenAggregate {
    kabupaten: string;
    lat: number;
    lng: number;
    totalPanti: number;
    totalAnak: number;
}

interface MapComponentProps {
    pantiLocations: PantiLocation[];
}

export default function MapComponent({ pantiLocations }: MapComponentProps) {
    const mapRef = useRef<L.Map | null>(null);

    // Center map on Riau province
    const riauCenter: [number, number] = [0.2933, 101.7068];

    // Agregasi per kabupaten
    const kabupatenAggregates: KabupatenAggregate[] = useMemo(() => {
        const map = new Map<string, { latSum: number; lngSum: number; count: number; totalPanti: number; totalAnak: number }>();
        pantiLocations.forEach((panti) => {
            if (!map.has(panti.kabupaten)) {
                map.set(panti.kabupaten, {
                    latSum: 0,
                    lngSum: 0,
                    count: 0,
                    totalPanti: 0,
                    totalAnak: 0,
                });
            }
            const agg = map.get(panti.kabupaten)!;
            agg.latSum += panti.lat;
            agg.lngSum += panti.lng;
            agg.count += 1;
            agg.totalPanti += 1;
            agg.totalAnak += panti.jumlahAnak;
        });
        return Array.from(map.entries()).map(([kabupaten, agg]) => ({
            kabupaten,
            lat: agg.latSum / agg.count,
            lng: agg.lngSum / agg.count,
            totalPanti: agg.totalPanti,
            totalAnak: agg.totalAnak,
        }));
    }, [pantiLocations]);

    return (
        <div className="h-full w-full">
            <MapContainer center={riauCenter} zoom={8} style={{ height: '100%', width: '100%' }} ref={mapRef}>
                <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                />
                {/* Marker per kabupaten */}
                {kabupatenAggregates.map((kab) => (
                    <Marker key={kab.kabupaten} position={[kab.lat, kab.lng]}>
                        <Popup>
                            <div className="p-2">
                                <h3 className="text-sm font-bold">{kab.kabupaten}</h3>
                                <p className="mb-1 text-xs text-gray-600">Jumlah Panti: {kab.totalPanti}</p>
                                <p className="text-xs font-medium text-blue-600">Total Anak Asuh: {kab.totalAnak}</p>
                            </div>
                        </Popup>
                    </Marker>
                ))}
            </MapContainer>
        </div>
    );
}
