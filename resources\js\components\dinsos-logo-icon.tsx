import React from 'react';

interface LogoProps {
    className?: string;
    width?: number;
    height?: number;
    style?: React.CSSProperties;
}

export default function DinsosProvisnsiRiauLogoIcon({ className, width = 48, height = 48, style, ...props }: LogoProps) {
    return (
        <div className={`${className} relative`} style={{ width, height, ...style }} {...props}>
            <img
                src="/dinsos-logo.png"
                alt="Dinas Sosial Provinsi Riau"
                className="absolute inset-0 h-full w-full rounded-md"
                style={{
                    objectFit: 'contain', // Kembali ke 'contain' agar logo terlihat utuh
                    objectPosition: 'center',
                }}
            />
        </div>
    );
}
