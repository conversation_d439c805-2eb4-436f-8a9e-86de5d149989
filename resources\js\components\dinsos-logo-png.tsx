import React from 'react';

interface LogoProps {
    className?: string;
    width?: number;
    height?: number;
    style?: React.CSSProperties;
}

export default function DinsosProvisnsiRiauLogoIcon({ className, width = 100, height = 100, style, ...props }: LogoProps) {
    return (
        <img
            src="/dinsos-logo.png"
            alt="Dinas Sosial Provinsi Riau"
            className={className}
            width={width}
            height={height}
            style={{ objectFit: 'contain', ...style }}
            {...props}
        />
    );
}

// Alternative: If you want to keep it as SVG but use PNG as background
export function DinsosProvisnsiRiauLogoIconSVG(props: React.SVGAttributes<SVGElement>) {
    return (
        <svg {...props} viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="logoPattern" patternUnits="userSpaceOnUse" width="100" height="100">
                    <image href="/dinsos-logo.png" x="0" y="0" width="100" height="100" />
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#logoPattern)" />
        </svg>
    );
}
