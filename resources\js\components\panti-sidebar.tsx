import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Building2, Calendar, DollarSign, FileText, Folder, LayoutGrid, Users } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/panti/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Data Panti',
        href: '/panti/datapanti',
        icon: Building2,
    },
    {
        title: 'Data Anak',
        href: '/panti/dataanak',
        icon: Users,
    },
    {
        title: 'Pendanaan',
        href: '/panti/pendan<PERSON>',
        icon: DollarSign,
    },
    {
        title: 'Bantu<PERSON>',
        href: '/panti/bansos',
        icon: Folder,
    },
    {
        title: 'Laporan <PERSON>',
        href: '/panti/laporankegiatan',
        icon: FileText,
    },
    {
        title: 'Jadwal Kunjungan',
        href: '/panti/jadwalkunjungan',
        icon: Calendar,
    },
];

const footerNavItems: NavItem[] = [];

export function PantiSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/panti/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
