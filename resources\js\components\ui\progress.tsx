import React from 'react';

interface ProgressProps {
    tahap?: string;
    value?: number;
}

export default function Progress({ tahap, value }: ProgressProps) {
    // Jika menggunakan tahap pengangkatan
    if (tahap) {
        const steps = [
            { key: 'menunggu_verifikasi', label: 'Pengajuan' },
            { key: 'proses_verifikasi', label: 'Verifikasi' },
            { key: 'sidang', label: 'Sidang' },
            { key: 'selesai', label: 'Seles<PERSON>' }
        ];

        const currentIdx = steps.findIndex((s) => s.key === tahap);
        const progressValue = currentIdx >= 0 ? ((currentIdx + 1) / steps.length) * 100 : 0;

        return (
            <div className="space-y-2">
                <div className="h-2 w-full rounded-full bg-gray-200">
                    <div 
                        className="h-2 rounded-full bg-blue-500 transition-all duration-300" 
                        style={{ width: `${progressValue}%` }} 
                    />
                </div>
                <div className="flex justify-between text-xs text-gray-600">
                    {steps.map((step, idx) => (
                        <span 
                            key={step.key} 
                            className={idx === currentIdx ? 'font-bold text-blue-700' : ''}
                        >
                            {step.label}
                        </span>
                    ))}
                </div>
            </div>
        );
    }

    // Jika menggunakan value langsung
    if (value !== undefined) {
        return (
            <div className="h-2 w-full rounded-full bg-gray-200">
                <div 
                    className="h-2 rounded-full bg-blue-500 transition-all duration-300" 
                    style={{ width: `${value}%` }} 
                />
            </div>
        );
    }

    // Default progress bar kosong
    return (
        <div className="h-2 w-full rounded-full bg-gray-200">
            <div className="h-2 rounded-full bg-blue-500 transition-all duration-300" style={{ width: '0%' }} />
        </div>
    );
}
