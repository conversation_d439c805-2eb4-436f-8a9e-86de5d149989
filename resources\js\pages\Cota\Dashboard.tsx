import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Calendar, Clock, MapPin, Users } from 'lucide-react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    modules: {
        cota: boolean;
    };
    applicationStatus: {
        title: string;
        description: string;
        color: string;
        percentage: number;
    } | null;
    progressPercentage: number;
    cotaData: any;
    calonOrangTuaAsuh: any;
    jadwalSidang: Array<{
        id: number;
        tanggal_sidang: string;
        waktu_mulai: string;
        waktu_selesai: string;
        tempat_sidang: string;
        agenda: string;
        status: string;
        kapasitas: number;
        keterangan?: string;
    }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard COTA',
        href: '/cota/dashboard',
    },
];

export default function CotaDashboard({ user, modules, applicationStatus, progressPercentage, cotaData, calonOrangTuaAsuh, jadwalSidang }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard COTA (Calon Orang Tua Asuh)" />

            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {/* Header */}
                <div className="mb-4">
                    <h1 className="mb-2 text-3xl font-bold text-gray-900">Dashboard COTA</h1>
                    <p className="mb-2 text-lg text-gray-600">Calon Orang Tua Asuh</p>
                    <p className="text-gray-600">Selamat datang, {user.name}</p>
                </div>

                {/* User Info Card */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Informasi Pengguna
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div>
                                <p className="text-sm text-gray-500">Nama</p>
                                <p className="font-medium">{user.name}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Email</p>
                                <p className="font-medium">{user.email}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Status</p>
                                <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                                    Calon Orang Tua Asuh
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Stats */}
                <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{applicationStatus ? applicationStatus.percentage : 0}%</div>
                            <p className="text-xs text-muted-foreground">Progress Aplikasi</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{calonOrangTuaAsuh ? calonOrangTuaAsuh.status : 'N/A'}</div>
                            <p className="text-xs text-muted-foreground">Status Terkini</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{cotaData ? '✓' : '✗'}</div>
                            <p className="text-xs text-muted-foreground">Status Pendaftaran</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Application Progress */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle>Progress Aplikasi</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            {/* Progress Bar */}
                            <div className="w-full">
                                <div className="mb-2 flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-700">
                                        Progress: {applicationStatus ? applicationStatus.percentage : 0}%
                                    </span>
                                    <span className="text-sm text-gray-500">{applicationStatus ? applicationStatus.title : 'Loading...'}</span>
                                </div>

                                {/* Progress Bar Container */}
                                <div className="mb-4 h-3 w-full rounded-full bg-gray-200">
                                    <div
                                        className={`h-3 rounded-full transition-all duration-500 ease-out ${
                                            applicationStatus && applicationStatus.color === 'green'
                                                ? 'bg-green-500'
                                                : applicationStatus && applicationStatus.color === 'red'
                                                  ? 'bg-red-500'
                                                  : applicationStatus && applicationStatus.color === 'yellow'
                                                    ? 'bg-yellow-500'
                                                    : 'bg-blue-500'
                                        }`}
                                        style={{ width: `${applicationStatus ? applicationStatus.percentage : 0}%` }}
                                    ></div>
                                </div>
                            </div>

                            {/* Step Indicators */}
                            <div className="grid grid-cols-4 gap-4">
                                {/* Step 1: Pendaftaran */}
                                <div className="text-center">
                                    <div
                                        className={`mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium ${
                                            cotaData ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'
                                        }`}
                                    >
                                        {cotaData ? '✓' : '1'}
                                    </div>
                                    <div className="text-xs">
                                        <p className={`font-medium ${cotaData ? 'text-green-600' : 'text-gray-500'}`}>Pendaftaran</p>
                                        <p className="text-gray-500">{cotaData ? 'Selesai' : 'Belum'}</p>
                                    </div>
                                </div>

                                {/* Step 2: Upload Dokumen */}
                                <div className="text-center">
                                    <div
                                        className={`mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium ${
                                            cotaData ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'
                                        }`}
                                    >
                                        {cotaData ? '✓' : '2'}
                                    </div>
                                    <div className="text-xs">
                                        <p className={`font-medium ${cotaData ? 'text-green-600' : 'text-gray-500'}`}>Dokumen</p>
                                        <p className="text-gray-500">{cotaData ? 'Lengkap' : 'Belum'}</p>
                                    </div>
                                </div>

                                {/* Step 3: Review */}
                                <div className="text-center">
                                    <div
                                        className={`mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium ${
                                            calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima'
                                                ? 'bg-green-500 text-white'
                                                : calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Ditolak'
                                                  ? 'bg-red-500 text-white'
                                                  : calonOrangTuaAsuh
                                                    ? 'bg-blue-500 text-white'
                                                    : 'bg-gray-300 text-gray-600'
                                        }`}
                                    >
                                        {calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima'
                                            ? '✓'
                                            : calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Ditolak'
                                              ? '✗'
                                              : '3'}
                                    </div>
                                    <div className="text-xs">
                                        <p
                                            className={`font-medium ${
                                                calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima'
                                                    ? 'text-green-600'
                                                    : calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Ditolak'
                                                      ? 'text-red-600'
                                                      : calonOrangTuaAsuh
                                                        ? 'text-blue-600'
                                                        : 'text-gray-500'
                                            }`}
                                        >
                                            Review
                                        </p>
                                        <p className="text-gray-500">
                                            {calonOrangTuaAsuh
                                                ? calonOrangTuaAsuh.status === 'Diterima'
                                                    ? 'Disetujui'
                                                    : calonOrangTuaAsuh.status === 'Ditolak'
                                                      ? 'Ditolak'
                                                      : 'Proses'
                                                : 'Pending'}
                                        </p>
                                    </div>
                                </div>

                                {/* Step 4: Sertifikasi */}
                                <div className="text-center">
                                    <div
                                        className={`mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium ${
                                            calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima'
                                                ? 'bg-green-500 text-white'
                                                : 'bg-gray-300 text-gray-600'
                                        }`}
                                    >
                                        {calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima' ? '✓' : '4'}
                                    </div>
                                    <div className="text-xs">
                                        <p
                                            className={`font-medium ${
                                                calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima' ? 'text-green-600' : 'text-gray-500'
                                            }`}
                                        >
                                            Sertifikasi
                                        </p>
                                        <p className="text-gray-500">
                                            {calonOrangTuaAsuh && calonOrangTuaAsuh.status === 'Diterima' ? 'Selesai' : 'Pending'}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Current Status Description */}
                            {applicationStatus && (
                                <div
                                    className={`rounded-lg p-3 ${
                                        applicationStatus.color === 'green'
                                            ? 'border border-green-200 bg-green-50'
                                            : applicationStatus.color === 'red'
                                              ? 'border border-red-200 bg-red-50'
                                              : applicationStatus.color === 'yellow'
                                                ? 'border border-yellow-200 bg-yellow-50'
                                                : 'border border-blue-200 bg-blue-50'
                                    }`}
                                >
                                    <p
                                        className={`text-sm font-medium ${
                                            applicationStatus.color === 'green'
                                                ? 'text-green-800'
                                                : applicationStatus.color === 'red'
                                                  ? 'text-red-800'
                                                  : applicationStatus.color === 'yellow'
                                                    ? 'text-yellow-800'
                                                    : 'text-blue-800'
                                        }`}
                                    >
                                        {applicationStatus.description}
                                    </p>
                                    {calonOrangTuaAsuh && calonOrangTuaAsuh.keterangan && (
                                        <p className="mt-1 text-xs opacity-75">Catatan: {calonOrangTuaAsuh.keterangan}</p>
                                    )}
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Jadwal Sidang */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Jadwal Sidang Terbaru
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {jadwalSidang && jadwalSidang.length > 0 ? (
                            <div className="space-y-4">
                                {jadwalSidang.map((jadwal) => (
                                    <div
                                        key={jadwal.id}
                                        className="flex items-start justify-between rounded-lg border border-gray-200 p-4 hover:bg-gray-50"
                                    >
                                        <div className="flex-1">
                                            <div className="mb-2 flex items-center gap-2">
                                                <h4 className="font-medium text-gray-900">{jadwal.agenda || 'Sidang COTA'}</h4>
                                                <Badge
                                                    className={
                                                        jadwal.status === 'final'
                                                            ? 'bg-blue-100 text-blue-800'
                                                            : jadwal.status === 'selesai'
                                                              ? 'bg-green-100 text-green-800'
                                                              : jadwal.status === 'batal'
                                                                ? 'bg-red-100 text-red-800'
                                                                : 'bg-yellow-100 text-yellow-800'
                                                    }
                                                >
                                                    {jadwal.status}
                                                </Badge>
                                            </div>
                                            <div className="space-y-1 text-sm text-gray-600">
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4" />
                                                    <span>
                                                        {new Date(jadwal.tanggal_sidang).toLocaleDateString('id-ID', {
                                                            weekday: 'long',
                                                            year: 'numeric',
                                                            month: 'long',
                                                            day: 'numeric',
                                                        })}
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Clock className="h-4 w-4" />
                                                    <span>
                                                        {jadwal.waktu_mulai} - {jadwal.waktu_selesai}
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <MapPin className="h-4 w-4" />
                                                    <span>{jadwal.tempat_sidang}</span>
                                                </div>
                                                {jadwal.keterangan && (
                                                    <div className="mt-2">
                                                        <p className="text-xs text-gray-500">
                                                            <strong>Keterangan:</strong> {jadwal.keterangan}
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-sm font-medium text-gray-900">Kapasitas: {jadwal.kapasitas}</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="py-8 text-center">
                                <Calendar className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                <p className="text-gray-500">Belum ada jadwal sidang yang tersedia</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
