// Move useForm, errors, and handleFileChange above JSX usage
// ...existing imports...

// (Removed duplicate UI component imports)
// (Removed duplicate AppLayout, BreadcrumbItem, api, and inertia imports)

// ...other code...

// useForm and errors definition (already present, just ensure it's above the JSX)
// handleFileChange definition (already present, just ensure it's above the JSX)

// ...existing code...

// (Moved file upload JSX for dokumen utama inside DaftarCota component, after useForm, errors, and handleFileChange are defined)
// Daftar dokumen persyaratan untuk modal detail (27 dokumen sesuai spesifikasi)
const dokumenPersyaratanList = [
    { field: 'rekomendasi_dinsos', label: 'Rekomendasi di Dinas Sosial Kabupaten/Kota' },
    { field: 'permohonan_pemohon', label: 'Permohonan dan <PERSON>emohon' },
    { field: 'surat_sehat_rs', label: 'Surat Keterangan Berbadan Sehat dari Rumah <PERSON>' },
    { field: 'surat_kesehatan_jiwa', label: 'Surat keterangan Kesehatan Jiwa dari Dokter Spesialis Jiwa dari Rumah Sakit Pemerintah (Suami/Istri)' },
    {
        field: 'surat_fungsi_reproduksi',
        label: 'Surat Keterangan tentang Fungsi Organ Reproduksi (Suami/lstri) dari Dokter Spesialis Obstetric dan Gineologi Rumah Sakit Pemerintah',
    },
    { field: 'akta_kelahiran_cota', label: 'Foto Copy Akta Kelahiran COTA' },
    { field: 'surat_catatan_kepolisian', label: 'Surat Keterangan Catatan Kepolisian Setempat' },
    { field: 'akta_nikah_cota', label: 'Foto Copy Nikah/Kutipan Akte Nikah COTA' },
    { field: 'kk_ktp_cota', label: 'Foto Copy Kartu Keluarga dan KTP COTA' },
    { field: 'akta_kelahiran_caa', label: 'Foto Copy Akta Kelahiran CAA' },
    { field: 'surat_penghasilan_cota', label: 'Surat Keterangan Penghasilan dari tempat bekerja COTA' },
    {
        field: 'surat_persetujuan_caa',
        label: 'Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial',
    },
    {
        field: 'surat_motivasi_cota',
        label: 'Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan Anak',
    },
    { field: 'surat_persetujuan_keluarga', label: 'Surat Pernyataan Persetujuan Keluarga COTA' },
    { field: 'surat_domisili', label: 'Surat Keterangan Domisili' },
    { field: 'surat_keterangan_sehat', label: 'Surat Keterangan Sehat' },
    { field: 'surat_tidak_dihukum', label: 'Surat Keterangan Tidak Pernah Dihukum' },
    { field: 'surat_tidak_narkoba', label: 'Surat Keterangan Tidak Pernah Terlibat Narkoba' },
    { field: 'surat_pernyataan_cota', label: 'Surat Pernyataan Kesanggupan untuk memelihara, mendidik, dan membesarkan CAA' },
    { field: 'surat_pernyataan_caa', label: 'Surat Pernyataan CAA' },
    { field: 'surat_pernyataan_keluarga', label: 'Surat Pernyataan Keluarga' },
    { field: 'surat_pernyataan_tidak_menelantarkan', label: 'Surat Pernyataan Tidak Menelantarkan' },
    { field: 'surat_pernyataan_tidak_memisahkan', label: 'Surat Pernyataan Tidak Memisahkan' },
    { field: 'surat_pernyataan_tidak_mengeksploitasi', label: 'Surat Pernyataan Tidak Mengeksploitasi' },
    { field: 'surat_pernyataan_tidak_memperjualbelikan', label: 'Surat Pernyataan Tidak Memperjualbelikan' },
    { field: 'surat_pernyataan_tidak_mengubah_identitas', label: 'Surat Pernyataan Tidak Mengubah Identitas' },
    { field: 'surat_pernyataan_tidak_mengubah_agama', label: 'Surat Pernyataan Tidak Mengubah Agama' },
    { field: 'surat_pernyataan_tidak_mengubah_kewarganegaraan', label: 'Surat Pernyataan Tidak Mengubah Kewarganegaraan' },
    { field: 'surat_pernyataan_tidak_mengubah_nama', label: 'Surat Pernyataan Tidak Mengubah Nama' },
];
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { getCotaProsesVerifikasiList, getCotaTersertifikasiList } from '@/utils/api';
import { Head, router, useForm } from '@inertiajs/react';
import { ArrowLeft, ChevronLeft, ChevronRight, Download, Eye, FileText, Trash2, Upload, UserPlus, Users, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    cotas: Array<{
        id: number;
        nama: string;
        email: string;
        alamat: string;
        status: string;
        tanggal_daftar: string;
    }>;
    pantis?: Array<{
        id: number;
        nama: string;
        alamat: string;
        pimpinan: string;
    }>;
    anaks?: Array<{
        id: number;
        nama_lengkap: string;
        usia: number;
        jenis_kelamin: string;
        status_anak: string;
        panti_id: number;
        panti_nama: string;
    }>;
    flash?: {
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dinsoskota/dashboard',
    },
    {
        title: 'Daftar COTA',
        href: '/dinsoskota/daftarcota',
    },
];

export default function DaftarCota({ user, cotas, pantis = [], anaks = [], flash }: Props) {
    // State for jumlah diterima & proses (from backend status)
    const [jumlahDiterima, setJumlahDiterima] = useState<number>(0);
    const [jumlahProses, setJumlahProses] = useState<number>(0);

    const [activeTab, setActiveTab] = useState('list');
    const [selectedCota, setSelectedCota] = useState<any>(null);
    const [showViewModal, setShowViewModal] = useState(false);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10); // You can make this configurable

    // Debug: Log the received data
    console.log('DaftarCota - Received data:', { pantis, anaks, cotas });
    const [editingCotaId, setEditingCotaId] = useState<number | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<{ id: number; name: string } | null>(null);

    // Phone validation state
    const [phoneError, setPhoneError] = useState<string>('');

    // Fetch jumlah diterima & proses dari endpoint baru
    useEffect(() => {
        getCotaTersertifikasiList()
            .then((res) => {
                if (res && res.data && Array.isArray(res.data.data)) {
                    setJumlahDiterima(res.data.data.length);
                } else {
                    setJumlahDiterima(0);
                }
            })
            .catch(() => setJumlahDiterima(0));
        getCotaProsesVerifikasiList()
            .then((res) => {
                if (res && res.data && Array.isArray(res.data.data)) {
                    setJumlahProses(res.data.data.length);
                } else {
                    setJumlahProses(0);
                }
            })
            .catch(() => setJumlahProses(0));
    }, []);

    // Helper function to format file size
    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Handler untuk setiap perubahan file input (validasi 10MB per file & tipe file)
    // Handler untuk setiap perubahan file input (validasi 10MB per file & tipe file)

    useEffect(() => {
        if (flash?.success) {
            alert(flash.success);
        }
        if (flash?.error) {
            alert(flash.error);
        }
    }, [flash]);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        // Informasi Personal
        nama: '',
        nik: '',
        telepon: '',
        email: '',
        password: '',
        password_confirmation: '',
        kabupaten_kota: '',

        // Pilihan Anak
        pilihan_anak: '', // 'dari_panti' atau 'luar_panti'
        anak_id: '',
        panti_id: '',

        // Dokumen Utama (WAJIB, sesuai backend)
        ktp: null as File | null,
        kartu_keluarga: null as File | null,
        surat_pernikahan: null as File | null,
        slip_gaji: null as File | null,

        // Dokumen Persyaratan (27 dokumen sesuai spesifikasi)
        rekomendasi_dinsos: null as File | null,
        permohonan_pemohon: null as File | null,
        surat_sehat_rs: null as File | null,
        surat_kesehatan_jiwa: null as File | null,
        surat_fungsi_reproduksi: null as File | null,
        akta_kelahiran_cota: null as File | null,
        surat_catatan_kepolisian: null as File | null,
        akta_nikah_cota: null as File | null,
        kk_ktp_cota: null as File | null,
        akta_kelahiran_caa: null as File | null,
        surat_penghasilan_cota: null as File | null,
        surat_persetujuan_caa: null as File | null,
        surat_motivasi_cota: null as File | null,
        surat_persetujuan_keluarga: null as File | null,
        surat_domisili: null as File | null,
        surat_keterangan_sehat: null as File | null,
        surat_tidak_dihukum: null as File | null,
        surat_tidak_narkoba: null as File | null,
        surat_pernyataan_cota: null as File | null,
        surat_pernyataan_caa: null as File | null,
        surat_pernyataan_keluarga: null as File | null,
        surat_pernyataan_tidak_menelantarkan: null as File | null,
        surat_pernyataan_tidak_memisahkan: null as File | null,
        surat_pernyataan_tidak_mengeksploitasi: null as File | null,
        surat_pernyataan_tidak_memperjualbelikan: null as File | null,
        surat_pernyataan_tidak_mengubah_identitas: null as File | null,
        surat_pernyataan_tidak_mengubah_agama: null as File | null,
        surat_pernyataan_tidak_mengubah_kewarganegaraan: null as File | null,
        surat_pernyataan_tidak_mengubah_nama: null as File | null,
    });

    const handleBackToDashboard = () => {
        router.get('/dinsoskota/dashboard');
    };

    const handleViewDetail = (id: number) => {
        const cota = cotas.find((c) => c.id === id);
        if (cota) {
            setSelectedCota(cota);
            setShowViewModal(true);
        }
    };

    const handleEdit = async (id: number) => {
        // Fetch COTA data from backend for the given id (JSON endpoint)
        try {
            const res = await fetch(`/dinsoskota/daftarcota/${id}`, { headers: { Accept: 'application/json' } });
            if (!res.ok) throw new Error('Gagal mengambil data COTA');
            const cota = await res.json();
            setEditingCotaId(id);

            // Set basic data
            setData((prev) => ({
                ...prev,
                nama: cota.nama || '',
                nik: cota.nik || '',
                telepon: cota.telepon || '',
                email: cota.email || '',
                password: '',
                password_confirmation: '',
                kabupaten_kota: cota.alamat || '',
                pilihan_anak: prev.pilihan_anak, // Keep current selection
                anak_id: prev.anak_id, // Keep current selection
                panti_id: prev.panti_id, // Keep current selection

                // Reset dokumen utama (wajib) - user harus upload ulang jika ingin mengubah
                ktp: null,
                kartu_keluarga: null,
                surat_pernikahan: null,
                slip_gaji: null,

                // Reset dokumen persyaratan - user harus upload ulang jika ingin mengubah
                rekomendasi_dinsos: null,
                permohonan_pemohon: null,
                surat_sehat_rs: null,
                surat_kesehatan_jiwa: null,
                surat_fungsi_reproduksi: null,
                akta_kelahiran_cota: null,
                surat_catatan_kepolisian: null,
                akta_nikah_cota: null,
                kk_ktp_cota: null,
                akta_kelahiran_caa: null,
                surat_penghasilan_cota: null,
                surat_persetujuan_caa: null,
                surat_motivasi_cota: null,
                surat_persetujuan_keluarga: null,
                surat_domisili: null,
                surat_keterangan_sehat: null,
                surat_tidak_dihukum: null,
                surat_tidak_narkoba: null,
                surat_pernyataan_cota: null,
                surat_pernyataan_caa: null,
                surat_pernyataan_keluarga: null,
                surat_pernyataan_tidak_menelantarkan: null,
                surat_pernyataan_tidak_memisahkan: null,
                surat_pernyataan_tidak_mengeksploitasi: null,
                surat_pernyataan_tidak_memperjualbelikan: null,
                surat_pernyataan_tidak_mengubah_identitas: null,
                surat_pernyataan_tidak_mengubah_agama: null,
                surat_pernyataan_tidak_mengubah_kewarganegaraan: null,
                surat_pernyataan_tidak_mengubah_nama: null,
            }));

            setActiveTab('register');
        } catch (err) {
            alert('Gagal mengambil data COTA. Silakan coba lagi.');
        }
    };

    const handleDelete = (id: number, name: string) => {
        setShowDeleteConfirm({ id, name });
    };

    const confirmDelete = () => {
        if (showDeleteConfirm) {
            router.delete(`/dinsoskota/daftarcota/${showDeleteConfirm.id}`, {
                onSuccess: () => {
                    setShowDeleteConfirm(null);
                    alert('COTA berhasil dihapus.');
                },
                onError: (errors) => {
                    console.error('Delete error:', errors);
                    alert('Gagal menghapus COTA. Silakan coba lagi.');
                },
            });
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Check for phone validation error
        if (phoneError) {
            alert('Mohon perbaiki error pada nomor telepon sebelum submit.');
            return;
        }

        // Calculate total file size before submission (27 dokumen sesuai spesifikasi)
        const files = [
            data.rekomendasi_dinsos,
            data.permohonan_pemohon,
            data.surat_sehat_rs,
            data.surat_kesehatan_jiwa,
            data.surat_fungsi_reproduksi,
            data.akta_kelahiran_cota,
            data.surat_catatan_kepolisian,
            data.akta_nikah_cota,
            data.kk_ktp_cota,
            data.akta_kelahiran_caa,
            data.surat_penghasilan_cota,
            data.surat_persetujuan_caa,
            data.surat_motivasi_cota,
            data.surat_persetujuan_keluarga,
            data.surat_domisili,
            data.surat_keterangan_sehat,
            data.surat_tidak_dihukum,
            data.surat_tidak_narkoba,
            data.surat_pernyataan_cota,
            data.surat_pernyataan_caa,
            data.surat_pernyataan_keluarga,
            data.surat_pernyataan_tidak_menelantarkan,
            data.surat_pernyataan_tidak_memisahkan,
            data.surat_pernyataan_tidak_mengeksploitasi,
            data.surat_pernyataan_tidak_memperjualbelikan,
            data.surat_pernyataan_tidak_mengubah_identitas,
            data.surat_pernyataan_tidak_mengubah_agama,
            data.surat_pernyataan_tidak_mengubah_kewarganegaraan,
            data.surat_pernyataan_tidak_mengubah_nama,
        ];
        const totalSize = files.reduce((sum, file) => sum + (file ? file.size : 0), 0);
        const maxTotalSize = 600 * 1024 * 1024; // 600MB total limit dengan konfigurasi baru
        const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(1);

        if (totalSize > maxTotalSize) {
            alert(
                `Total ukuran file terlalu besar (${totalSizeMB}MB). Maksimal total ukuran semua file adalah 600MB. ` +
                    `Silakan kompres atau pilih file yang lebih kecil untuk menghindari error upload.`,
            );
            return;
        }

        // Warning jika mendekati batas
        if (totalSize > 200 * 1024 * 1024) {
            // 200MB
            const confirmSubmit = confirm(
                `Total ukuran file cukup besar (${totalSizeMB}MB). ` +
                    `Upload mungkin membutuhkan waktu lama dan koneksi internet yang stabil. ` +
                    `Apakah Anda yakin ingin melanjutkan?`,
            );
            if (!confirmSubmit) {
                return;
            }
        }

        if (editingCotaId) {
            // Update existing COTA
            put(`/dinsoskota/daftarcota/${editingCotaId}`, {
                onSuccess: () => {
                    reset();
                    setPhoneError('');
                    setEditingCotaId(null);
                    setActiveTab('list');
                },
                onError: (errors) => {
                    console.error('Update error:', errors);
                    if (typeof errors === 'object' && 'message' in errors) {
                        alert(`Gagal mengupdate COTA: ${errors.message}`);
                    } else {
                        alert('Gagal mengupdate COTA. Data terlalu besar atau terjadi kesalahan. Silakan coba lagi dengan file yang lebih kecil.');
                    }
                },
            });
        } else {
            // Create new COTA
            post('/dinsoskota/daftarcota/register', {
                onSuccess: () => {
                    reset();
                    setPhoneError('');
                    setActiveTab('list');
                },
                onError: (errors) => {
                    console.error('Registration error:', errors);
                    if (typeof errors === 'object' && 'message' in errors) {
                        const message = errors.message as string;
                        if (message.includes('POST Content-Length') || message.includes('too large')) {
                            alert(
                                'Gagal mendaftar COTA: Total ukuran file terlalu besar. ' +
                                    'Silakan kompres file atau pilih file yang lebih kecil (maksimal 12MB per file, total 300MB). ' +
                                    'Pastikan koneksi internet stabil dan coba lagi.',
                            );
                        } else {
                            alert(`Gagal mendaftar COTA: ${message}`);
                        }
                    } else {
                        alert(
                            'Gagal mendaftar COTA. Kemungkinan file terlalu besar atau terjadi kesalahan. ' +
                                'Silakan coba lagi dengan file yang lebih kecil.',
                        );
                    }
                },
            });
        }
    };

    const handleFileChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;

        if (file) {
            // Check file size (max 20MB per file dengan konfigurasi baru)
            const maxSize = 20 * 1024 * 1024; // 20MB in bytes
            if (file.size > maxSize) {
                alert(`File ${file.name} terlalu besar. Maksimal ukuran file adalah 20MB. Silakan kompres file terlebih dahulu.`);
                e.target.value = ''; // Reset the input
                return;
            }

            // Check file type (accept common document formats)
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            if (!allowedTypes.includes(file.type)) {
                alert(`Tipe file ${file.name} tidak didukung. Gunakan format JPG, PNG, atau PDF.`);
                e.target.value = ''; // Reset the input
                return;
            }

            // Warning untuk file besar
            if (file.size > 15 * 1024 * 1024) {
                // 15MB
                const confirmUpload = confirm(
                    `File ${file.name} berukuran ${(file.size / (1024 * 1024)).toFixed(1)}MB. ` +
                        `File besar dapat menyebabkan upload lambat. Apakah Anda yakin ingin melanjutkan? ` +
                        `Disarankan untuk mengompres file jika memungkinkan.`,
                );
                if (!confirmUpload) {
                    e.target.value = ''; // Reset the input
                    return;
                }
            }
        }

        setData(field as any, file);
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        const digitsOnly = value.replace(/\D/g, '');

        if (digitsOnly.length > 13) {
            setPhoneError('Nomor telepon maksimal 13 angka');
        } else {
            setPhoneError('');
            setData('telepon', value);
        }
    };

    const getStatusBadge = (status: string) => {
        const statusClasses: Record<string, string> = {
            diproses: 'bg-blue-100 text-blue-800',
            diterima: 'bg-green-100 text-green-800',
            ditolak: 'bg-red-100 text-red-800',
        };

        return `inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${statusClasses[status] || 'bg-gray-100 text-gray-800'}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID');
    };

    // Pagination functions
    const totalPages = Math.ceil(cotas.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentCotas = cotas.slice(startIndex, endIndex);

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    const goToPreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const getPageNumbers = () => {
        const pageNumbers = [];
        const maxVisiblePages = 5;

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pageNumbers.push(i);
            }
        } else {
            if (currentPage <= 3) {
                for (let i = 1; i <= 4; i++) {
                    pageNumbers.push(i);
                }
                pageNumbers.push('...');
                pageNumbers.push(totalPages);
            } else if (currentPage >= totalPages - 2) {
                pageNumbers.push(1);
                pageNumbers.push('...');
                for (let i = totalPages - 3; i <= totalPages; i++) {
                    pageNumbers.push(i);
                }
            } else {
                pageNumbers.push(1);
                pageNumbers.push('...');
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pageNumbers.push(i);
                }
                pageNumbers.push('...');
                pageNumbers.push(totalPages);
            }
        }

        return pageNumbers;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Daftar COTA - Dinas Sosial Kabupaten/Kota" />

            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {/* Header */}
                <div className="mb-4 flex items-center justify-between">
                    <div>
                        <h1 className="mb-2 text-3xl font-bold text-gray-900">Daftar COTA</h1>
                        <p className="text-gray-600">Kelola daftar Calon Orang Tua Asuh di kabupaten/kota</p>
                    </div>
                    <Button variant="outline" onClick={handleBackToDashboard} className="flex items-center gap-2">
                        <ArrowLeft className="h-4 w-4" />
                        Kembali ke Dashboard
                    </Button>
                </div>

                {/* Tabs */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="list" className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            Daftar COTA
                        </TabsTrigger>
                        <TabsTrigger value="register" className="flex items-center gap-2">
                            <UserPlus className="h-4 w-4" />
                            {editingCotaId ? 'Edit COTA' : 'Daftarkan COTA'}
                        </TabsTrigger>
                    </TabsList>

                    {/* Daftar COTA Tab */}
                    <TabsContent value="list">
                        {/* Stats Cards */}
                        <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm text-gray-600">Total COTA</p>
                                            <p className="text-2xl font-bold text-gray-900">{cotas.length}</p>
                                        </div>
                                        <Users className="h-8 w-8 text-blue-600" />
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm text-gray-600">Diterima</p>
                                            <p className="text-2xl font-bold text-green-600">{jumlahDiterima}</p>
                                        </div>
                                        <FileText className="h-8 w-8 text-green-600" />
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm text-gray-600">Diproses</p>
                                            <p className="text-2xl font-bold text-blue-600">{jumlahProses}</p>
                                        </div>
                                        <FileText className="h-8 w-8 text-blue-600" />
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* COTA Table */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Daftar Calon Orang Tua Asuh</CardTitle>
                                <CardDescription>Daftar lengkap COTA yang terdaftar di kabupaten/kota</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>No</TableHead>
                                            <TableHead>Nama</TableHead>
                                            <TableHead>Email</TableHead>
                                            <TableHead>Alamat</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Tanggal Daftar</TableHead>
                                            <TableHead className="text-center">Aksi</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {currentCotas.map((cota, index) => (
                                            <TableRow key={cota.id}>
                                                <TableCell className="font-medium">{startIndex + index + 1}</TableCell>
                                                <TableCell className="font-medium">{cota.nama}</TableCell>
                                                <TableCell>{cota.email}</TableCell>
                                                <TableCell>{cota.alamat}</TableCell>
                                                <TableCell>
                                                    <span className={getStatusBadge(cota.status)}>{cota.status}</span>
                                                </TableCell>
                                                <TableCell>{formatDate(cota.tanggal_daftar)}</TableCell>
                                                <TableCell className="text-center">
                                                    <div className="flex items-center justify-center gap-2">
                                                        <Button
                                                            size="icon"
                                                            variant="outline"
                                                            className="h-8 w-8 border-blue-500 text-blue-600 hover:bg-blue-50"
                                                            title="Lihat Detail"
                                                            onClick={() => handleViewDetail(cota.id)}
                                                        >
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            size="icon"
                                                            variant="outline"
                                                            className="h-8 w-8 border-green-500 text-green-600 hover:bg-green-50"
                                                            title="Edit Dokumen"
                                                            onClick={() => handleEdit(cota.id)}
                                                        >
                                                            <FileText className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            size="icon"
                                                            variant="outline"
                                                            className="h-8 w-8 border-red-500 text-red-600 hover:bg-red-50"
                                                            title="Hapus COTA"
                                                            onClick={() => handleDelete(cota.id, cota.nama)}
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>

                                {/* Pagination Controls - Always show */}
                                <div className="mt-6 flex items-center justify-between">
                                    <div className="text-sm text-gray-700">
                                        {cotas.length > 0
                                            ? `Menampilkan ${startIndex + 1} sampai ${Math.min(endIndex, cotas.length)} dari ${cotas.length} data`
                                            : 'Tidak ada data'}
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={goToPreviousPage}
                                            disabled={currentPage === 1 || cotas.length === 0}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            Previous
                                        </Button>

                                        <div className="flex items-center space-x-1">
                                            {cotas.length > 0 ? (
                                                getPageNumbers().map((pageNumber, index) => (
                                                    <React.Fragment key={index}>
                                                        {pageNumber === '...' ? (
                                                            <span className="px-3 py-2 text-gray-500">...</span>
                                                        ) : (
                                                            <Button
                                                                variant={currentPage === pageNumber ? 'default' : 'outline'}
                                                                size="sm"
                                                                onClick={() => goToPage(pageNumber as number)}
                                                                className="min-w-[40px]"
                                                            >
                                                                {pageNumber}
                                                            </Button>
                                                        )}
                                                    </React.Fragment>
                                                ))
                                            ) : (
                                                <Button variant="default" size="sm" disabled className="min-w-[40px]">
                                                    1
                                                </Button>
                                            )}
                                        </div>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={goToNextPage}
                                            disabled={currentPage === totalPages || cotas.length === 0}
                                            className="flex items-center gap-1"
                                        >
                                            Next
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Registration Form Tab */}
                    <TabsContent value="register">
                        <form onSubmit={handleSubmit} encType="multipart/form-data">
                            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                                {/* Personal Information Card */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Informasi Personal</CardTitle>
                                        <CardDescription>Data pribadi calon orang tua asuh</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="nama">Nama Lengkap</Label>
                                            <Input
                                                id="nama"
                                                value={data.nama}
                                                onChange={(e) => setData('nama', e.target.value)}
                                                placeholder="Masukkan nama lengkap"
                                                required
                                            />
                                            {errors.nama && <p className="text-sm text-red-500">{errors.nama}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="nik">NIK</Label>
                                            <Input
                                                id="nik"
                                                value={data.nik}
                                                onChange={(e) => setData('nik', e.target.value)}
                                                placeholder="Masukkan NIK"
                                                maxLength={16}
                                                required
                                            />
                                            {errors.nik && <p className="text-sm text-red-500">{errors.nik}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="telepon">Nomor Telepon</Label>
                                            <Input
                                                id="telepon"
                                                value={data.telepon}
                                                onChange={handlePhoneChange}
                                                placeholder="Masukkan nomor telepon"
                                                maxLength={15}
                                                required
                                            />
                                            {phoneError && <p className="text-sm text-red-500">{phoneError}</p>}
                                            {errors.telepon && <p className="text-sm text-red-500">{errors.telepon}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email">Email</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                placeholder="Masukkan email"
                                                required
                                            />
                                            {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="password">Password</Label>
                                            <Input
                                                id="password"
                                                type="password"
                                                value={data.password}
                                                onChange={(e) => setData('password', e.target.value)}
                                                placeholder="Masukkan password"
                                                required
                                                minLength={8}
                                            />
                                            {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="password_confirmation">Konfirmasi Password</Label>
                                            <Input
                                                id="password_confirmation"
                                                type="password"
                                                value={data.password_confirmation}
                                                onChange={(e) => setData('password_confirmation', e.target.value)}
                                                placeholder="Konfirmasi password"
                                                required
                                                minLength={8}
                                            />
                                            {errors.password_confirmation && <p className="text-sm text-red-500">{errors.password_confirmation}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="kabupaten_kota">Kabupaten/Kota</Label>
                                            <Select value={data.kabupaten_kota} onValueChange={(value) => setData('kabupaten_kota', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Pilih kabupaten/kota" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="pekanbaru">Kota Pekanbaru</SelectItem>
                                                    <SelectItem value="dumai">Kota Dumai</SelectItem>
                                                    <SelectItem value="kampar">Kabupaten Kampar</SelectItem>
                                                    <SelectItem value="rokan_hulu">Kabupaten Rokan Hulu</SelectItem>
                                                    <SelectItem value="rokan_hilir">Kabupaten Rokan Hilir</SelectItem>
                                                    <SelectItem value="siak">Kabupaten Siak</SelectItem>
                                                    <SelectItem value="bengkalis">Kabupaten Bengkalis</SelectItem>
                                                    <SelectItem value="indragiri_hulu">Kabupaten Indragiri Hulu</SelectItem>
                                                    <SelectItem value="indragiri_hilir">Kabupaten Indragiri Hilir</SelectItem>
                                                    <SelectItem value="pelalawan">Kabupaten Pelalawan</SelectItem>
                                                    <SelectItem value="kuantan_singingi">Kabupaten Kuantan Singingi</SelectItem>
                                                    <SelectItem value="kepulauan_meranti">Kabupaten Kepulauan Meranti</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.kabupaten_kota && <p className="text-sm text-red-500">{errors.kabupaten_kota}</p>}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Child Selection Card */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Pilihan Anak Asuh</CardTitle>
                                        <CardDescription>Pilih apakah ingin mengangkat anak dari panti atau dari luar panti</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="pilihan_anak">Pilihan Sumber Anak</Label>
                                            <Select
                                                value={data.pilihan_anak}
                                                onValueChange={(value) => {
                                                    setData('pilihan_anak', value);
                                                    // Reset selections when changing option
                                                    setData('anak_id', '');
                                                    setData('panti_id', '');
                                                }}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Pilih sumber anak asuh" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="dari_panti">Dari Panti Asuhan</SelectItem>
                                                    <SelectItem value="luar_panti">Dari Luar Panti</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.pilihan_anak && <p className="text-sm text-red-500">{errors.pilihan_anak}</p>}
                                        </div>

                                        {data.pilihan_anak === 'dari_panti' && (
                                            <>
                                                <div className="space-y-2">
                                                    <Label htmlFor="panti_id">Pilih Panti Asuhan</Label>
                                                    <Select
                                                        value={data.panti_id}
                                                        onValueChange={(value) => {
                                                            setData('panti_id', value);
                                                            setData('anak_id', ''); // Reset child selection when panti changes
                                                        }}
                                                    >
                                                        <SelectTrigger>
                                                            <SelectValue placeholder="Pilih panti asuhan" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {pantis.length > 0 ? (
                                                                pantis.map((panti) => (
                                                                    <SelectItem key={panti.id} value={panti.id.toString()}>
                                                                        {panti.nama} - {panti.alamat}
                                                                    </SelectItem>
                                                                ))
                                                            ) : (
                                                                <SelectItem value="" disabled>
                                                                    Tidak ada panti tersedia
                                                                </SelectItem>
                                                            )}
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.panti_id && <p className="text-sm text-red-500">{errors.panti_id}</p>}
                                                </div>

                                                {data.panti_id && (
                                                    <div className="space-y-2">
                                                        <Label htmlFor="anak_id">Pilih Anak Asuh</Label>
                                                        <Select value={data.anak_id} onValueChange={(value) => setData('anak_id', value)}>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Pilih anak asuh" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {anaks
                                                                    .filter(
                                                                        (anak) =>
                                                                            anak.panti_id.toString() === data.panti_id &&
                                                                            ['Tersedia', 'aktif'].includes(anak.status_anak),
                                                                    )
                                                                    .map((anak) => (
                                                                        <SelectItem key={anak.id} value={anak.id.toString()}>
                                                                            {anak.nama_lengkap} ({anak.usia} tahun, {anak.jenis_kelamin})
                                                                        </SelectItem>
                                                                    ))}
                                                                {anaks.filter(
                                                                    (anak) =>
                                                                        anak.panti_id.toString() === data.panti_id &&
                                                                        ['Tersedia', 'aktif'].includes(anak.status_anak),
                                                                ).length === 0 && (
                                                                    <SelectItem value="" disabled>
                                                                        Tidak ada anak tersedia di panti ini
                                                                    </SelectItem>
                                                                )}
                                                            </SelectContent>
                                                        </Select>
                                                        {errors.anak_id && <p className="text-sm text-red-500">{errors.anak_id}</p>}

                                                        {data.anak_id && (
                                                            <div className="mt-4 rounded-lg bg-blue-50 p-4">
                                                                {(() => {
                                                                    const selectedAnak = anaks.find((a) => a.id.toString() === data.anak_id);
                                                                    return selectedAnak ? (
                                                                        <div>
                                                                            <h4 className="font-medium text-blue-900">Detail Anak Terpilih:</h4>
                                                                            <p className="text-sm text-blue-700">Nama: {selectedAnak.nama_lengkap}</p>
                                                                            <p className="text-sm text-blue-700">Usia: {selectedAnak.usia} tahun</p>
                                                                            <p className="text-sm text-blue-700">
                                                                                Jenis Kelamin: {selectedAnak.jenis_kelamin}
                                                                            </p>
                                                                            <p className="text-sm text-blue-700">Panti: {selectedAnak.panti_nama}</p>
                                                                        </div>
                                                                    ) : null;
                                                                })()}
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </>
                                        )}

                                        {data.pilihan_anak === 'luar_panti' && (
                                            <div className="rounded-lg bg-yellow-50 p-4">
                                                <p className="text-sm text-yellow-800">
                                                    <strong>Catatan:</strong> Untuk adopsi anak dari luar panti, proses akan dilanjutkan sesuai
                                                    prosedur standar yang berlaku. Silakan lengkapi dokumen persyaratan dan tunggu konfirmasi dari
                                                    petugas.
                                                </p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>

                            {/* File utama dan dokumen persyaratan digabung */}
                            <div className="mt-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Upload Dokumen COTA</CardTitle>
                                        <CardDescription>Upload semua dokumen yang diperlukan (maksimal 12MB per file, total 300MB)</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-6">
                                        {/* Upload Dokumen COTA (27 Dokumen Sesuai Spesifikasi) */}
                                        <div>
                                            <h4 className="mb-4 text-lg font-semibold text-blue-900">Upload Dokumen COTA</h4>
                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                {/* 1. Rekomendasi di Dinas Sosial Kabupaten/Kota */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="rekomendasi_dinsos">1. Rekomendasi di Dinas Sosial Kabupaten/Kota</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="rekomendasi_dinsos"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('rekomendasi_dinsos')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.rekomendasi_dinsos && <p className="text-sm text-red-500">{errors.rekomendasi_dinsos}</p>}
                                                </div>

                                                {/* 2. Permohonan dan Pemohon */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="permohonan_pemohon">2. Permohonan dan Pemohon</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="permohonan_pemohon"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('permohonan_pemohon')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.permohonan_pemohon && <p className="text-sm text-red-500">{errors.permohonan_pemohon}</p>}
                                                </div>

                                                {/* 3. Surat Keterangan Berbadan Sehat dari Rumah Sakit Pemerintah */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="surat_sehat_rs">
                                                        3. Surat Keterangan Berbadan Sehat dari Rumah Sakit Pemerintah
                                                    </Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="surat_sehat_rs"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('surat_sehat_rs')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.surat_sehat_rs && <p className="text-sm text-red-500">{errors.surat_sehat_rs}</p>}
                                                </div>

                                                {/* 4. Surat keterangan Kesehatan Jiwa dari Dokter Spesialis Jiwa dari Rumah Sakit Pemerintah (Suami/Istri) */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="surat_kesehatan_jiwa">
                                                        4. Surat keterangan Kesehatan Jiwa dari Dokter Spesialis Jiwa dari Rumah Sakit Pemerintah
                                                        (Suami/Istri)
                                                    </Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="surat_kesehatan_jiwa"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('surat_kesehatan_jiwa')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.surat_kesehatan_jiwa && (
                                                        <p className="text-sm text-red-500">{errors.surat_kesehatan_jiwa}</p>
                                                    )}
                                                </div>

                                                {/* 5. Surat Keterangan tentang Fungsi Organ Reproduksi (Suami/lstri) dari Dokter Spesialis Obstetric dan Gineologi Rumah Sakit Pemerintah */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="surat_fungsi_reproduksi">
                                                        5. Surat Keterangan tentang Fungsi Organ Reproduksi (Suami/lstri) dari Dokter Spesialis
                                                        Obstetric dan Gineologi Rumah Sakit Pemerintah
                                                    </Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="surat_fungsi_reproduksi"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('surat_fungsi_reproduksi')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.surat_fungsi_reproduksi && (
                                                        <p className="text-sm text-red-500">{errors.surat_fungsi_reproduksi}</p>
                                                    )}
                                                </div>

                                                {/* 6. Foto Copy Akta Kelahiran COTA */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="akta_kelahiran_cota">6. Foto Copy Akta Kelahiran COTA</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="akta_kelahiran_cota"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('akta_kelahiran_cota')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.akta_kelahiran_cota && (
                                                        <p className="text-sm text-red-500">{errors.akta_kelahiran_cota}</p>
                                                    )}
                                                </div>

                                                {/* 7. Surat Keterangan Catatan Kepolisian Setempat */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="surat_catatan_kepolisian">7. Surat Keterangan Catatan Kepolisian Setempat</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="surat_catatan_kepolisian"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('surat_catatan_kepolisian')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.surat_catatan_kepolisian && (
                                                        <p className="text-sm text-red-500">{errors.surat_catatan_kepolisian}</p>
                                                    )}
                                                </div>

                                                {/* 8. Foto Copy Nikah/Kutipan Akte Nikah COTA */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="akta_nikah_cota">8. Foto Copy Nikah/Kutipan Akte Nikah COTA</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="akta_nikah_cota"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('akta_nikah_cota')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.akta_nikah_cota && <p className="text-sm text-red-500">{errors.akta_nikah_cota}</p>}
                                                </div>

                                                {/* 9. Foto Copy Kartu Keluarga dan KTP COTA */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="kk_ktp_cota">9. Foto Copy Kartu Keluarga dan KTP COTA</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="kk_ktp_cota"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('kk_ktp_cota')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.kk_ktp_cota && <p className="text-sm text-red-500">{errors.kk_ktp_cota}</p>}
                                                </div>

                                                {/* 10. Foto Copy Akta Kelahiran CAA */}
                                                <div className="space-y-2">
                                                    <Label htmlFor="akta_kelahiran_caa">10. Foto Copy Akta Kelahiran CAA</Label>
                                                    <div className="relative">
                                                        <Input
                                                            id="akta_kelahiran_caa"
                                                            type="file"
                                                            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                            onChange={handleFileChange('akta_kelahiran_caa')}
                                                            required
                                                        />
                                                        <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                                    </div>
                                                    {errors.akta_kelahiran_caa && <p className="text-sm text-red-500">{errors.akta_kelahiran_caa}</p>}
                                                </div>
                                            </div>
                                        </div>

                                        {/* 11. Surat Keterangan Penghasilan dari tempat bekerja COTA */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_penghasilan_cota">11. Surat Keterangan Penghasilan dari tempat bekerja COTA</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_penghasilan_cota"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_penghasilan_cota')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_penghasilan_cota && <p className="text-sm text-red-500">{errors.surat_penghasilan_cota}</p>}
                                        </div>

                                        {/* 12. Surat Persyaratan Persetujuan CAA */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_persetujuan_caa">
                                                12. Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu
                                                menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_persetujuan_caa"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_persetujuan_caa')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_persetujuan_caa && <p className="text-sm text-red-500">{errors.surat_persetujuan_caa}</p>}
                                        </div>

                                        {/* 13. Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan Anak */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_motivasi_cota">
                                                13. Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan
                                                Anak
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_motivasi_cota"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_motivasi_cota')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_motivasi_cota && <p className="text-sm text-red-500">{errors.surat_motivasi_cota}</p>}
                                        </div>

                                        {/* 14. Surat Pernyataan Persetujuan Keluarga COTA */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_persetujuan_keluarga">14. Surat Pernyataan Persetujuan Keluarga COTA</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_persetujuan_keluarga"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_persetujuan_keluarga')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_persetujuan_keluarga && (
                                                <p className="text-sm text-red-500">{errors.surat_persetujuan_keluarga}</p>
                                            )}
                                        </div>

                                        {/* 15. Surat Keterangan Domisili */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_domisili">15. Surat Keterangan Domisili</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_domisili"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_domisili')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_domisili && <p className="text-sm text-red-500">{errors.surat_domisili}</p>}
                                        </div>

                                        {/* 16. Surat Keterangan Sehat */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_keterangan_sehat">16. Surat Keterangan Sehat</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_keterangan_sehat"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_keterangan_sehat')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_keterangan_sehat && <p className="text-sm text-red-500">{errors.surat_keterangan_sehat}</p>}
                                        </div>

                                        {/* 17. Surat Keterangan Tidak Pernah Dihukum */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_tidak_dihukum">17. Surat Keterangan Tidak Pernah Dihukum</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_tidak_dihukum"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_tidak_dihukum')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_tidak_dihukum && <p className="text-sm text-red-500">{errors.surat_tidak_dihukum}</p>}
                                        </div>

                                        {/* 18. Surat Keterangan Tidak Pernah Terlibat Narkoba */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_tidak_narkoba">18. Surat Keterangan Tidak Pernah Terlibat Narkoba</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_tidak_narkoba"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_tidak_narkoba')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_tidak_narkoba && <p className="text-sm text-red-500">{errors.surat_tidak_narkoba}</p>}
                                        </div>

                                        {/* 12. Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_persetujuan_caa">
                                                12. Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu
                                                menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_persetujuan_caa"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_persetujuan_caa')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_persetujuan_caa && <p className="text-sm text-red-500">{errors.surat_persetujuan_caa}</p>}
                                        </div>

                                        {/* 13. Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan Anak */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_motivasi_cota">
                                                13. Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan
                                                Anak
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_motivasi_cota"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_motivasi_cota')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_motivasi_cota && <p className="text-sm text-red-500">{errors.surat_motivasi_cota}</p>}
                                        </div>

                                        {/* 14. Surat Pernyataan Persetujuan Keluarga COTA */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_persetujuan_keluarga">14. Surat Pernyataan Persetujuan Keluarga COTA</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_persetujuan_keluarga"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_persetujuan_keluarga')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_persetujuan_keluarga && (
                                                <p className="text-sm text-red-500">{errors.surat_persetujuan_keluarga}</p>
                                            )}
                                        </div>

                                        {/* 15. Surat Keterangan Domisili */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_domisili">15. Surat Keterangan Domisili</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_domisili"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_domisili')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_domisili && <p className="text-sm text-red-500">{errors.surat_domisili}</p>}
                                        </div>

                                        {/* 16. Surat Keterangan Sehat */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_keterangan_sehat">16. Surat Keterangan Sehat</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_keterangan_sehat"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_keterangan_sehat')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_keterangan_sehat && <p className="text-sm text-red-500">{errors.surat_keterangan_sehat}</p>}
                                        </div>

                                        {/* 17. Surat Pernyataan Kesanggupan */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_cota">
                                                17. Surat Pernyataan Kesanggupan untuk memelihara, mendidik, dan membesarkan CAA
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_cota"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_cota')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_cota && <p className="text-sm text-red-500">{errors.surat_pernyataan_cota}</p>}
                                        </div>

                                        {/* 18. Surat Pernyataan CAA */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_caa">18. Surat Pernyataan CAA</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_caa"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_caa')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_caa && <p className="text-sm text-red-500">{errors.surat_pernyataan_caa}</p>}
                                        </div>

                                        {/* 19. Surat Pernyataan Keluarga */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_keluarga">19. Surat Pernyataan Keluarga</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_keluarga"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_keluarga')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_keluarga && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_keluarga}</p>
                                            )}
                                        </div>

                                        {/* 20. Surat Pernyataan Tidak Menelantarkan */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_menelantarkan">20. Surat Pernyataan Tidak Menelantarkan</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_menelantarkan"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_menelantarkan')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_menelantarkan && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_menelantarkan}</p>
                                            )}
                                        </div>

                                        {/* 21. Surat Pernyataan Tidak Memisahkan */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_memisahkan">21. Surat Pernyataan Tidak Memisahkan</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_memisahkan"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_memisahkan')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_memisahkan && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_memisahkan}</p>
                                            )}
                                        </div>

                                        {/* 22. Surat Pernyataan Tidak Mengeksploitasi */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_mengeksploitasi">22. Surat Pernyataan Tidak Mengeksploitasi</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_mengeksploitasi"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_mengeksploitasi')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_mengeksploitasi && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_mengeksploitasi}</p>
                                            )}
                                        </div>

                                        {/* 23. Surat Pernyataan Tidak Memperjualbelikan */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_memperjualbelikan">
                                                23. Surat Pernyataan Tidak Memperjualbelikan
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_memperjualbelikan"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_memperjualbelikan')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_memperjualbelikan && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_memperjualbelikan}</p>
                                            )}
                                        </div>

                                        {/* 24. Surat Pernyataan Tidak Mengubah Identitas */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_mengubah_identitas">
                                                24. Surat Pernyataan Tidak Mengubah Identitas
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_mengubah_identitas"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_mengubah_identitas')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_mengubah_identitas && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_mengubah_identitas}</p>
                                            )}
                                        </div>

                                        {/* 25. Surat Pernyataan Tidak Mengubah Agama */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_mengubah_agama">25. Surat Pernyataan Tidak Mengubah Agama</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_mengubah_agama"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_mengubah_agama')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_mengubah_agama && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_mengubah_agama}</p>
                                            )}
                                        </div>

                                        {/* 26. Surat Pernyataan Tidak Mengubah Kewarganegaraan */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_mengubah_kewarganegaraan">
                                                26. Surat Pernyataan Tidak Mengubah Kewarganegaraan
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_mengubah_kewarganegaraan"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_mengubah_kewarganegaraan')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_mengubah_kewarganegaraan && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_mengubah_kewarganegaraan}</p>
                                            )}
                                        </div>

                                        {/* 27. Surat Pernyataan Tidak Mengubah Nama */}
                                        <div className="space-y-2">
                                            <Label htmlFor="surat_pernyataan_tidak_mengubah_nama">27. Surat Pernyataan Tidak Mengubah Nama</Label>
                                            <div className="relative">
                                                <Input
                                                    id="surat_pernyataan_tidak_mengubah_nama"
                                                    type="file"
                                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                    onChange={handleFileChange('surat_pernyataan_tidak_mengubah_nama')}
                                                    required
                                                />
                                                <Upload className="absolute top-3 right-3 h-4 w-4 text-gray-400" />
                                            </div>
                                            {errors.surat_pernyataan_tidak_mengubah_nama && (
                                                <p className="text-sm text-red-500">{errors.surat_pernyataan_tidak_mengubah_nama}</p>
                                            )}
                                        </div>

                                        {/* File Size Indicator */}
                                        <div className="mt-4">
                                            <div className="rounded-lg bg-blue-50 p-4">
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="text-sm font-medium text-blue-900">Total Dokumen: 27 Dokumen COTA</p>
                                                        <p className="text-xs text-blue-700">Maksimal: 12MB per file</p>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className={`text-xs font-medium text-green-600`}>{'✅ Sesuai'}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                            {/* Submit Button */}
                            <div className="mt-6 flex justify-end gap-3">
                                {editingCotaId && (
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => {
                                            setEditingCotaId(null);
                                            reset();
                                            setPhoneError('');
                                            setActiveTab('list');
                                        }}
                                    >
                                        Batal
                                    </Button>
                                )}
                                <Button type="submit" disabled={processing} className="bg-blue-600 hover:bg-blue-700">
                                    {processing
                                        ? editingCotaId
                                            ? 'Memperbarui...'
                                            : 'Mendaftar...'
                                        : editingCotaId
                                          ? 'Perbarui COTA'
                                          : 'Daftarkan COTA'}
                                </Button>
                            </div>
                        </form>
                    </TabsContent>
                </Tabs>
                {/* View Detail Modal */}
                {showViewModal && selectedCota && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 max-h-[90vh] w-full max-w-2xl overflow-y-auto border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-xl font-bold text-blue-900">Detail COTA</CardTitle>
                                <Button variant="ghost" size="sm" onClick={() => setShowViewModal(false)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label className="font-medium text-blue-700">Nama</Label>
                                        <p className="text-gray-900">{selectedCota.nama}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Email</Label>
                                        <p className="text-gray-900">{selectedCota.email}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Alamat</Label>
                                        <p className="text-gray-900">{selectedCota.alamat}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Status</Label>
                                        <div className="mt-1">
                                            <span className={getStatusBadge(selectedCota.status)}>{selectedCota.status}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Tanggal Daftar</Label>
                                        <p className="text-gray-900">{formatDate(selectedCota.tanggal_daftar)}</p>
                                    </div>
                                </div>

                                {/* Dokumen Persyaratan Section */}
                                <div className="mt-8">
                                    <h3 className="mb-2 text-lg font-bold text-blue-900">Dokumen Persyaratan</h3>
                                    <div className="space-y-3">
                                        {dokumenPersyaratanList.map((doc, idx) => {
                                            const filePath = selectedCota[doc.field];
                                            return (
                                                <div
                                                    key={doc.field}
                                                    className="flex items-center justify-between rounded-lg border border-gray-200 p-3"
                                                >
                                                    <div>
                                                        <p className="font-medium text-blue-900">
                                                            {idx + 1}. {doc.label}
                                                        </p>
                                                        {filePath ? (
                                                            <p className="text-sm text-green-600">Dokumen tersedia</p>
                                                        ) : (
                                                            <p className="text-sm text-red-600">Dokumen belum diupload</p>
                                                        )}
                                                    </div>
                                                    {filePath && (
                                                        <div className="flex gap-2">
                                                            <Button
                                                                size="sm"
                                                                variant="outline"
                                                                onClick={() => window.open(`/storage/${filePath}`, '_blank')}
                                                                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                                            >
                                                                <Eye className="mr-1 h-3 w-3" />
                                                                Preview
                                                            </Button>
                                                            <Button
                                                                size="sm"
                                                                variant="outline"
                                                                onClick={() => {
                                                                    const link = document.createElement('a');
                                                                    link.href = `/storage/${filePath}`;
                                                                    link.download = `${doc.label}_${selectedCota.nama}`;
                                                                    document.body.appendChild(link);
                                                                    link.click();
                                                                    document.body.removeChild(link);
                                                                }}
                                                                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                                            >
                                                                <Download className="mr-1 h-3 w-3" />
                                                                Download
                                                            </Button>
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>

                                <div className="mt-6 flex justify-end">
                                    <Button variant="outline" onClick={() => setShowViewModal(false)}>
                                        Tutup
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-red-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader>
                                <CardTitle className="text-xl font-bold text-red-600">Konfirmasi Hapus</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-700">
                                    Apakah Anda yakin ingin menghapus COTA <span className="font-semibold">"{showDeleteConfirm?.name}"</span>?
                                    Tindakan ini tidak dapat dibatalkan.
                                </p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                                        Batal
                                    </Button>
                                    <Button variant="destructive" onClick={confirmDelete}>
                                        Hapus
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
