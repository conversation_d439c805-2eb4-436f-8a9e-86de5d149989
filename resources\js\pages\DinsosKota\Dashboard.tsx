import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Users } from 'lucide-react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    modules: {
        cota: boolean;
    };
    statistics: {
        totalCota: number;
        prosesVerifikasi: number;
        tersertifikasi: number;
        menungguVerifikasi: number;
        terdaftar: number;
        tingkatPersetujuan: number;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard Dinsos Kabupaten/Kota',
        href: '/dinsoskota/dashboard',
    },
];

export default function DinsosKotaDashboard({ user, modules, statistics }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard Dinas Sosial Kabupaten/Kota" />

            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {/* Header */}
                <div className="mb-4">
                    <h1 className="mb-2 text-3xl font-bold text-gray-900">Dashboard Dinas Sosial Kabupaten/Kota</h1>
                    <p className="text-gray-600">Selamat datang, {user.name}</p>
                </div>

                {/* User Info Card */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="h-5 w-5" />
                            Informasi Pengguna
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div>
                                <p className="text-sm text-gray-500">Nama</p>
                                <p className="font-medium">{user.name}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Email</p>
                                <p className="font-medium">{user.email}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Role</p>
                                <p className="font-medium capitalize">Dinas Sosial Kabupaten/Kota</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Available Modules - Dihilangkan sesuai permintaan */}

                {/* Regional Stats */}
                <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{statistics.totalCota}</div>
                            <p className="text-xs text-muted-foreground">Pendaftar COTA</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{statistics.menungguVerifikasi}</div>
                            <p className="text-xs text-muted-foreground">Menunggu Verifikasi</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{statistics.tersertifikasi}</div>
                            <p className="text-xs text-muted-foreground">COTA Tersertifikasi</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-2xl font-bold">{statistics.tingkatPersetujuan}%</div>
                            <p className="text-xs text-muted-foreground">Tingkat Persetujuan</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activities */}
                <Card className="mt-8">
                    <CardHeader>
                        <CardTitle>Aktivitas Terkini</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center space-x-4">
                                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium">Permohonan COTA baru diterima</p>
                                    <p className="text-xs text-gray-500">2 jam yang lalu</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-4">
                                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium">Verifikasi dokumen selesai</p>
                                    <p className="text-xs text-gray-500">5 jam yang lalu</p>
                                </div>
                            </div>
                            <div className="flex items-center space-x-4">
                                <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium">Jadwal sidang ditetapkan</p>
                                    <p className="text-xs text-gray-500">1 hari yang lalu</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
