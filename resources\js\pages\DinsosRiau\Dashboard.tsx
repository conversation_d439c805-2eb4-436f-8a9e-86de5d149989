import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Building2, MapPin, TrendingUp } from 'lucide-react';
import { Suspense, lazy } from 'react';
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

// Lazy load MapComponent untuk menghindari SSR issues
const MapComponent = lazy(() => import('@/components/MapComponent'));

interface PantiLocation {
    id: number;
    nama: string;
    alamat: string;
    kabupaten: string;
    lat: number;
    lng: number;
    jumlahAnak: number;
}

interface KabupatenAggregate {
    kabupaten: string;
    lat: number;
    lng: number;
    totalPanti: number;
    totalAnak: number;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    modules: {
        panti_asuhan: boolean;
        cota: boolean;
        bantuan_sosial: boolean;
    };
    dashboardData?: {
        pantiPersebaranData: Array<{
            kabupaten: string;
            jumlah: number;
        }>;
        cotaPendaftarData: Array<{
            tahun: string;
            jumlah: number;
        }>;
        pantiPerTahunData: Array<{
            tahun: string;
            jumlah: number;
        }>;
        pantiLocations: PantiLocation[];
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard Dinsos Provinsi Riau',
        href: '/dinsosriau/dashboard',
    },
];

export default function DinsosRiauDashboard({ user, modules, dashboardData }: Props) {
    // Only use real backend data, no fallback
    const pantiPersebaranData = dashboardData?.pantiPersebaranData || [];
    const cotaPendaftarData = dashboardData?.cotaPendaftarData || [];
    const pantiPerTahunData = dashboardData?.pantiPerTahunData || [];
    const pantiLocations: PantiLocation[] = dashboardData?.pantiLocations || [];

    // Filter hanya panti yang sudah terverifikasi
    const verifiedPantiLocations = pantiLocations; // Sudah terfilter di backend, status hanya approved/diverifikasi

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard Dinas Sosial Provinsi Riau" />

            <div className="flex h-full flex-1 flex-col space-y-4 px-4 py-4 lg:px-6">
                {/* Peta Persebaran Panti Asuhan - Tinggi dikurangi untuk fit page */}
                <Card>
                    <CardHeader className="pb-3">
                        <CardTitle className="flex items-center space-x-2 text-lg">
                            <MapPin className="h-4 w-4" />
                            <span>Peta Persebaran Panti Asuhan di Provinsi Riau</span>
                        </CardTitle>
                        <CardDescription className="text-sm">Lokasi geografis panti asuhan dengan informasi detail setiap titik</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-4">
                        <div className="h-[320px] w-full">
                            <Suspense
                                fallback={
                                    <div className="flex h-full w-full items-center justify-center rounded-lg bg-gray-100">
                                        <div className="text-center">
                                            <div className="mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                            <p className="text-sm text-gray-500">Loading Map...</p>
                                        </div>
                                    </div>
                                }
                            >
                                <MapComponent pantiLocations={verifiedPantiLocations} />
                            </Suspense>
                        </div>
                    </CardContent>
                </Card>

                {/* Analytics Section - 2 Bar Charts Horizontal dengan tinggi dikurangi */}
                <div className="grid gap-4 md:grid-cols-2">
                    {/* COTA Pendaftar Chart */}
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center space-x-2 text-lg">
                                <TrendingUp className="h-4 w-4" />
                                <span>Grafik Jumlah Pendaftar COTA</span>
                            </CardTitle>
                            <CardDescription className="text-sm">Tren pendaftar Calon Orang Tua Asuh per tahun</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-4">
                            <div className="h-[220px]">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart data={cotaPendaftarData}>
                                        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                                        <XAxis
                                            dataKey="tahun"
                                            tick={{ fontSize: 12 }}
                                            tickLine={{ stroke: 'hsl(var(--muted))' }}
                                            axisLine={{ stroke: 'hsl(var(--muted))' }}
                                        />
                                        <YAxis
                                            tick={{ fontSize: 12 }}
                                            tickLine={{ stroke: 'hsl(var(--muted))' }}
                                            axisLine={{ stroke: 'hsl(var(--muted))' }}
                                        />
                                        <Tooltip
                                            formatter={(value, name, props) => {
                                                // value: jumlah, name: 'jumlah', props: { payload: { tahun, jumlah } }
                                                return [
                                                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                                        <span style={{ fontWeight: 700, fontSize: 16, color: '#1e293b', marginBottom: 2 }}>
                                                            {props?.payload?.tahun}
                                                        </span>
                                                        <span style={{ fontWeight: 500, fontSize: 15, color: '#475569' }}>
                                                            Pendaftar:{' '}
                                                            <span style={{ fontWeight: 700, fontFamily: 'monospace', color: '#1e293b' }}>
                                                                {value.toLocaleString()}
                                                            </span>
                                                        </span>
                                                    </div>,
                                                ];
                                            }}
                                            labelStyle={{ display: 'none' }}
                                            contentStyle={{
                                                background: '#f1f5f9',
                                                border: '1px solid #60a5fa',
                                                borderRadius: '10px',
                                                boxShadow: '0 4px 24px 0 rgba(30, 64, 175, 0.10)',
                                                padding: '12px 16px',
                                                fontFamily: 'inherit',
                                                fontSize: 15,
                                                color: '#1e293b',
                                            }}
                                        />
                                        <Bar dataKey="jumlah" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Panti per Tahun Chart */}
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="flex items-center space-x-2 text-lg">
                                <Building2 className="h-4 w-4" />
                                <span>Grafik Jumlah Panti Asuhan per Tahun</span>
                            </CardTitle>
                            <CardDescription className="text-sm">Pertumbuhan jumlah panti asuhan di Provinsi Riau</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-4">
                            <div className="h-[220px]">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart data={pantiPerTahunData}>
                                        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                                        <XAxis
                                            dataKey="tahun"
                                            tick={{ fontSize: 12 }}
                                            tickLine={{ stroke: 'hsl(var(--muted))' }}
                                            axisLine={{ stroke: 'hsl(var(--muted))' }}
                                        />
                                        <YAxis
                                            tick={{ fontSize: 12 }}
                                            tickLine={{ stroke: 'hsl(var(--muted))' }}
                                            axisLine={{ stroke: 'hsl(var(--muted))' }}
                                        />
                                        <Tooltip
                                            formatter={(value, name, props) => {
                                                return [
                                                    <div style={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                                        <span style={{ fontWeight: 700, fontSize: 16, color: '#1e293b', marginBottom: 2 }}>
                                                            {props?.payload?.tahun}
                                                        </span>
                                                        <span style={{ fontWeight: 500, fontSize: 15, color: '#475569' }}>
                                                            Jumlah Panti:{' '}
                                                            <span style={{ fontWeight: 700, fontFamily: 'monospace', color: '#1e293b' }}>
                                                                {value.toLocaleString()}
                                                            </span>
                                                        </span>
                                                    </div>,
                                                ];
                                            }}
                                            labelStyle={{ display: 'none' }}
                                            contentStyle={{
                                                background: '#f1f5f9',
                                                border: '1px solid #60a5fa',
                                                borderRadius: '10px',
                                                boxShadow: '0 4px 24px 0 rgba(30, 64, 175, 0.10)',
                                                padding: '12px 16px',
                                                fontFamily: 'inherit',
                                                fontSize: 15,
                                                color: '#1e293b',
                                            }}
                                        />
                                        <Bar dataKey="jumlah" fill="#1e40af" radius={[4, 4, 0, 0]} />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </DinsosRiauLayout>
    );
}
