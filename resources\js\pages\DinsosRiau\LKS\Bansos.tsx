import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, useForm } from '@inertiajs/react';
import { Calendar, ClipboardCheck, DollarSign, FileText, Users } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { <PERSON>, BarChart, CartesianGrid, XA<PERSON>s, <PERSON><PERSON><PERSON><PERSON> } from 'recharts';
import BansosInfoTab from './BansosInfoTab';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    jumlahBelumVerifikasi: number;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Bantuan Sosial',
        href: '/dinsosriau/lks/bansos',
    },
];

// Sample data for bantuan sosial per tahun (lebih sedikit dan masuk akal)
const bansosData = [
    { tahun: '2019', penerima: 320 },
    { tahun: '2020', penerima: 410 },
    { tahun: '2021', penerima: 390 },
    { tahun: '2022', penerima: 450 },
    { tahun: '2023', penerima: 480 },
    { tahun: '2024', penerima: 510 },
];

// Tambahkan tipe untuk jadwal evaluasi
interface JadwalEvaluasiTabel {
    id: number;
    file: string;
    tanggal: string;
    waktu: string;
}

export default function LKSBansos({ user, jumlahBelumVerifikasi }: Props) {
    const [showJadwalModal, setShowJadwalModal] = useState(false);
    const [jadwalEvaluasiList, setJadwalEvaluasiList] = useState<JadwalEvaluasiTabel[]>([]);
    const [loadingJadwal, setLoadingJadwal] = useState(false);
    const [tabAktif, setTabAktif] = useState<'input' | 'lihat'>('input');
    const [editingJadwal, setEditingJadwal] = useState<JadwalEvaluasiTabel | null>(null);
    // Fungsi untuk fetch jadwal evaluasi dari backend
    const fetchJadwalEvaluasiList = useCallback(
        (forceRefresh = false) => {
            if ((showJadwalModal && tabAktif === 'lihat') || forceRefresh) {
                setLoadingJadwal(true);
                fetch('/dinsosriau/lks/bansos/jadwal-evaluasi')
                    .then((res) => res.json())
                    .then((res) => {
                        if (res.success && Array.isArray(res.data)) {
                            setJadwalEvaluasiList(
                                res.data.map((item: any) => ({
                                    id: item.id,
                                    file: item.file_tim ? `storage/${item.file_tim}` : '-',
                                    tanggal: item.tanggal_evaluasi,
                                    waktu: item.jam_evaluasi,
                                })),
                            );
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching jadwal evaluasi:', error);
                    })
                    .finally(() => setLoadingJadwal(false));
            }
        },
        [showJadwalModal, tabAktif],
    );

    // Fetch jadwal evaluasi saat modal dibuka/tab lihat aktif
    useEffect(() => {
        fetchJadwalEvaluasiList();
    }, [fetchJadwalEvaluasiList]);

    // Fetch data ketika tab berubah ke 'lihat'
    useEffect(() => {
        if (tabAktif === 'lihat' && showJadwalModal) {
            fetchJadwalEvaluasiList(true);
        }
    }, [tabAktif, showJadwalModal]);

    const { data, setData, post, patch, processing, errors, reset } = useForm({
        nama_tim: null as File | null,
        tanggal_evaluasi: '',
        jam_evaluasi: '',
    });

    const handleDataPengajuan = () => {
        router.get(route('dinsosriau.lks.bansos.data'));
    };

    const handlePencairan = () => {
        router.get(route('dinsosriau.lks.bansos.pencairan'));
    };

    const handleSuratPertanggungjawaban = () => {
        router.get(route('dinsosriau.lks.bansos.pertanggungjawaban'));
    };

    const handleJadwalEvaluasi = () => {
        setShowJadwalModal(true);
    };

    const handleCloseModal = () => {
        setShowJadwalModal(false);
        setEditingJadwal(null);
        reset();
    };

    const handleEdit = (jadwal: JadwalEvaluasiTabel) => {
        setEditingJadwal(jadwal);

        // Format tanggal untuk input date (YYYY-MM-DD)
        let formattedDate = '';
        if (jadwal.tanggal) {
            const date = new Date(jadwal.tanggal);
            if (!isNaN(date.getTime())) {
                formattedDate = date.toISOString().split('T')[0];
            }
        }

        // Format waktu untuk input time (HH:MM)
        let formattedTime = '';
        if (jadwal.waktu) {
            // Jika waktu sudah dalam format HH:MM, gunakan langsung
            if (jadwal.waktu.match(/^\d{2}:\d{2}$/)) {
                formattedTime = jadwal.waktu;
            } else {
                // Jika dalam format lain, coba parse
                const time = new Date(`1970-01-01T${jadwal.waktu}`);
                if (!isNaN(time.getTime())) {
                    formattedTime = time.toTimeString().slice(0, 5);
                }
            }
        }

        setData({
            nama_tim: null, // File tidak bisa di-set dari data existing
            tanggal_evaluasi: formattedDate,
            jam_evaluasi: formattedTime,
        });
        setTabAktif('input');
    };

    const handleSubmitJadwal = (e: React.FormEvent) => {
        e.preventDefault();

        if (editingJadwal) {
            // Debug: Log data yang akan dikirim
            console.log('Data yang akan dikirim untuk update:', {
                nama_tim: data.nama_tim,
                tanggal_evaluasi: data.tanggal_evaluasi,
                jam_evaluasi: data.jam_evaluasi,
                editingJadwal: editingJadwal,
            });

            // Buat FormData untuk memastikan format yang benar
            const formData = new FormData();
            if (data.nama_tim) {
                formData.append('nama_tim', data.nama_tim);
            }
            formData.append('tanggal_evaluasi', data.tanggal_evaluasi);
            formData.append('jam_evaluasi', data.jam_evaluasi);

            // Update existing jadwal menggunakan router.post dengan FormData
            router.post(route('dinsosriau.lks.bansos.jadwal-evaluasi.update', editingJadwal.id), formData, {
                onSuccess: () => {
                    reset();
                    setEditingJadwal(null);
                    setTabAktif('lihat');
                    setTimeout(() => {
                        fetchJadwalEvaluasiList(true);
                    }, 500);
                },
                onError: (errors) => {
                    console.error('Error updating jadwal evaluasi:', errors);
                },
            });
        } else {
            // Create new jadwal
            post(route('dinsosriau.lks.bansos.jadwal-evaluasi.store'), {
                onSuccess: () => {
                    reset();
                    setTabAktif('lihat');
                    setTimeout(() => {
                        fetchJadwalEvaluasiList(true);
                    }, 500);
                },
                onError: (errors) => {
                    console.error('Error submitting jadwal evaluasi:', errors);
                },
            });
        }
    };

    // Chart configuration for shadcn
    const chartConfig = {
        penerima: {
            label: 'Jumlah Penerima',
            color: '#2563eb', // Blue-600 (same as login form)
        },
    } satisfies ChartConfig;

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="LKS Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                {/* Informasi Bantuan Sosial - Full Width */}
                <div className="w-full">
                    <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="p-4">
                            <div className="flex items-start gap-3">
                                <div className="rounded-full bg-blue-100 p-2">
                                    <FileText className="h-4 w-4 text-blue-600" />
                                </div>
                                <div className="flex-1">
                                    <h3 className="mb-1 font-semibold text-blue-900">Informasi Bantuan Sosial</h3>
                                    <p className="text-xs leading-relaxed text-blue-800">
                                        Sistem manajemen bantuan sosial terintegrasi untuk memastikan penyaluran bantuan yang tepat sasaran dan
                                        akuntabel. Data yang ditampilkan berdasarkan realisasi program bantuan sosial Dinas Sosial Provinsi Riau.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Informasi Pengajuan Perlu Diverifikasi - Smaller Width */}
                <div className="w-1/2">
                    <BansosInfoTab jumlahBelumVerifikasi={jumlahBelumVerifikasi} />
                </div>

                {/* Chart Section - Full Width */}
                <Card className="border-blue-200 shadow-lg">
                    <CardHeader className="border-b border-blue-200 bg-blue-50 py-4">
                        <div className="text-center">
                            <CardTitle className="text-xl font-bold text-blue-900">Grafik Bantuan Sosial</CardTitle>
                            <p className="text-sm text-blue-600">Data penerima bantuan sosial per tahun (2019-2024)</p>
                        </div>
                    </CardHeader>
                    <CardContent className="p-6">
                        <ChartContainer config={chartConfig} className="h-[300px] w-full">
                            <BarChart data={bansosData} barCategoryGap={24} barGap={4}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="tahun" tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
                                <YAxis tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
                                <ChartTooltip
                                    content={({ active, payload, label }) => {
                                        if (active && payload && payload.length) {
                                            return (
                                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 shadow-md">
                                                    <div className="flex flex-col items-start">
                                                        <span className="text-xs font-semibold text-blue-900">Tahun {label}</span>
                                                        <span className="text-lg font-bold text-green-700">{payload[0].value} penerima</span>
                                                    </div>
                                                </div>
                                            );
                                        }
                                        return null;
                                    }}
                                />
                                <Bar
                                    dataKey="penerima"
                                    fill="var(--color-penerima)"
                                    radius={[4, 4, 0, 0]}
                                    isAnimationActive={true}
                                    cursor="pointer"
                                    onMouseOver={(e) => {
                                        e.target.style.fill = '#1d4ed8'; // blue-700
                                    }}
                                    onMouseOut={(e) => {
                                        e.target.style.fill = 'var(--color-penerima)';
                                    }}
                                />
                            </BarChart>
                        </ChartContainer>

                        {/* Chart Summary - Only Total & Tertinggi (2024) */}
                        <div className="mt-6 flex flex-col items-center justify-center gap-4 md:flex-row md:gap-8">
                            <div className="flex flex-col items-center rounded-xl bg-blue-50 px-8 py-6 shadow-sm">
                                <div className="mb-1 text-3xl font-extrabold text-blue-900">
                                    {bansosData.reduce((sum, item) => sum + item.penerima, 0).toLocaleString()}
                                </div>
                                <div className="text-base font-medium text-blue-700">Total Penerima</div>
                            </div>
                            <div className="flex flex-col items-center rounded-xl bg-blue-50 px-8 py-6 shadow-sm">
                                <div className="mb-1 text-3xl font-extrabold text-blue-900">
                                    {Math.max(...bansosData.map((item) => item.penerima)).toLocaleString()}
                                </div>
                                <div className="text-base font-medium text-blue-700">Tertinggi (2024)</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons Section */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Button
                        onClick={handleDataPengajuan}
                        className="flex h-24 items-center justify-center gap-4 bg-blue-600 text-white hover:bg-blue-700"
                    >
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                            <FileText className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="text-left">
                            <div className="font-semibold">Data Pengajuan</div>
                            <div className="font-semibold">Bantuan Sosial</div>
                            <div className="text-xs opacity-90">Kelola dan monitor data pengajuan</div>
                        </div>
                    </Button>

                    <Button
                        onClick={handlePencairan}
                        className="flex h-24 items-center justify-center gap-4 bg-green-600 text-white hover:bg-green-700"
                    >
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                            <DollarSign className="h-6 w-6 text-green-600" />
                        </div>
                        <div className="text-left">
                            <div className="font-semibold">Pencairan</div>
                            <div className="text-xs opacity-90">Proses pencairan dana bantuan sosial</div>
                        </div>
                    </Button>

                    <Button
                        onClick={handleSuratPertanggungjawaban}
                        className="flex h-24 items-center justify-center gap-4 bg-orange-600 text-white hover:bg-orange-700"
                    >
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                            <ClipboardCheck className="h-6 w-6 text-orange-600" />
                        </div>
                        <div className="text-left">
                            <div className="font-semibold">Surat Pertanggung</div>
                            <div className="font-semibold">Jawaban</div>
                            <div className="text-xs opacity-90">Kelola surat pertanggung jawaban bantuan</div>
                        </div>
                    </Button>

                    <Button
                        onClick={handleJadwalEvaluasi}
                        className="flex h-24 items-center justify-center gap-4 bg-purple-600 text-white hover:bg-purple-700"
                    >
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                            <Calendar className="h-6 w-6 text-purple-600" />
                        </div>
                        <div className="text-left">
                            <div className="font-semibold">Jadwal & Tim</div>
                            <div className="font-semibold">Evaluasi</div>
                            <div className="text-xs opacity-90">Atur jadwal dan tim evaluasi</div>
                        </div>
                    </Button>
                </div>

                {/* Modal Jadwal dan Tim Evaluasi */}
                <Dialog open={showJadwalModal} onOpenChange={setShowJadwalModal}>
                    <DialogContent className="max-w-2xl">
                        <Tabs value={tabAktif} onValueChange={(v) => setTabAktif(v as 'input' | 'lihat')} className="w-full">
                            <TabsList className="mb-4 flex w-full justify-center gap-2">
                                <TabsTrigger value="input">Input Jadwal Evaluasi</TabsTrigger>
                                <TabsTrigger value="lihat">Lihat Jadwal Evaluasi</TabsTrigger>
                            </TabsList>
                            <TabsContent value="input">
                                <DialogHeader>
                                    <DialogTitle className="flex items-center gap-2">
                                        <Users className="h-5 w-5 text-purple-600" />
                                        {editingJadwal ? 'Edit Jadwal dan Tim Evaluasi' : 'Input Jadwal dan Tim Evaluasi'}
                                    </DialogTitle>
                                </DialogHeader>
                                <form onSubmit={handleSubmitJadwal} className="mt-2 space-y-4">
                                    <div>
                                        <Label htmlFor="nama_tim">
                                            File Tim Evaluasi {editingJadwal && '(Opsional - kosongkan jika tidak ingin mengubah file)'}
                                        </Label>
                                        <Input
                                            id="nama_tim"
                                            type="file"
                                            onChange={(e) => {
                                                const file = e.target.files?.[0] || null;
                                                setData('nama_tim', file);
                                            }}
                                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                            className={errors.nama_tim ? 'border-red-500' : ''}
                                            required={!editingJadwal}
                                        />
                                        <p className="mt-1 text-xs text-gray-500">Format: PDF, DOC, DOCX, JPG, PNG (Max: 5MB)</p>
                                        {data.nama_tim && <p className="mt-1 text-sm text-green-600">File dipilih: {data.nama_tim.name}</p>}
                                        {errors.nama_tim && <p className="mt-1 text-sm text-red-500">{errors.nama_tim}</p>}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="tanggal_evaluasi">Tanggal Evaluasi</Label>
                                            <Input
                                                id="tanggal_evaluasi"
                                                type="date"
                                                value={data.tanggal_evaluasi}
                                                onChange={(e) => setData('tanggal_evaluasi', e.target.value)}
                                                className={errors.tanggal_evaluasi ? 'border-red-500' : ''}
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Format: dd/mm/yyyy</p>
                                            {errors.tanggal_evaluasi && <p className="mt-1 text-sm text-red-500">{errors.tanggal_evaluasi}</p>}
                                        </div>
                                        <div>
                                            <Label htmlFor="jam_evaluasi">Jam Evaluasi</Label>
                                            <Input
                                                id="jam_evaluasi"
                                                type="time"
                                                value={data.jam_evaluasi}
                                                onChange={(e) => setData('jam_evaluasi', e.target.value)}
                                                className={errors.jam_evaluasi ? 'border-red-500' : ''}
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Format: hh:mm</p>
                                            {errors.jam_evaluasi && <p className="mt-1 text-sm text-red-500">{errors.jam_evaluasi}</p>}
                                        </div>
                                    </div>

                                    <div className="flex justify-end gap-2 pt-4">
                                        <Button type="button" variant="outline" onClick={handleCloseModal} disabled={processing}>
                                            Batal
                                        </Button>
                                        <Button type="submit" disabled={processing} className="bg-purple-600 hover:bg-purple-700">
                                            {processing
                                                ? editingJadwal
                                                    ? 'Memperbarui...'
                                                    : 'Menyimpan...'
                                                : editingJadwal
                                                  ? 'Perbarui Jadwal'
                                                  : 'Simpan Jadwal'}
                                        </Button>
                                    </div>
                                </form>
                            </TabsContent>
                            <TabsContent value="lihat">
                                <DialogHeader>
                                    <DialogTitle className="flex items-center gap-2">
                                        <Users className="h-5 w-5 text-purple-600" />
                                        Lihat Jadwal Evaluasi
                                    </DialogTitle>
                                </DialogHeader>
                                <div className="mt-4 overflow-x-auto">
                                    <table className="min-w-full border text-sm">
                                        <thead className="bg-gray-100">
                                            <tr>
                                                <th className="border px-2 py-1">No</th>
                                                <th className="border px-2 py-1">File Pencairan</th>
                                                <th className="border px-2 py-1">Tgl Evaluasi</th>
                                                <th className="border px-2 py-1">Waktu</th>
                                                <th className="border px-2 py-1">Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {loadingJadwal ? (
                                                <tr>
                                                    <td colSpan={5} className="py-4 text-center text-gray-500">
                                                        Memuat data...
                                                    </td>
                                                </tr>
                                            ) : jadwalEvaluasiList.length === 0 ? (
                                                <tr>
                                                    <td colSpan={5} className="py-4 text-center text-gray-500">
                                                        Belum ada jadwal evaluasi
                                                    </td>
                                                </tr>
                                            ) : (
                                                jadwalEvaluasiList.map((item, idx) => (
                                                    <tr key={item.id}>
                                                        <td className="border px-2 py-1 text-center">{idx + 1}</td>
                                                        <td className="border px-2 py-1 text-center">
                                                            {item.file !== '-' ? (
                                                                <a
                                                                    href={`/${item.file}`}
                                                                    className="text-blue-600 underline"
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                >
                                                                    {item.file.split('/').pop()}
                                                                </a>
                                                            ) : (
                                                                '-'
                                                            )}
                                                        </td>
                                                        <td className="border px-2 py-1 text-center">
                                                            {new Date(item.tanggal).toLocaleDateString('id-ID')}
                                                        </td>
                                                        <td className="border px-2 py-1 text-center">{item.waktu}</td>
                                                        <td className="border px-2 py-1 text-center">
                                                            <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                                Edit
                                                            </Button>
                                                        </td>
                                                    </tr>
                                                ))
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </DialogContent>
                </Dialog>
            </div>
        </DinsosRiauLayout>
    );
}
