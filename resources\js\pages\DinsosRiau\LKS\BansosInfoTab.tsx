import { Card, CardContent } from '@/components/ui/card';
import { Info } from 'lucide-react';

interface Props {
    jumlahBelumVerifikasi: number;
}

export default function BansosInfoTab({ jumlahBelumVerifikasi }: Props) {
    return (
        <Card className="mb-4 h-full w-full border-yellow-200 bg-yellow-50">
            <CardContent className="flex h-full items-center gap-4 p-4">
                <div className="rounded-full bg-yellow-100 p-2">
                    <Info className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                    <div className="font-semibold text-yellow-900">Informasi Pengajuan Perlu Diverifikasi</div>
                    <div className="text-sm text-yellow-800">
                        Jumlah pengajuan bansos yang <span className="font-bold">belum diverifikasi</span> oleh Dinas Sosial Provinsi Riau:{' '}
                        <span className="font-bold text-yellow-900">{jumlahBelumVerifikasi}</span>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
