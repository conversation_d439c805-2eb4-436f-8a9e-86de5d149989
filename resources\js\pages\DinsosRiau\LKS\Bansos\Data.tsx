import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, Check, ChevronLeft, ChevronRight, Download, Eye, FileText, Search, X } from 'lucide-react';
import { useState } from 'react';

interface PengajuanBansos {
    id: number;
    nama_panti: string;
    nama_ketua: string;
    kabupaten_kota: string;
    no_hp: string;
    tanggal_pengajuan: string;
    status: string;
    user: {
        id: number;
        name: string;
        email: string;
    };
    created_at: string;
    dokumen_verifikasis?: DokumenVerifikasiData[];
    // File dokumen fields
    proposal?: string;
    sk_pengurus?: string;
    rencana_anggaran?: string;
    akta_notaris?: string;
    surat_pengesahan?: string;
    tanda_daftar_lks?: string;
    data_anak?: string;
    sarana_prasarana?: string;
    surat_pernyataan?: string;
    pakta_integritas?: string;
    npwp?: string;
    surat_domisil?: string;
    izin_operasional?: string;
    foto_ktp?: string;
    foto_rekening?: string;
    surat_rekomendasi?: string;
}

interface DokumenVerifikasiData {
    id: number;
    pengajuan_bansos_id: number;
    dokumen_id: number;
    nama_dokumen: string;
    status_verifikasi: 'pending' | 'diterima' | 'ditolak';
    catatan?: string;
}

interface DokumenVerifikasi {
    id: number;
    nama_dokumen: string;
    file_path: string;
    status_verifikasi: 'pending' | 'diterima' | 'ditolak';
    has_file?: boolean;
}

// Fungsi untuk mendapatkan dokumen verifikasi dari data pengajuan
const getDokumenVerifikasi = (pengajuan: PengajuanBansos): DokumenVerifikasi[] => {
    const dokumenFields = [
        { field: 'proposal', nama: 'Proposal Bantuan Sosial', id: 1 },
        { field: 'rencana_anggaran', nama: 'RAB (Rencana Anggaran Biaya)', id: 2 },
        { field: 'surat_pengesahan', nama: 'Surat Pengesahan Kemenkumham', id: 3 },
        { field: 'data_anak', nama: 'Data Anak', id: 4 },
        { field: 'surat_pernyataan', nama: 'Surat Pernyataan Tanggung Jawab', id: 5 },
        { field: 'sarana_prasarana', nama: 'Sarana dan Prasarana', id: 6 },
        { field: 'izin_operasional', nama: 'Izin Operasional', id: 7 },
        { field: 'npwp', nama: 'NPWP', id: 8 },
        { field: 'akta_notaris', nama: 'Akta Notaris', id: 9 },
        { field: 'sk_pengurus', nama: 'SK Pengurus', id: 10 },
        { field: 'tanda_daftar_lks', nama: 'Tanda Daftar LKS', id: 11 },
        { field: 'foto_rekening', nama: 'Foto Rekening Bank', id: 12 },
        { field: 'pakta_integritas', nama: 'Pakta Integritas', id: 13 },
        { field: 'surat_domisil', nama: 'Surat Keterangan Domisili', id: 14 },
        { field: 'foto_ktp', nama: 'Foto KTP Pengurus (Ketua, Sekretaris, Bendahara)', id: 15 },
        { field: 'surat_rekomendasi', nama: 'Surat Rekomendasi Kabupaten/Kota', id: 16 },
    ];

    return dokumenFields.map((dok) => ({
        id: dok.id,
        nama_dokumen: dok.nama,
        file_path: (pengajuan[dok.field as keyof PengajuanBansos] as string) || '',
        status_verifikasi: 'pending' as const,
        has_file: !!pengajuan[dok.field as keyof PengajuanBansos], // Tambahkan flag untuk cek apakah ada file
    }));
};

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pengajuan: PengajuanBansos[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Bantuan Sosial',
        href: '/dinsosriau/lks/bansos',
    },
    {
        title: 'Data Pengajuan',
        href: '/dinsosriau/lks/bansos/data',
    },
];

export default function BansosData({ user, pengajuan, pagination }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [showDetailModal, setShowDetailModal] = useState<PengajuanBansos | null>(null);
    const [dokumenStatus, setDokumenStatus] = useState<{ [key: number]: 'pending' | 'diterima' | 'ditolak' }>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Filter pengajuan based on search term and status filter
    const filteredPengajuan = pengajuan.filter((item) => {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
            searchTerm === '' ||
            item.nama_panti.toLowerCase().includes(searchLower) ||
            item.nama_ketua.toLowerCase().includes(searchLower) ||
            item.kabupaten_kota.toLowerCase().includes(searchLower) ||
            item.status.toLowerCase().includes(searchLower);

        const matchesStatus = statusFilter === '' || statusFilter === 'all' || item.status.toLowerCase() === statusFilter.toLowerCase();

        return matchesSearch && matchesStatus;
    });

    const { data, setData, put, processing, reset } = useForm({
        status: '',
        catatan: '',
        dokumen_verifikasi: {},
    });

    const getStatusBadge = (status: string) => {
        switch (status.toLowerCase()) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 border-yellow-200';
            case 'diproses':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'diterima':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'ditolak':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getVerificationStatus = (pengajuan: PengajuanBansos) => {
        if (!pengajuan.dokumen_verifikasis || pengajuan.dokumen_verifikasis.length === 0) {
            return { text: 'Belum Diverifikasi', class: 'bg-gray-100 text-gray-800' };
        }

        const totalDokumen = getDokumenVerifikasi(pengajuan).length;
        const terverifikasi = pengajuan.dokumen_verifikasis.length;
        const diterima = pengajuan.dokumen_verifikasis.filter((d) => d.status_verifikasi === 'diterima').length;
        const ditolak = pengajuan.dokumen_verifikasis.filter((d) => d.status_verifikasi === 'ditolak').length;

        if (terverifikasi === totalDokumen) {
            if (ditolak === 0) {
                return { text: 'Semua ACC', class: 'bg-green-100 text-green-800' };
            } else {
                return { text: `${diterima} ACC, ${ditolak} Ditolak`, class: 'bg-red-100 text-red-800' };
            }
        } else {
            return { text: `${terverifikasi}/${totalDokumen} Diverifikasi`, class: 'bg-yellow-100 text-yellow-800' };
        }
    };

    const formatTanggal = (tanggal: string) => {
        return new Date(tanggal).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const handleSubmitStatus = () => {
        if (!showDetailModal) return;

        put(route('dinsosriau.lks.bansos.pengajuan.status', showDetailModal.id), {
            onSuccess: () => {
                setShowDetailModal(null);
                reset();
            },
        });
    };

    const handleViewDetail = (id: number) => {
        const found = pengajuan.find((p) => p.id === id);
        if (found) {
            setShowDetailModal(found);
            setData({
                status: found.status,
                catatan: '',
                dokumen_verifikasi: {},
            });

            // Set status verifikasi yang sudah tersimpan dari database
            const savedDokumenStatus: { [key: number]: 'pending' | 'diterima' | 'ditolak' } = {};
            if (found.dokumen_verifikasis) {
                found.dokumen_verifikasis.forEach((dok) => {
                    savedDokumenStatus[dok.dokumen_id] = dok.status_verifikasi;
                });
            }
            setDokumenStatus(savedDokumenStatus);
        }
    };

    const handleCloseDetailModal = () => {
        setShowDetailModal(null);
        setDokumenStatus({});
        reset();
    };

    const handleDokumenAction = (dokumenId: number, action: 'diterima' | 'ditolak') => {
        setDokumenStatus((prev) => ({
            ...prev,
            [dokumenId]: action,
        }));
    };

    const handleSubmitVerifikasi = () => {
        if (!showDetailModal || isSubmitting) return;

        // Cek apakah semua dokumen sudah diverifikasi
        const totalDokumen = getDokumenVerifikasi(showDetailModal).length;
        const dokumenTerverifikasi = Object.keys(dokumenStatus).length;

        if (dokumenTerverifikasi === 0) {
            alert('Silakan verifikasi minimal satu dokumen terlebih dahulu.');
            return;
        }

        // Tentukan status pengajuan berdasarkan verifikasi dokumen
        const dokumenDiterima = Object.values(dokumenStatus).filter((status) => status === 'diterima').length;
        const dokumenDitolak = Object.values(dokumenStatus).filter((status) => status === 'ditolak').length;

        let newStatus = '';
        let catatan = '';

        // Logika baru: Hanya diterima jika SEMUA dokumen diterima, selainnya ditolak
        if (dokumenTerverifikasi === totalDokumen && dokumenDitolak === 0) {
            // Semua dokumen sudah diverifikasi dan semua diterima
            newStatus = 'diterima';
            catatan = 'Semua dokumen telah diverifikasi dan diterima.';
        } else {
            // Ada dokumen ditolak atau belum semua dokumen diverifikasi
            newStatus = 'ditolak';
            if (dokumenDitolak > 0) {
                catatan = `Pengajuan ditolak karena ${dokumenDitolak} dari ${totalDokumen} dokumen tidak memenuhi syarat.`;
            } else {
                catatan = `Pengajuan ditolak karena belum semua dokumen diverifikasi (${dokumenTerverifikasi}/${totalDokumen}).`;
            }
        }

        console.log('Mengirim data verifikasi:', {
            status: newStatus,
            catatan: catatan,
            dokumen_verifikasi: dokumenStatus,
            id: showDetailModal.id,
        });

        setIsSubmitting(true);

        // Submit ke backend dengan data yang tepat
        router.put(
            route('dinsosriau.lks.bansos.pengajuan.status', showDetailModal.id),
            {
                status: newStatus,
                catatan: catatan,
                dokumen_verifikasi: dokumenStatus,
            },
            {
                onSuccess: (page) => {
                    console.log('Success response:', page);
                    alert(`Status pengajuan berhasil diubah menjadi: ${newStatus.toUpperCase()}`);
                    setShowDetailModal(null);
                    setDokumenStatus({});
                    setIsSubmitting(false);
                    reset();
                    // Refresh halaman untuk update data
                    router.reload();
                },
                onError: (errors) => {
                    console.error('Error updating status:', errors);
                    console.error('Data yang dikirim:', {
                        status: newStatus,
                        catatan: catatan,
                        dokumen_verifikasi: dokumenStatus,
                    });

                    setIsSubmitting(false);

                    let errorMessage = 'Gagal mengubah status pengajuan.';
                    if (errors && typeof errors === 'object') {
                        const errorKeys = Object.keys(errors);
                        if (errorKeys.length > 0) {
                            errorMessage += ' Error: ' + Object.values(errors).flat().join(', ');
                        }
                    }
                    alert(errorMessage);
                },
            },
        );
    };

    const handleBatal = () => {
        if (showDetailModal && showDetailModal.dokumen_verifikasis) {
            const savedDokumenStatus: { [key: number]: 'pending' | 'diterima' | 'ditolak' } = {};
            showDetailModal.dokumen_verifikasis.forEach((dok) => {
                savedDokumenStatus[dok.dokumen_id] = dok.status_verifikasi;
            });
            setDokumenStatus(savedDokumenStatus);
        } else {
            setDokumenStatus({});
        }
        reset();
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Pengajuan Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-blue-900">Data Pengajuan Bantuan Sosial</h1>
                        <p className="text-sm text-blue-600">Kelola dan monitor semua pengajuan bantuan sosial</p>
                    </div>
                    <div className="flex items-center gap-4">
                        <Link href="/dinsosriau/lks/bansos">
                            <Button variant="outline" size="sm" className="flex items-center gap-2">
                                <ArrowLeft className="h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Filters */}
                <Card className="border-blue-200">
                    <CardContent className="p-4">
                        <div className="flex items-center gap-4">
                            <div className="relative flex-1">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                <Input
                                    placeholder="Cari berdasarkan nama panti, ketua, kabupaten, atau status..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <div className="w-48">
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Filter Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="diproses">Menunggu</SelectItem>
                                        <SelectItem value="diterima">Diterima</SelectItem>
                                        <SelectItem value="ditolak">Ditolak</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Table */}
                <Card className="flex-1 border-blue-200">
                    <CardHeader className="border-b border-blue-200 bg-blue-50">
                        <CardTitle className="flex items-center gap-2 text-blue-900">
                            <FileText className="h-5 w-5" />
                            Daftar Pengajuan Bantuan Sosial
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-blue-50">
                                        <TableHead className="w-16 text-center text-blue-800">No</TableHead>
                                        <TableHead className="text-blue-800">Nama Panti</TableHead>
                                        <TableHead className="text-blue-800">Ketua</TableHead>
                                        <TableHead className="text-blue-800">Kabupaten/Kota</TableHead>
                                        <TableHead className="text-blue-800">Tanggal Pengajuan</TableHead>
                                        <TableHead className="w-32 text-center text-blue-800">Status</TableHead>

                                        <TableHead className="w-32 text-center align-middle text-blue-800">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredPengajuan.length > 0 ? (
                                        filteredPengajuan.map((item, index) => (
                                            <TableRow key={item.id} className="transition-colors hover:bg-blue-50">
                                                <TableCell className="text-center font-medium text-blue-900">{index + 1}</TableCell>
                                                <TableCell className="font-medium">{item.nama_panti}</TableCell>
                                                <TableCell>{item.nama_ketua}</TableCell>
                                                <TableCell>{item.kabupaten_kota}</TableCell>
                                                <TableCell>{formatTanggal(item.tanggal_pengajuan)}</TableCell>
                                                <TableCell className="text-center">
                                                    <Badge className={getStatusBadge(item.status)}>
                                                        {item.status.toLowerCase() === 'diproses'
                                                            ? 'Menunggu'
                                                            : item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                                                    </Badge>
                                                </TableCell>
                                                {/* Aksi: View button aligned center */}
                                                <TableCell className="text-center align-middle">
                                                    <div className="flex items-center justify-center">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            className="h-8 w-8 border-blue-500 p-0 text-blue-600 hover:bg-blue-50"
                                                            title="Lihat Detail & Verifikasi Dokumen"
                                                            onClick={() => handleViewDetail(item.id)}
                                                        >
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={8} className="py-8 text-center text-gray-500">
                                                {searchTerm || statusFilter
                                                    ? 'Tidak ada data yang sesuai dengan pencarian/filter'
                                                    : 'Belum ada data pengajuan bantuan sosial'}
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between border-t border-blue-200 px-4 py-3">
                            <div className="text-sm text-gray-700">
                                {searchTerm || statusFilter
                                    ? `Menampilkan ${filteredPengajuan.length} hasil dari pencarian/filter`
                                    : `Menampilkan ${pagination.from} hingga ${pagination.to} dari ${pagination.total} data`}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                    disabled={pagination.current_page === 1}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Sebelumnya
                                </Button>
                                <span className="flex items-center px-3 text-sm text-gray-600">
                                    Halaman {pagination.current_page} dari {pagination.last_page}
                                </span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                    disabled={pagination.current_page === pagination.last_page}
                                >
                                    Selanjutnya
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Detail View Modal with Document Verification */}
                {showDetailModal && (
                    <div
                        className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm"
                        onClick={handleCloseDetailModal}
                    >
                        <Card
                            className="flex max-h-[95vh] w-full max-w-5xl flex-col overflow-hidden border border-blue-200/50 bg-white shadow-2xl"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <CardHeader className="flex flex-shrink-0 flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-lg font-semibold text-blue-900">Detail Pengajuan & Verifikasi Dokumen</CardTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-gray-600 transition-colors hover:bg-gray-100 hover:text-gray-800"
                                    onClick={handleCloseDetailModal}
                                    title="Tutup"
                                >
                                    <X className="h-5 w-5" />
                                </Button>
                            </CardHeader>
                            <div className="flex-1 overflow-y-auto">
                                <CardContent className="space-y-6">
                                    {/* Detail Pengajuan */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label>Nama Panti</Label>
                                            <Input value={showDetailModal.nama_panti} disabled className="bg-gray-50" />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>Ketua</Label>
                                            <Input value={showDetailModal.nama_ketua} disabled className="bg-gray-50" />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>Kabupaten/Kota</Label>
                                            <Input value={showDetailModal.kabupaten_kota} disabled className="bg-gray-50" />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>No HP</Label>
                                            <Input value={showDetailModal.no_hp} disabled className="bg-gray-50" />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>Tanggal Pengajuan</Label>
                                            <Input value={formatTanggal(showDetailModal.tanggal_pengajuan)} disabled className="bg-gray-50" />
                                        </div>
                                        <div className="space-y-2">
                                            <Label>Status Saat Ini</Label>
                                            <div className="flex items-center">
                                                <Badge className={getStatusBadge(showDetailModal.status)}>
                                                    {showDetailModal.status.charAt(0).toUpperCase() + showDetailModal.status.slice(1)}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Tabel Verifikasi Dokumen dengan Scroll */}
                                    <div className="border-t pt-6">
                                        <div className="mb-4 flex items-center justify-between">
                                            <h3 className="text-lg font-semibold text-blue-900">Verifikasi Dokumen</h3>
                                            <div className="text-sm text-gray-600">
                                                Progress: {Object.keys(dokumenStatus).length} / {getDokumenVerifikasi(showDetailModal).length} dokumen
                                                diverifikasi
                                            </div>
                                        </div>

                                        {/* Scrollable Document Verification Table */}
                                        <div className="max-h-[400px] overflow-y-auto rounded-lg border border-blue-200">
                                            <Table>
                                                <TableHeader className="sticky top-0 z-10 bg-blue-50">
                                                    <TableRow>
                                                        <TableHead className="w-16 text-center font-semibold text-blue-800">No</TableHead>
                                                        <TableHead className="min-w-[250px] font-semibold text-blue-800">Dokumen</TableHead>
                                                        <TableHead className="w-40 text-center font-semibold text-blue-800">Status File</TableHead>
                                                        <TableHead className="w-48 text-center font-semibold text-blue-800">Aksi</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {getDokumenVerifikasi(showDetailModal).map((dokumen, index) => {
                                                        const currentStatus = dokumenStatus[dokumen.id] || dokumen.status_verifikasi;
                                                        return (
                                                            <TableRow key={dokumen.id} className="transition-colors hover:bg-blue-50">
                                                                <TableCell className="text-center font-medium text-blue-900">{index + 1}</TableCell>
                                                                <TableCell className="font-medium">{dokumen.nama_dokumen}</TableCell>
                                                                <TableCell className="text-center">
                                                                    {dokumen.has_file ? (
                                                                        <span className="text-sm font-medium text-green-600">✓ Ada</span>
                                                                    ) : (
                                                                        <span className="text-sm font-medium text-red-600">✗ Tidak Ada</span>
                                                                    )}
                                                                </TableCell>
                                                                <TableCell className="text-center">
                                                                    <div className="flex justify-center gap-1">
                                                                        <Button
                                                                            size="sm"
                                                                            variant="outline"
                                                                            className={`h-7 w-7 p-0 ${dokumen.has_file ? 'border-blue-500 text-blue-600 hover:bg-blue-50' : 'cursor-not-allowed border-gray-300 text-gray-400'}`}
                                                                            onClick={() =>
                                                                                dokumen.has_file &&
                                                                                window.open(`/storage/${dokumen.file_path}`, '_blank')
                                                                            }
                                                                            title={dokumen.has_file ? 'Lihat Dokumen' : 'File tidak tersedia'}
                                                                            disabled={!dokumen.has_file}
                                                                        >
                                                                            <Eye className="h-3 w-3" />
                                                                        </Button>
                                                                        <Button
                                                                            size="sm"
                                                                            variant="outline"
                                                                            className={`h-7 w-7 p-0 ${dokumen.has_file ? 'border-gray-500 text-gray-600 hover:bg-gray-50' : 'cursor-not-allowed border-gray-300 text-gray-400'}`}
                                                                            onClick={() =>
                                                                                dokumen.has_file &&
                                                                                window.open(`/storage/${dokumen.file_path}`, '_blank')
                                                                            }
                                                                            title={dokumen.has_file ? 'Download File' : 'File tidak tersedia'}
                                                                            disabled={!dokumen.has_file}
                                                                        >
                                                                            <Download className="h-3 w-3" />
                                                                        </Button>
                                                                        <Button
                                                                            size="sm"
                                                                            variant={currentStatus === 'diterima' ? 'default' : 'outline'}
                                                                            className={`h-7 w-7 p-0 ${currentStatus === 'diterima' ? 'bg-green-600 hover:bg-green-700' : 'border-green-500 text-green-600 hover:bg-green-50'}`}
                                                                            title="Terima"
                                                                            onClick={() => handleDokumenAction(dokumen.id, 'diterima')}
                                                                        >
                                                                            <Check className="h-3 w-3" />
                                                                        </Button>
                                                                        <Button
                                                                            size="sm"
                                                                            variant={currentStatus === 'ditolak' ? 'default' : 'outline'}
                                                                            className={`h-7 w-7 p-0 ${currentStatus === 'ditolak' ? 'bg-red-600 hover:bg-red-700' : 'border-red-500 text-red-600 hover:bg-red-50'}`}
                                                                            title="Tolak"
                                                                            onClick={() => handleDokumenAction(dokumen.id, 'ditolak')}
                                                                        >
                                                                            <X className="h-3 w-3" />
                                                                        </Button>
                                                                    </div>
                                                                </TableCell>
                                                            </TableRow>
                                                        );
                                                    })}
                                                </TableBody>
                                            </Table>
                                        </div>
                                    </div>
                                </CardContent>
                            </div>

                            {/* Fixed Action Buttons */}
                            <div className="flex flex-shrink-0 gap-2 border-t bg-gray-50 p-4">
                                <Button variant="outline" className="flex-1" onClick={handleBatal} disabled={isSubmitting}>
                                    Batal
                                </Button>
                                <Button
                                    className={`flex-1 ${Object.keys(dokumenStatus).length === 0 ? 'cursor-not-allowed bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}
                                    onClick={handleSubmitVerifikasi}
                                    disabled={isSubmitting || Object.keys(dokumenStatus).length === 0}
                                    title={Object.keys(dokumenStatus).length === 0 ? 'Verifikasi minimal satu dokumen terlebih dahulu' : ''}
                                >
                                    {isSubmitting
                                        ? 'Mengirim...'
                                        : Object.keys(dokumenStatus).length === 0
                                          ? 'Verifikasi Dokumen'
                                          : `Kirim Verifikasi (${Object.keys(dokumenStatus).length}/${getDokumenVerifikasi(showDetailModal).length})`}
                                </Button>
                            </div>
                        </Card>
                    </div>
                )}
            </div>
        </DinsosRiauLayout>
    );
}
