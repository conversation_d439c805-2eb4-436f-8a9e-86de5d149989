import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, ChevronLeft, ChevronRight, DollarSign, Search, Upload } from 'lucide-react';
import { useState } from 'react';

interface FilePencairan {
    id: number;
    file_path: string;
    nama_file: string;
    created_at: string;
    url: string;
}

interface PencairanBansos {
    id: number;
    nama_panti: string;
    nama_ketua: string;
    kabupaten_kota: string;
    no_hp: string;
    tanggal_pengajuan: string;
    status: string;
    file_uploaded?: boolean;
    file_pencairan?: FilePencairan;
    user: {
        id: number;
        name: string;
        email: string;
    };
    created_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pencairan: PencairanBansos[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Bantuan Sosial',
        href: '/dinsosriau/lks/bansos',
    },
    {
        title: 'Pencairan',
        href: '/dinsosriau/lks/bansos/pencairan',
    },
];

export default function Pencairan({ user, pencairan, pagination }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedPencairan, setSelectedPencairan] = useState<PencairanBansos | null>(null);
    const [showUploadModal, setShowUploadModal] = useState(false);
    const [showViewModal, setShowViewModal] = useState(false);
    const [viewFile, setViewFile] = useState<FilePencairan | null>(null);
    // Filter pencairan based on search term
    const filteredPencairan: PencairanBansos[] = (pencairan || []).filter((item: PencairanBansos) => {
        const searchLower = searchTerm.toLowerCase();
        return item.nama_panti.toLowerCase().includes(searchLower) || item.kabupaten_kota.toLowerCase().includes(searchLower);
    });

    const { data, setData, post, processing, reset, errors } = useForm({
        pengajuan_bansos_id: '',
        file: null as File | null,
        jenis_file: 'surat_pencairan', // Set default ke surat_pencairan
        keterangan: '',
    });

    const handleUploadFile = (item: PencairanBansos) => {
        setSelectedPencairan(item);
        setData('pengajuan_bansos_id', item.id.toString());
        setShowUploadModal(true);
    };

    const handleViewFile = (item: PencairanBansos) => {
        if (item.file_pencairan) {
            setViewFile(item.file_pencairan);
            setShowViewModal(true);
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('file', file);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!data.file) {
            alert('Mohon pilih file yang akan diupload');
            return;
        }

        post(route('dinsosriau.lks.bansos.pencairan.upload'), {
            onSuccess: () => {
                setShowUploadModal(false);
                setSelectedPencairan(null);
                reset();
                setData('jenis_file', 'surat_pencairan'); // Reset jenis_file ke default
                alert('File berhasil diupload!');
                router.reload(); // reload data agar button berubah
            },
            onError: (errors) => {
                console.error('Upload errors:', errors);
            },
        });
    };

    const handleCloseModal = () => {
        setShowUploadModal(false);
        setSelectedPencairan(null);
        reset();
        setData('jenis_file', 'surat_pencairan'); // Reset jenis_file ke default
    };

    const formatTanggal = (tanggal: string) => {
        return new Date(tanggal).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatRupiah = (amount: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
        }).format(amount);
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Pencairan Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-green-900">Pencairan Bantuan Sosial</h1>
                        <p className="text-sm text-green-600">Proses pencairan dana untuk bantuan sosial yang telah disetujui</p>
                    </div>
                    <div className="flex items-center gap-4">
                        <Link href="/dinsosriau/lks/bansos">
                            <Button variant="outline" size="sm" className="flex items-center gap-2">
                                <ArrowLeft className="h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Info Card */}
                <Card className="border-green-200 bg-green-50">
                    <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                            <div className="rounded-full bg-green-100 p-2">
                                <DollarSign className="h-4 w-4 text-green-600" />
                            </div>
                            <div className="flex-1">
                                <h3 className="mb-1 font-semibold text-green-900">Informasi Pencairan</h3>
                                <p className="text-xs leading-relaxed text-green-800">
                                    Halaman ini menampilkan daftar bantuan sosial yang telah disetujui dan siap untuk dicairkan. Pastikan semua
                                    dokumen telah lengkap sebelum melakukan proses pencairan.
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Filters */}
                <Card className="border-green-200">
                    <CardContent className="p-4">
                        <div className="flex items-center gap-4">
                            <div className="relative flex-1">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                <Input
                                    placeholder="Cari berdasarkan nama panti atau kabupaten..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Table */}
                <Card className="flex-1 border-green-200">
                    <CardHeader className="border-b border-green-200 bg-green-50">
                        <CardTitle className="flex items-center gap-2 text-green-900">
                            <DollarSign className="h-5 w-5" />
                            Daftar Pencairan Bantuan Sosial
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-green-50">
                                        <TableHead className="w-16 text-center text-green-800">No</TableHead>
                                        <TableHead className="text-green-800">Nama Panti</TableHead>
                                        <TableHead className="text-green-800">Kabupaten/Kota</TableHead>
                                        <TableHead className="text-green-800">Tanggal Pengajuan</TableHead>
                                        <TableHead className="w-32 text-center text-green-800">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredPencairan.length > 0 ? (
                                        filteredPencairan.map((item, index) => (
                                            <TableRow key={item.id} className="transition-colors hover:bg-green-50">
                                                <TableCell className="text-center font-medium text-green-900">{index + 1}</TableCell>
                                                <TableCell className="font-medium">{item.nama_panti}</TableCell>
                                                <TableCell>{item.kabupaten_kota}</TableCell>
                                                <TableCell>{formatTanggal(item.tanggal_pengajuan)}</TableCell>
                                                <TableCell className="text-center">
                                                    {item.file_uploaded && item.file_pencairan ? (
                                                        <Button
                                                            size="sm"
                                                            className="flex h-8 items-center gap-1 bg-blue-600 px-3 text-white hover:bg-blue-700"
                                                            title="Lihat File Pencairan"
                                                            onClick={() => handleViewFile(item)}
                                                        >
                                                            <Upload className="h-4 w-4" />
                                                            <span className="text-xs">View</span>
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            size="sm"
                                                            className="flex h-8 items-center gap-1 bg-green-600 px-3 text-white hover:bg-green-700"
                                                            title="Unggah File Pencairan"
                                                            onClick={() => handleUploadFile(item)}
                                                        >
                                                            <Upload className="h-4 w-4" />
                                                            <span className="text-xs">Unggah File</span>
                                                        </Button>
                                                    )}
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={5} className="py-8 text-center text-gray-500">
                                                {searchTerm
                                                    ? 'Tidak ada data yang sesuai dengan pencarian'
                                                    : 'Belum ada data yang siap untuk dicairkan'}
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between border-t border-green-200 px-4 py-3">
                            <div className="text-sm text-gray-700">
                                {searchTerm ? (
                                    `Menampilkan ${filteredPencairan.length} hasil dari pencarian`
                                ) : pagination.total > 0 ? (
                                    <>
                                        Menampilkan {pagination.from || 0} hingga {pagination.to || 0} dari {pagination.total || 0} data
                                    </>
                                ) : (
                                    <>Tidak ada data yang ditampilkan</>
                                )}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                    disabled={pagination.current_page === 1 || pagination.total === 0}
                                    className="flex items-center gap-1"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Sebelumnya
                                </Button>
                                <span className="flex items-center rounded border bg-gray-50 px-3 text-sm text-gray-600">
                                    Halaman {pagination.current_page || 1} dari {pagination.last_page || 1}
                                </span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                    disabled={pagination.current_page === pagination.last_page || pagination.last_page <= 1 || pagination.total === 0}
                                    className="flex items-center gap-1"
                                >
                                    Selanjutnya
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* View Modal - HARUS di luar Table/TableRow/TableBody */}
                <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>File Pencairan</DialogTitle>
                        </DialogHeader>
                        {viewFile && (
                            <div className="space-y-3">
                                <div>
                                    <Label className="text-sm font-medium">Nama File</Label>
                                    <div className="text-sm text-gray-900">{viewFile.nama_file}</div>
                                </div>
                                <div>
                                    <Label className="text-sm font-medium">Tanggal Upload</Label>
                                    <div className="text-sm text-gray-900">{formatTanggal(viewFile.created_at)}</div>
                                </div>
                                <div>
                                    <a href={viewFile.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">
                                        Lihat File
                                    </a>
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>

                {/* Upload Modal */}
                <Dialog open={showUploadModal} onOpenChange={setShowUploadModal}>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>Upload File Pencairan</DialogTitle>
                        </DialogHeader>
                        {selectedPencairan && (
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <Label className="text-sm font-medium">Panti</Label>
                                    <Input value={selectedPencairan.nama_panti} disabled className="bg-gray-50" />
                                </div>
                                <div>
                                    <Label htmlFor="jenis_file" className="text-sm font-medium">
                                        Jenis File
                                    </Label>
                                    <Input value="Surat Pencairan" disabled className="bg-gray-50" />
                                    <p className="mt-1 text-xs text-gray-500">Jenis file telah otomatis diatur ke Surat Pencairan</p>
                                </div>
                                <div>
                                    <Label htmlFor="file" className="text-sm font-medium">
                                        File <span className="text-red-500">*</span>
                                    </Label>
                                    <Input
                                        id="file"
                                        type="file"
                                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                        onChange={handleFileChange}
                                        className="cursor-pointer"
                                    />
                                    <p className="mt-1 text-xs text-gray-500">Format: PDF, DOC, DOCX, JPG, PNG (Maks. 10MB)</p>
                                    {errors.file && <p className="mt-1 text-sm text-red-500">{errors.file}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="keterangan" className="text-sm font-medium">
                                        Keterangan
                                    </Label>
                                    <Textarea
                                        id="keterangan"
                                        value={data.keterangan}
                                        onChange={(e) => setData('keterangan', e.target.value)}
                                        placeholder="Masukkan keterangan tambahan (opsional)"
                                        rows={3}
                                    />
                                </div>
                                <div className="flex gap-2 pt-4">
                                    <Button type="button" variant="outline" onClick={handleCloseModal} className="flex-1" disabled={processing}>
                                        Batal
                                    </Button>
                                    <Button type="submit" className="flex-1 bg-green-600 hover:bg-green-700" disabled={processing}>
                                        {processing ? 'Uploading...' : 'Upload'}
                                    </Button>
                                </div>
                            </form>
                        )}
                    </DialogContent>
                </Dialog>
            </div>
        </DinsosRiauLayout>
    );
}
