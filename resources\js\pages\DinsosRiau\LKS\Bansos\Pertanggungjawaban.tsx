import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, ChevronLeft, ChevronRight, ClipboardCheck, Clock, Download, Edit, Eye, Search, X, XCircle } from 'lucide-react';
import { useState } from 'react';

interface LaporanBansos {
    id: number;
    nama_panti: string;
    kabupaten_kota: string;
    tanggal_upload: string;
    file_laporan: string;
    status: string;
    created_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    laporan: LaporanBansos[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Bantuan Sosial',
        href: '/dinsosriau/lks/bansos',
    },
    {
        title: 'Surat Pertanggung Jawaban',
        href: '/dinsosriau/lks/bansos/pertanggungjawaban',
    },
];

export default function BansosPertanggungjawaban({ user, laporan, pagination }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [showStatusModal, setShowStatusModal] = useState<LaporanBansos | null>(null);
    const [showDocumentModal, setShowDocumentModal] = useState<LaporanBansos | null>(null);

    // Filter laporan based on search term and status
    const filteredLaporan = laporan.filter((item) => {
        const searchLower = searchTerm.toLowerCase();
        const statusMatch = statusFilter === 'all' || item.status.toLowerCase() === statusFilter;
        return (
            statusMatch &&
            (item.nama_panti.toLowerCase().includes(searchLower) ||
                item.kabupaten_kota.toLowerCase().includes(searchLower) ||
                item.status.toLowerCase().includes(searchLower))
        );
    });

    const { data, setData, put, processing, reset, errors } = useForm({
        status: '',
        catatan: '',
    });

    const handleUpdateStatus = (item: LaporanBansos) => {
        // Reset form terlebih dahulu
        reset();
        // Set status saat ini sebagai default
        setData({
            status: item.status || '',
            catatan: '',
        });
        setShowStatusModal(item);
    };

    const handleCloseModal = () => {
        setShowStatusModal(null);
        reset();
    };

    const handleSubmitStatus = () => {
        if (!showStatusModal) return;

        // Validasi frontend: jika status ditolak, catatan wajib diisi
        if (data.status === 'ditolak' && (!data.catatan || data.catatan.trim() === '')) {
            alert('Catatan penolakan wajib diisi jika status Ditolak.');
            return;
        }

        put(route('dinsosriau.lks.bansos.laporan.status', showStatusModal.id), {
            onSuccess: () => {
                handleCloseModal();
            },
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status.toLowerCase()) {
            case 'diproses':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'diterima':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'ditolak':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'diproses':
                return <Clock className="h-3 w-3" />;
            case 'diterima':
                return <CheckCircle className="h-3 w-3" />;
            case 'ditolak':
                return <XCircle className="h-3 w-3" />;
            default:
                return <Clock className="h-3 w-3" />;
        }
    };

    const formatTanggal = (tanggal: string) => {
        return new Date(tanggal).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const handleDownload = (filePath: string, namaPanti: string) => {
        const link = document.createElement('a');
        link.href = `/storage/${filePath}`;
        link.download = `Laporan_${namaPanti}_${new Date().getTime()}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleViewDetail = (item: LaporanBansos) => {
        // Display the uploaded accountability report in modal popup
        if (item.file_laporan) {
            setShowDocumentModal(item);
        } else {
            alert('File laporan pertanggungjawaban tidak tersedia');
        }
    };

    const handleCloseDocumentModal = () => {
        setShowDocumentModal(null);
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Surat Pertanggung Jawaban" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-orange-900">Surat Pertanggung Jawaban</h1>
                        <p className="text-sm text-orange-600">Kelola dan monitor laporan pertanggungjawaban bantuan sosial</p>
                    </div>
                    <div className="flex items-center gap-4">
                        <Link href="/dinsosriau/lks/bansos">
                            <Button variant="outline" size="sm" className="flex items-center gap-2">
                                <ArrowLeft className="h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Info Card */}
                <Card className="border-orange-200 bg-orange-50">
                    <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                            <div className="rounded-full bg-orange-100 p-2">
                                <ClipboardCheck className="h-4 w-4 text-orange-600" />
                            </div>
                            <div className="flex-1">
                                <h3 className="mb-1 font-semibold text-orange-900">Informasi Pertanggungjawaban</h3>
                                <p className="text-xs leading-relaxed text-orange-800">
                                    Halaman ini menampilkan semua laporan pertanggungjawaban yang telah dikirimkan oleh penerima bantuan sosial.
                                    Periksa dan validasi setiap laporan untuk memastikan akuntabilitas penggunaan dana.
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Filters */}
                <Card className="border-orange-200">
                    <CardContent className="p-4">
                        <div className="flex items-center gap-4">
                            <div className="relative flex-1">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                <Input
                                    placeholder="Cari berdasarkan nama panti, kabupaten, atau status..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            <div>
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger className="w-40 border-gray-300 bg-white">
                                        <SelectValue placeholder="Filter Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="diproses">Diproses</SelectItem>
                                        <SelectItem value="diterima">Diterima</SelectItem>
                                        <SelectItem value="ditolak">Ditolak</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Table */}
                <Card className="flex-1 border-orange-200">
                    <CardHeader className="border-b border-orange-200 bg-orange-50">
                        <CardTitle className="flex items-center gap-2 text-orange-900">
                            <ClipboardCheck className="h-5 w-5" />
                            Daftar Laporan Pertanggungjawaban
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-orange-50">
                                        <TableHead className="w-16 text-center text-orange-800">No</TableHead>
                                        <TableHead className="text-orange-800">Nama Panti</TableHead>
                                        <TableHead className="text-orange-800">Kabupaten/Kota</TableHead>
                                        <TableHead className="text-orange-800">Tanggal Upload</TableHead>
                                        <TableHead className="w-32 text-center text-orange-800">Status</TableHead>
                                        <TableHead className="w-32 text-center text-orange-800">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredLaporan.length > 0 ? (
                                        filteredLaporan.map((item, index) => (
                                            <TableRow key={item.id} className="transition-colors hover:bg-orange-50">
                                                <TableCell className="text-center font-medium text-orange-900">{index + 1}</TableCell>
                                                <TableCell className="font-medium">{item.nama_panti}</TableCell>
                                                <TableCell>{item.kabupaten_kota}</TableCell>
                                                <TableCell>{formatTanggal(item.tanggal_upload)}</TableCell>
                                                <TableCell className="text-center">
                                                    <Badge className={getStatusBadge(item.status)}>
                                                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="flex justify-center gap-1">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            className="h-8 w-8 border-orange-500 p-0 text-orange-600 hover:bg-orange-50"
                                                            title="Lihat Surat Pertanggungjawaban"
                                                            onClick={() => handleViewDetail(item)}
                                                        >
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            className="h-8 w-8 border-blue-500 p-0 text-blue-600 hover:bg-blue-50"
                                                            title="Download Laporan"
                                                            onClick={() => handleDownload(item.file_laporan, item.nama_panti)}
                                                        >
                                                            <Download className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            className="h-8 w-8 border-green-500 p-0 text-green-600 hover:bg-green-50"
                                                            title="Update Status"
                                                            onClick={() => handleUpdateStatus(item)}
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={6} className="py-8 text-center text-gray-500">
                                                {searchTerm ? 'Tidak ada data yang sesuai dengan pencarian' : 'Belum ada laporan pertanggungjawaban'}
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between border-t border-orange-200 px-4 py-3">
                            <div className="text-sm text-gray-700">
                                {searchTerm ? (
                                    `Menampilkan ${filteredLaporan.length} hasil dari pencarian`
                                ) : pagination.total > 0 ? (
                                    <>
                                        Menampilkan {pagination.from || 0} hingga {pagination.to || 0} dari {pagination.total || 0} data
                                    </>
                                ) : (
                                    <>Tidak ada data yang ditampilkan</>
                                )}
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                    disabled={pagination.current_page === 1 || pagination.total === 0}
                                    className="flex items-center gap-1"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Sebelumnya
                                </Button>
                                <span className="flex items-center rounded border bg-gray-50 px-3 text-sm text-gray-600">
                                    Halaman {pagination.current_page || 1} dari {pagination.last_page || 1}
                                </span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                    disabled={pagination.current_page === pagination.last_page || pagination.last_page <= 1 || pagination.total === 0}
                                    className="flex items-center gap-1"
                                >
                                    Selanjutnya
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Status Update Modal */}
                {showStatusModal && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-orange-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-lg font-semibold text-orange-900">Update Status Laporan</CardTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                                    onClick={handleCloseModal}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="nama_panti">Nama Panti</Label>
                                    <Input value={showStatusModal.nama_panti} disabled className="bg-gray-50" />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="status">Status Laporan</Label>
                                    <div className="mb-1 text-xs text-gray-500">
                                        Status saat ini: <span className="font-medium">{showStatusModal.status}</span>
                                    </div>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                        <SelectTrigger className="w-full border-gray-300 bg-white">
                                            <SelectValue placeholder="Pilih: Diterima atau Ditolak" />
                                        </SelectTrigger>
                                        <SelectContent className="z-[100] border bg-white shadow-lg" position="popper" sideOffset={5}>
                                            <SelectItem value="diterima" className="cursor-pointer hover:bg-gray-100 focus:bg-gray-100">
                                                <span className="flex items-center gap-2">
                                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                                    Diterima
                                                </span>
                                            </SelectItem>
                                            <SelectItem value="ditolak" className="cursor-pointer hover:bg-gray-100 focus:bg-gray-100">
                                                <span className="flex items-center gap-2">
                                                    <XCircle className="h-4 w-4 text-red-500" />
                                                    Ditolak
                                                </span>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && <p className="text-sm text-red-500">{errors.status}</p>}
                                </div>

                                {data.status === 'ditolak' && (
                                    <div className="space-y-2">
                                        <Label htmlFor="catatan">
                                            Catatan Penolakan <span className="text-red-500">*</span>
                                        </Label>
                                        <Textarea
                                            value={data.catatan}
                                            onChange={(e) => setData('catatan', e.target.value)}
                                            placeholder="Wajib diisi jika laporan ditolak. Jelaskan alasan penolakan..."
                                            className="min-h-[80px]"
                                        />
                                        {errors.catatan && <p className="text-sm text-red-500">{errors.catatan}</p>}
                                    </div>
                                )}

                                <div className="flex gap-2 pt-4">
                                    <Button variant="outline" className="flex-1" onClick={handleCloseModal} disabled={processing}>
                                        Batal
                                    </Button>
                                    <Button className="flex-1 bg-orange-600 hover:bg-orange-700" onClick={handleSubmitStatus} disabled={processing}>
                                        {processing ? 'Menyimpan...' : 'Simpan'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Document Viewer Modal */}
                {showDocumentModal && (
                    <div
                        className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm"
                        onClick={handleCloseDocumentModal}
                    >
                        <Card
                            className="flex max-h-[95vh] w-full max-w-4xl flex-col overflow-hidden bg-white shadow-2xl"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <CardHeader className="flex flex-shrink-0 flex-row items-center justify-between space-y-0 border-b border-orange-200 bg-orange-50 pb-2">
                                <CardTitle className="text-lg font-semibold text-orange-900">
                                    Surat Pertanggungjawaban - {showDocumentModal.nama_panti}
                                </CardTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-orange-600 transition-colors hover:bg-orange-100 hover:text-orange-800"
                                    onClick={handleCloseDocumentModal}
                                    title="Tutup"
                                >
                                    <X className="h-5 w-5" />
                                </Button>
                            </CardHeader>

                            <div className="flex-1 overflow-hidden">
                                <CardContent className="h-full p-0">
                                    {/* Document Info */}
                                    <div className="border-b border-orange-200 bg-orange-50 p-4">
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span className="font-medium text-orange-800">Nama Panti:</span>
                                                <span className="ml-2 text-gray-700">{showDocumentModal.nama_panti}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-orange-800">Kabupaten/Kota:</span>
                                                <span className="ml-2 text-gray-700">{showDocumentModal.kabupaten_kota}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-orange-800">Tanggal Upload:</span>
                                                <span className="ml-2 text-gray-700">{formatTanggal(showDocumentModal.tanggal_upload)}</span>
                                            </div>
                                            <div>
                                                <span className="font-medium text-orange-800">Status:</span>
                                                <Badge className={`ml-2 ${getStatusBadge(showDocumentModal.status)}`}>
                                                    {showDocumentModal.status.charAt(0).toUpperCase() + showDocumentModal.status.slice(1)}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Document Viewer */}
                                    <div className="h-[calc(100vh-300px)] min-h-[400px] bg-gray-100">
                                        <iframe
                                            src={`/storage/${showDocumentModal.file_laporan}`}
                                            className="h-full w-full border-0"
                                            title="Surat Pertanggungjawaban"
                                        />
                                    </div>
                                </CardContent>
                            </div>

                            {/* Modal Footer */}
                            <div className="flex flex-shrink-0 gap-2 border-t bg-gray-50 p-4">
                                <Button variant="outline" className="flex-1" onClick={handleCloseDocumentModal}>
                                    Tutup
                                </Button>
                                <Button
                                    className="flex-1 bg-orange-600 hover:bg-orange-700"
                                    onClick={() => handleDownload(showDocumentModal.file_laporan, showDocumentModal.nama_panti)}
                                >
                                    <Download className="mr-2 h-4 w-4" />
                                    Download Dokumen
                                </Button>
                            </div>
                        </Card>
                    </div>
                )}
            </div>
        </DinsosRiauLayout>
    );
}
