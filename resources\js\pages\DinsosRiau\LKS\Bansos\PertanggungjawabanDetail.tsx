import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { Head, router } from '@inertiajs/react';
import { ArrowLeft, Download, FileText } from 'lucide-react';

interface LaporanBansos {
    id: number;
    nama_panti: string;
    kabupaten_kota: string;
    tanggal_upload: string;
    status: string;
    keterangan: string;
    file_laporan: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    laporan: LaporanBansos;
}

export default function PertanggungjawabanDetail({ laporan }: Props) {
    const getStatusBadge = (status: string) => {
        switch (status.toLowerCase()) {
            case 'disetujui':
                return (
                    <Badge variant="default" className="bg-green-500">
                        Disetujui
                    </Badge>
                );
            case 'ditolak':
                return <Badge variant="destructive">Di<PERSON><PERSON></Badge>;
            case 'menunggu':
                return (
                    <Badge variant="secondary" className="bg-yellow-500 text-white">
                        Menunggu
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const handleBack = () => {
        router.get(route('dinsosriau.lks.bansos.pertanggungjawaban'));
    };

    const handleDownload = () => {
        if (laporan.file_laporan) {
            window.open(`/storage/${laporan.file_laporan}`, '_blank');
        }
    };

    return (
        <DinsosRiauLayout>
            <Head title="Detail Laporan Pertanggungjawaban" />

            <div className="container mx-auto p-6">
                <div className="mb-6">
                    <Button variant="outline" onClick={handleBack} className="mb-4">
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Kembali ke Daftar
                    </Button>

                    <h1 className="text-3xl font-bold text-gray-900">Detail Laporan Pertanggungjawaban</h1>
                    <p className="mt-2 text-gray-600">Informasi lengkap laporan pertanggungjawaban bantuan sosial</p>
                </div>

                <div className="grid gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <span className="flex items-center">
                                    <FileText className="mr-2 h-5 w-5" />
                                    Informasi Laporan
                                </span>
                                {getStatusBadge(laporan.status)}
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div>
                                    <label className="text-sm font-medium text-gray-500">Nama Panti</label>
                                    <p className="text-lg font-medium text-gray-900">{laporan.nama_panti}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Kabupaten/Kota</label>
                                    <p className="text-lg font-medium text-gray-900">{laporan.kabupaten_kota}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Tanggal Upload</label>
                                    <p className="text-lg font-medium text-gray-900">
                                        {new Date(laporan.tanggal_upload).toLocaleDateString('id-ID', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                        })}
                                    </p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Status</label>
                                    <div className="mt-1">{getStatusBadge(laporan.status)}</div>
                                </div>
                            </div>

                            {laporan.keterangan && (
                                <div>
                                    <label className="text-sm font-medium text-gray-500">Keterangan</label>
                                    <p className="mt-1 rounded-lg bg-gray-50 p-3 text-gray-900">{laporan.keterangan}</p>
                                </div>
                            )}

                            {laporan.file_laporan && (
                                <div>
                                    <label className="text-sm font-medium text-gray-500">File Laporan</label>
                                    <div className="mt-2">
                                        <Button variant="outline" onClick={handleDownload} className="flex items-center">
                                            <Download className="mr-2 h-4 w-4" />
                                            Unduh File Laporan
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Informasi Sistem</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                                <div>
                                    <label className="text-gray-500">Dibuat pada</label>
                                    <p className="text-gray-900">{new Date(laporan.created_at).toLocaleString('id-ID')}</p>
                                </div>

                                <div>
                                    <label className="text-gray-500">Terakhir diupdate</label>
                                    <p className="text-gray-900">{new Date(laporan.updated_at).toLocaleString('id-ID')}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </DinsosRiauLayout>
    );
}
