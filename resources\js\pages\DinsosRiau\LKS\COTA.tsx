import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { Calendar, RefreshCw, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    cotaStats: {
        totalCalonOrangTuaAsuh: number;
        jadwalSidangAktif: number;
        statusBreakdown: Record<string, number>;
        upcomingJadwal: number;
        lastUpdated: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dinsosriau/dashboard',
    },
    {
        title: 'LKS COTA',
        href: '/dinsosriau/lks/cota',
    },
];

export default function COTA({ user, cotaStats }: Props) {
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [lastUpdated, setLastUpdated] = useState(new Date());

    // Auto refresh every 30 seconds
    useEffect(() => {
        const interval = setInterval(() => {
            handleRefresh();
        }, 30000); // 30 seconds

        return () => clearInterval(interval);
    }, []);

    const handleRefresh = () => {
        setIsRefreshing(true);
        router.reload({
            only: ['cotaStats'],
            onFinish: () => {
                setIsRefreshing(false);
                setLastUpdated(new Date());
            },
        });
    };

    const formatLastUpdated = () => {
        return lastUpdated.toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
        });
    };
    const menuItems = [
        {
            title: 'Data Calon Orang Tua Asuh',
            description: 'Kelola data dan status calon orang tua asuh',
            icon: Users,
            href: '/dinsosriau/lks/cota/data-calon',
            color: 'bg-blue-500',
        },
        {
            title: 'Kelola Penjadwalan',
            description: 'Atur jadwal sidang dan pengangkatan anak asuh',
            icon: Calendar,
            href: '/dinsosriau/lks/cota/jadwal-sidang',
            color: 'bg-purple-500',
        },
    ];

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="LKS COTA - Dinas Sosial Provinsi Riau" />

            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">LKS COTA</h1>
                        <p className="text-muted-foreground">Sistem Layanan Kesejahteraan Sosial - Calon Orang Tua Asuh</p>
                    </div>
                    <div className="flex items-center gap-4">
                        {/* <div className="text-right">
                            <p className="text-xs text-muted-foreground">Terakhir diperbarui</p>
                            <p className="text-sm font-medium">{formatLastUpdated()}</p>
                        </div> */}
                        <button
                            onClick={handleRefresh}
                            disabled={isRefreshing}
                            className="rounded-md bg-blue-600 p-2 text-white hover:bg-blue-700 disabled:opacity-50"
                            title="Refresh data"
                        >
                            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                        </button>
                    </div>
                </div>

                {/* Navigation Cards */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
                    {menuItems.map((item, index) => {
                        const IconComponent = item.icon;
                        return (
                            <Link key={index} href={item.href}>
                                <Card className="group cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg">
                                    <CardHeader className="pb-4">
                                        <div className="flex items-center space-x-4">
                                            <div className={`rounded-lg p-3 ${item.color} text-white`}>
                                                <IconComponent className="h-6 w-6" />
                                            </div>
                                            <div className="flex-1">
                                                <CardTitle className="text-lg transition-colors group-hover:text-blue-600">{item.title}</CardTitle>
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <CardDescription className="text-sm leading-relaxed">{item.description}</CardDescription>
                                    </CardContent>
                                </Card>
                            </Link>
                        );
                    })}
                </div>

                {/* Quick Stats */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-md bg-blue-100 p-2">
                                    <Users className="h-4 w-4 text-blue-600" />
                                </div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-muted-foreground">
                                        Total Calon Orang Tua Asuh (Dinsos Provinsi + Kabupaten/Kota)
                                    </p>
                                    <p className="text-2xl font-bold text-blue-600">{cotaStats.totalCalonOrangTuaAsuh}</p>
                                    {/* Status breakdown removed as requested */}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-md bg-purple-100 p-2">
                                    <Calendar className="h-4 w-4 text-purple-600" />
                                </div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-muted-foreground">Jadwal Sidang Aktif</p>
                                    <p className="text-2xl font-bold text-purple-600">{cotaStats.jadwalSidangAktif}</p>
                                    {cotaStats.upcomingJadwal > 0 && (
                                        <p className="mt-1 text-xs text-muted-foreground">{cotaStats.upcomingJadwal} dalam 7 hari ke depan</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </DinsosRiauLayout>
    );
}
