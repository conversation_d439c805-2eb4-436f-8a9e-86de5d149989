// Daftar dokumen persyaratan untuk modal detail
const dokumenPersyaratanList = [
    { field: 'file_ktp', label: 'KTP' },
    { field: 'file_kk', label: 'Kartu <PERSON>' },
    { field: 'file_surat_pernikahan', label: 'Surat Pernikahan' },
    { field: 'file_slip_gaji', label: '<PERSON><PERSON>' },
    { field: 'file_surat_keterangan_sehat', label: 'Surat Keterangan Sehat' },
    { field: 'rekomendasi_dinsos', label: 'Rekomendasi di Dinas Sosial Kabupaten/Kota' },
    { field: 'permohonan_pemohon', label: 'Permohonan dan <PERSON>' },
    { field: 'surat_sehat_rs', label: 'Surat Keterangan Berbadan Sehat dari Rumah Sakit Pemerintah' },
    { field: 'surat_kesehatan_jiwa', label: 'Surat Kesehatan Jiwa dari Dokter Spesialis Jiwa RS Pemerintah (Suami/Istri)' },
    {
        field: 'surat_fungsi_reproduksi',
        label: 'Surat Keterangan Fungsi Organ Reproduksi (Suami/Istri) dari Dokter Spesialis Obstetric dan Gineologi RS Pemerintah',
    },
    { field: 'akta_kelahiran_cota', label: 'Foto Copy Akta Kelahiran COTA' },
    { field: 'surat_catatan_kepolisian', label: 'Surat Keterangan Catatan Kepolisian Setempat' },
    { field: 'akta_nikah_cota', label: 'Foto Copy Nikah/Kutipan Akte Nikah COTA' },
    { field: 'kk_ktp_cota', label: 'Foto Copy Kartu Keluarga dan KTP COTA' },
    { field: 'akta_kelahiran_caa', label: 'Foto Copy Akta Kelahiran CAA' },
    { field: 'surat_penghasilan_cota', label: 'Surat Keterangan Penghasilan dari tempat bekerja COTA' },
    {
        field: 'surat_persetujuan_caa',
        label: 'Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial',
    },
    {
        field: 'surat_motivasi_cota',
        label: 'Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan Anak',
    },
    { field: 'surat_non_diskriminasi', label: 'Surat Pernyataan COTA akan memperlakukan anak kandung dan anak angkat tanpa diskriminasi' },
    {
        field: 'surat_asal_usul',
        label: 'Surat Pernyataan bahwa COTA akan memberitahukan kepada anak angkat mengenai asal usul dan orangtua kandungnya',
    },
    {
        field: 'surat_wali_nikah',
        label: 'Surat Pernyataan COTA tidak berhak menjadi Wali Nikah bagi anak angkat perempuan dan memberi kuasa kepada Wali Hakim',
    },
    {
        field: 'surat_hak_waris',
        label: 'Surat Pernyataan COTA akan memberikan hak waris/hibah atas harta kepada anak angkat sesuai ketentuan hukum islam yang berlaku di Indonesia',
    },
    { field: 'surat_persetujuan_keluarga', label: 'Surat Pernyataan persetujuan adopsi dari pihak keluarga COTA' },
    {
        field: 'laporan_sosial_caa',
        label: 'Laporan Sosial Calon Anak Angkat yang dibuat oleh pekerja sosial instansi sosial setempat dan pekerja sosial Panti / Yayasan',
    },
    {
        field: 'berita_acara_ibu_kandung',
        label: 'Surat/Berita Acara Penyerahan dan Kuasa dari Pihak ibu kandung kepada instansi sosial setempat/ COTA',
    },
    {
        field: 'berita_acara_instansi',
        label: 'Surat/Berita acara Penyerahan dan Kuasa dari Pihak instansi sosial setempat dan pekerja sosial Panti / Yayasan',
    },
    {
        field: 'laporan_cota',
        label: 'Laporan Calon Orangtua Angkat yang dibuat oleh pekerja sosial instansi sosial setempat dan pekerja sosial/yayasan',
    },
    { field: 'sk_izin_pengasuhan', label: 'SK Pemberian Izin Pengasuhan Anak' },
    { field: 'surat_perjanjian_pengasuhan', label: 'Surat Perjanjian Pengasuhan Anak Antara Panti / Yayasan dengan COTA (Opsional)' },
    { field: 'berita_acara_penyerahan', label: 'Surat/Berita Acara Penyerahan Anak dari Panti/Yayasan kepada COTA (Opsional)' },
    {
        field: 'laporan_perkembangan_anak',
        label: 'Laporan Perkembangan Anak yang dibuat oleh Pekerja Sosial Instansi Sosial setempat dan Pekerja Sosial Panti/Yayasan (Opsional)',
    },
    { field: 'foto_cota_caa', label: 'Foto Calon Orang Tua Angkat dan Calon Anak Angkat' },
];
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, ChevronLeft, ChevronRight, Download, Eye, Info, Search, X } from 'lucide-react';
import { useState } from 'react';

interface CalonOrangTuaAsuh {
    id: number;
    nama_lengkap: string;
    kabupaten_kota: string;
    status: string;
    status_badge: {
        label: string;
        class: string;
    };
    created_at: string;
    rejection_reason?: string;
    file_ktp?: string;
    file_kk?: string;
    file_surat_pernikahan?: string;
    file_slip_gaji?: string;
    file_surat_keterangan_sehat?: string;
    // Dokumen persyaratan tambahan dari Dinas Kabupaten/Kota
    rekomendasi_dinsos?: string;
    permohonan_pemohon?: string;
    surat_sehat_rs?: string;
    surat_kesehatan_jiwa?: string;
    surat_fungsi_reproduksi?: string;
    akta_kelahiran_cota?: string;
    surat_catatan_kepolisian?: string;
    akta_nikah_cota?: string;
    kk_ktp_cota?: string;
    akta_kelahiran_caa?: string;
    surat_penghasilan_cota?: string;
    surat_persetujuan_caa?: string;
    surat_motivasi_cota?: string;
    surat_non_diskriminasi?: string;
    surat_asal_usul?: string;
    surat_wali_nikah?: string;
    surat_hak_waris?: string;
    surat_persetujuan_keluarga?: string;
    laporan_sosial_caa?: string;
    berita_acara_ibu_kandung?: string;
    berita_acara_instansi?: string;
    laporan_cota?: string;
    sk_izin_pengasuhan?: string;
    surat_perjanjian_pengasuhan?: string;
    berita_acara_penyerahan?: string;
    laporan_perkembangan_anak?: string;
    foto_cota_caa?: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    calonOrangTuaAsuh: {
        data: CalonOrangTuaAsuh[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    filters: {
        search?: string;
    };
    flash?: {
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dinsosriau/dashboard',
    },
    {
        title: 'LKS COTA',
        href: '/dinsosriau/lks/cota',
    },
    {
        title: 'Data Calon Orang Tua Asuh',
        href: '/dinsosriau/lks/cota/data-calon',
    },
];

export default function DataCalon({ user, calonOrangTuaAsuh, filters, flash }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [selectedCalon, setSelectedCalon] = useState<CalonOrangTuaAsuh | null>(null);
    const [showDialog, setShowDialog] = useState(false);
    const [showRejectionDialog, setShowRejectionDialog] = useState(false);
    const [status, setStatus] = useState<string>('');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/dinsosriau/lks/cota/data-calon', {
            search: searchTerm,
        });
    };

    const handlePageChange = (page: number) => {
        router.get('/dinsosriau/lks/cota/data-calon', {
            page,
            search: filters?.search,
        });
    };

    const handleView = (calon: CalonOrangTuaAsuh) => {
        setSelectedCalon(calon);
        setStatus(calon.status);
        setShowDialog(true);
    };

    const updateStatus = async (id: number, newStatus: string) => {
        try {
            // Send update to backend
            router.patch(
                `/dinsosriau/lks/cota/data-calon/${id}/status`,
                {
                    status: newStatus,
                },
                {
                    preserveState: false, // Refresh data from server
                    preserveScroll: true,
                    onSuccess: () => {
                        // Update selected calon if it's the same item
                        if (selectedCalon?.id === id) {
                            setSelectedCalon((prev) =>
                                prev
                                    ? {
                                          ...prev,
                                          status: newStatus,
                                          status_badge: {
                                              label: newStatus,
                                              class:
                                                  newStatus === 'Diterima'
                                                      ? 'bg-green-100 text-green-800'
                                                      : newStatus === 'Ditolak'
                                                        ? 'bg-red-100 text-red-800'
                                                        : 'bg-yellow-100 text-yellow-800',
                                          },
                                      }
                                    : null,
                            );
                        }
                    },
                    onError: (errors) => {
                        console.error('Failed to update status:', errors);
                        alert('Gagal mengupdate status. Silakan coba lagi.');
                    },
                },
            );
        } catch (error) {
            console.error('Error updating status:', error);
            alert('Terjadi kesalahan. Silakan coba lagi.');
        }
    };

    const handleAcc = () => {
        if (selectedCalon) {
            setStatus('Diterima');
            updateStatus(selectedCalon.id, 'Diterima');
            setShowDialog(false);
        }
    };

    const handleTolak = () => {
        if (selectedCalon) {
            setStatus('Ditolak');
            updateStatus(selectedCalon.id, 'Ditolak');
            setShowDialog(false);
        }
    };

    const handleShowRejectionReason = (calon: CalonOrangTuaAsuh) => {
        setSelectedCalon(calon);
        setShowRejectionDialog(true);
    };

    // Use real data from backend with proper pagination
    const data = calonOrangTuaAsuh || {
        data: [],
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0,
        from: 0,
        to: 0,
    };

    return (
        <>
            <DinsosRiauLayout breadcrumbs={breadcrumbs}>
                <Head title="Data Calon Orang Tua Asuh" />

                <div className="flex h-full flex-1 flex-col gap-6 p-6">
                    {/* Success Message */}
                    {flash?.success && (
                        <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                            <div className="flex">
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-green-800">{flash.success}</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Header Section */}
                    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                        <div className="flex items-center gap-4">
                            <Link href="/dinsosriau/lks/cota">
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Kembali
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold text-blue-900">Data Calon Orang Tua Asuh</h1>
                                <p className="mt-1 text-blue-600">Kelola data calon orang tua asuh di Provinsi Riau</p>
                            </div>
                        </div>

                        {/* Search Bar */}
                        <form onSubmit={handleSearch} className="relative w-full md:w-80">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-blue-400" />
                            <Input
                                placeholder="Cari nama, kabupaten/kota..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="border-blue-200 pl-10 focus:border-blue-500 focus:ring-blue-500"
                            />
                        </form>
                    </div>

                    {/* Table Card */}
                    <Card className="border-blue-200 shadow-lg">
                        <CardHeader className="border-b border-blue-200 bg-blue-50">
                            <CardTitle className="flex items-center justify-between text-blue-900">
                                <span>Daftar Calon Orang Tua Asuh</span>
                                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                    {data.total} data
                                </Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow className="bg-blue-50">
                                            <TableHead className="w-16 font-semibold text-blue-900">No</TableHead>
                                            <TableHead className="font-semibold text-blue-900">Nama Calon Orang Tua Asuh</TableHead>
                                            <TableHead className="font-semibold text-blue-900">Kabupaten/Kota</TableHead>
                                            <TableHead className="font-semibold text-blue-900">Status</TableHead>
                                            <TableHead className="text-center font-semibold text-blue-900">Aksi</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {data.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={5} className="py-8 text-center text-blue-500">
                                                    Tidak ada data yang ditemukan
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            data.data.map((item, index) => (
                                                <TableRow key={item.id} className="transition-colors hover:bg-blue-50">
                                                    <TableCell className="font-medium text-blue-900">
                                                        {(data.current_page - 1) * data.per_page + index + 1}
                                                    </TableCell>
                                                    <TableCell className="font-medium text-blue-900">{item.nama_lengkap}</TableCell>
                                                    <TableCell className="text-blue-700">{item.kabupaten_kota}</TableCell>
                                                    <TableCell>
                                                        <Badge className={item.status_badge.class}>{item.status_badge.label}</Badge>
                                                    </TableCell>
                                                    <TableCell className="text-center">
                                                        <div className="flex justify-center gap-2">
                                                            <Button
                                                                size="sm"
                                                                variant="outline"
                                                                onClick={() => handleView(item)}
                                                                className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                                            >
                                                                <Eye className="mr-1 h-3 w-3" />
                                                                View
                                                            </Button>
                                                            {item.status === 'Ditolak' && item.rejection_reason && (
                                                                <Button
                                                                    size="sm"
                                                                    variant="outline"
                                                                    onClick={() => handleShowRejectionReason(item)}
                                                                    className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                                                                    title="Lihat Alasan Penolakan"
                                                                >
                                                                    <Info className="mr-1 h-3 w-3" />
                                                                    Info
                                                                </Button>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {data.last_page > 1 && (
                                <div className="flex items-center justify-between border-t border-blue-200 bg-blue-50 px-6 py-3">
                                    <p className="text-sm text-blue-600">
                                        Menampilkan <span className="font-medium">{data.from}</span> sampai{' '}
                                        <span className="font-medium">{data.to}</span> dari <span className="font-medium">{data.total}</span> hasil
                                    </p>
                                    <div className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(data.current_page - 1)}
                                            disabled={data.current_page === 1}
                                            className="border-blue-200 text-blue-600 hover:bg-blue-100"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            Previous
                                        </Button>
                                        <span className="text-sm text-blue-600">
                                            Page {data.current_page} of {data.last_page}
                                        </span>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handlePageChange(data.current_page + 1)}
                                            disabled={data.current_page === data.last_page}
                                            className="border-blue-200 text-blue-600 hover:bg-blue-100"
                                        >
                                            Next
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </DinsosRiauLayout>

            {/* Modal View Detail */}
            {showDialog && selectedCalon && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                    <Card className="m-4 max-h-[90vh] w-full max-w-4xl overflow-y-auto border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-xl font-bold text-blue-900">Detail Calon Orang Tua Asuh</CardTitle>
                            <Button variant="ghost" size="sm" onClick={() => setShowDialog(false)}>
                                <X className="h-4 w-4" />
                            </Button>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div>
                                    <Label className="font-medium text-blue-700">Nama Lengkap</Label>
                                    <p className="text-gray-900">{selectedCalon.nama_lengkap}</p>
                                </div>
                                <div>
                                    <Label className="font-medium text-blue-700">Kabupaten/Kota</Label>
                                    <p className="text-gray-900">{selectedCalon.kabupaten_kota}</p>
                                </div>
                                <div>
                                    <Label className="font-medium text-blue-700">Status</Label>
                                    <div className="mt-1">
                                        <Badge
                                            className={
                                                status === 'Diterima'
                                                    ? 'bg-green-100 text-green-800'
                                                    : status === 'Ditolak'
                                                      ? 'bg-red-100 text-red-800'
                                                      : 'bg-yellow-100 text-yellow-800'
                                            }
                                        >
                                            {status}
                                        </Badge>
                                    </div>
                                </div>
                                <div>
                                    <Label className="font-medium text-blue-700">Tanggal Pendaftaran</Label>
                                    <p className="text-gray-900">{new Date(selectedCalon.created_at).toLocaleDateString('id-ID')}</p>
                                </div>
                            </div>

                            <div>
                                <Label className="font-medium text-blue-700">Dokumen Persyaratan</Label>
                                <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-2">
                                    {dokumenPersyaratanList.map((doc, idx) => {
                                        const filePath = (selectedCalon as any)[doc.field];
                                        return (
                                            <div key={doc.field}>
                                                <p className="text-sm text-gray-600">{doc.label}:</p>
                                                {filePath ? (
                                                    <div className="flex gap-2">
                                                        <a
                                                            href={`/storage/${filePath}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="text-sm text-blue-600 hover:underline"
                                                        >
                                                            Lihat Dokumen
                                                        </a>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => {
                                                                const link = document.createElement('a');
                                                                link.href = `/storage/${filePath}`;
                                                                link.download = `${doc.label}_${selectedCalon.nama_lengkap}`;
                                                                document.body.appendChild(link);
                                                                link.click();
                                                                document.body.removeChild(link);
                                                            }}
                                                            className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                                        >
                                                            <Download className="mr-1 h-3 w-3" />
                                                            Download
                                                        </Button>
                                                    </div>
                                                ) : (
                                                    <span className="text-sm text-red-600">Belum diupload</span>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>

                            {/* Show buttons only if status is still being processed */}
                            {status === 'Diproses' && (
                                <div className="mt-6 flex gap-2">
                                    <Button className="w-full bg-green-600 text-white hover:bg-green-700" onClick={handleAcc}>
                                        ACC
                                    </Button>
                                    <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50" onClick={handleTolak}>
                                        Tolak
                                    </Button>
                                </div>
                            )}

                            {/* Show status message if already processed */}
                            {status === 'Diterima' && (
                                <div className="mt-6 rounded-lg bg-green-50 p-4 text-center">
                                    <p className="text-sm font-medium text-green-700">✅ Pengajuan telah disetujui dan tidak dapat diubah lagi</p>
                                </div>
                            )}

                            {status === 'Ditolak' && (
                                <div className="mt-6 rounded-lg bg-red-50 p-4 text-center">
                                    <p className="text-sm font-medium text-red-700">❌ Pengajuan telah ditolak dan tidak dapat diubah lagi</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Modal Alasan Penolakan */}
            {showRejectionDialog && selectedCalon && (
                <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                    <div className="mx-4 w-full max-w-md rounded-lg bg-white p-6">
                        <div className="mb-4 flex items-center justify-between">
                            <h3 className="text-lg font-semibold text-gray-900">Alasan Penolakan</h3>
                            <button onClick={() => setShowRejectionDialog(false)} className="text-gray-400 hover:text-gray-600">
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                            <p className="text-sm whitespace-pre-wrap text-red-800">
                                {selectedCalon.rejection_reason || 'Tidak ada alasan penolakan yang diberikan.'}
                            </p>
                        </div>
                        <div className="mt-6 flex justify-end">
                            <button
                                onClick={() => setShowRejectionDialog(false)}
                                className="rounded-lg bg-red-600 px-4 py-2 text-white transition-colors hover:bg-red-700"
                            >
                                Tutup
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}
