import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Download, Eye, FileText, Mail, MapPin, Phone, User } from 'lucide-react';

interface CalonOrangTuaAsuh {
    id: number;
    nama_lengkap: string;
    nik: string;
    kabupaten_kota: string;
    alamat: string;
    telepon: string;
    email?: string;
    status: string;
    status_badge: {
        label: string;
        class: string;
    };
    keterangan?: string;
    file_ktp?: string;
    file_kk?: string;
    file_surat_pernikahan?: string;
    file_slip_gaji?: string;
    file_surat_keterangan_sehat?: string;
    created_at: string;
    // Dokumen persyaratan tambahan dari <PERSON>/Kota
    rekomendasi_dinsos?: string;
    permohonan_pemohon?: string;
    surat_sehat_rs?: string;
    surat_kesehatan_jiwa?: string;
    surat_fungsi_reproduksi?: string;
    akta_kelahiran_cota?: string;
    surat_catatan_kepolisian?: string;
    akta_nikah_cota?: string;
    kk_ktp_cota?: string;
    akta_kelahiran_caa?: string;
    surat_penghasilan_cota?: string;
    surat_persetujuan_caa?: string;
    surat_motivasi_cota?: string;
    surat_non_diskriminasi?: string;
    surat_asal_usul?: string;
    surat_wali_nikah?: string;
    surat_hak_waris?: string;
    surat_persetujuan_keluarga?: string;
    laporan_sosial_caa?: string;
    berita_acara_ibu_kandung?: string;
    berita_acara_instansi?: string;
    laporan_cota?: string;
    sk_izin_pengasuhan?: string;
    surat_perjanjian_pengasuhan?: string;
    berita_acara_penyerahan?: string;
    laporan_perkembangan_anak?: string;
    foto_cota_caa?: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    calonOrangTuaAsuh: CalonOrangTuaAsuh;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dinsosriau/dashboard',
    },
    {
        title: 'LKS COTA',
        href: '/dinsosriau/lks/cota',
    },
    {
        title: 'Data Calon Orang Tua Asuh',
        href: '/dinsosriau/lks/cota/data-calon',
    },
    {
        title: 'Detail',
        href: '#',
    },
];

export default function DataCalonDetail({ user, calonOrangTuaAsuh }: Props) {
    const handleDownload = (filePath: string, fileName: string) => {
        const link = document.createElement('a');
        link.href = `/storage/${filePath}`;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handlePreview = (filePath: string) => {
        window.open(`/storage/${filePath}`, '_blank');
    };

    // Daftar lengkap dokumen persyaratan COTA (samakan dengan DaftarCota)
    const documentFields = [
        { field: 'rekomendasi_dinsos', label: 'Rekomendasi di Dinas Sosial Kabupaten/Kota', fileName: 'Rekomendasi_Dinsos' },
        { field: 'permohonan_pemohon', label: 'Permohonan dan Pemohon', fileName: 'Permohonan_Pemohon' },
        { field: 'surat_sehat_rs', label: 'Surat Keterangan Berbadan Sehat dari RS Pemerintah', fileName: 'Surat_Sehat_RS' },
        {
            field: 'surat_kesehatan_jiwa',
            label: 'Surat Kesehatan Jiwa dari Dokter Spesialis Jiwa RS Pemerintah (Suami/Istri)',
            fileName: 'Surat_Kesehatan_Jiwa',
        },
        {
            field: 'surat_fungsi_reproduksi',
            label: 'Surat Keterangan Fungsi Organ Reproduksi (Suami/Istri) dari Dokter Spesialis Obstetric dan Gineologi RS Pemerintah',
            fileName: 'Surat_Fungsi_Reproduksi',
        },
        { field: 'akta_kelahiran_cota', label: 'Foto Copy Akta Kelahiran COTA', fileName: 'Akta_Kelahiran_COTA' },
        { field: 'surat_catatan_kepolisian', label: 'Surat Keterangan Catatan Kepolisian Setempat', fileName: 'Surat_Catatan_Kepolisian' },
        { field: 'akta_nikah_cota', label: 'Foto Copy Nikah/Kutipan Akte Nikah COTA', fileName: 'Akta_Nikah_COTA' },
        { field: 'kk_ktp_cota', label: 'Foto Copy Kartu Keluarga dan KTP COTA', fileName: 'KK_KTP_COTA' },
        { field: 'akta_kelahiran_caa', label: 'Foto Copy Akta Kelahiran CAA', fileName: 'Akta_Kelahiran_CAA' },
        { field: 'surat_penghasilan_cota', label: 'Surat Keterangan Penghasilan dari tempat bekerja COTA', fileName: 'Surat_Penghasilan_COTA' },
        {
            field: 'surat_persetujuan_caa',
            label: 'Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial',
            fileName: 'Surat_Persetujuan_CAA',
        },
        {
            field: 'surat_motivasi_cota',
            label: 'Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan Anak',
            fileName: 'Surat_Motivasi_COTA',
        },
        {
            field: 'surat_non_diskriminasi',
            label: 'Surat Pernyataan COTA akan memperlakukan anak kandung dan anak angkat tanpa diskriminasi',
            fileName: 'Surat_NonDiskriminasi',
        },
        {
            field: 'surat_asal_usul',
            label: 'Surat Pernyataan bahwa COTA akan memberitahukan kepada anak angkat mengenai asal usul dan orangtua kandungnya',
            fileName: 'Surat_Asal_Usul',
        },
        {
            field: 'surat_wali_nikah',
            label: 'Surat Pernyataan COTA tidak berhak menjadi Wali Nikah bagi anak angkat perempuan dan memberi kuasa kepada Wali Hakim',
            fileName: 'Surat_Wali_Nikah',
        },
        {
            field: 'surat_hak_waris',
            label: 'Surat Pernyataan COTA akan memberikan hak waris/hibah atas harta kepada anak angkat sesuai ketentuan hukum islam yang berlaku di Indonesia',
            fileName: 'Surat_Hak_Waris',
        },
        {
            field: 'surat_persetujuan_keluarga',
            label: 'Surat Pernyataan persetujuan adopsi dari pihak keluarga COTA',
            fileName: 'Surat_Persetujuan_Keluarga',
        },
        {
            field: 'laporan_sosial_caa',
            label: 'Laporan Sosial Calon Anak Angkat yang dibuat oleh pekerja sosial instansi sosial setempat dan pekerja sosial Panti / Yayasan',
            fileName: 'Laporan_Sosial_CAA',
        },
        {
            field: 'berita_acara_ibu_kandung',
            label: 'Surat/Berita Acara Penyerahan dan Kuasa dari Pihak ibu kandung kepada instansi sosial setempat/ COTA',
            fileName: 'Berita_Acara_Ibu_Kandung',
        },
        {
            field: 'berita_acara_instansi',
            label: 'Surat/Berita acara Penyerahan dan Kuasa dari Pihak instansi sosial setempat dan pekerja sosial Panti / Yayasan',
            fileName: 'Berita_Acara_Instansi',
        },
        {
            field: 'laporan_cota',
            label: 'Laporan Calon Orangtua Angkat yang dibuat oleh pekerja sosial instansi sosial setempat dan pekerja sosial/yayasan',
            fileName: 'Laporan_COTA',
        },
        { field: 'sk_izin_pengasuhan', label: 'SK Pemberian Izin Pengasuhan Anak', fileName: 'SK_Izin_Pengasuhan' },
        {
            field: 'surat_perjanjian_pengasuhan',
            label: 'Surat Perjanjian Pengasuhan Anak Antara Panti / Yayasan dengan COTA',
            fileName: 'Surat_Perjanjian_Pengasuhan',
        },
        {
            field: 'berita_acara_penyerahan',
            label: 'Surat/Berita Acara Penyerahan Anak dari Panti/Yayasan kepada COTA',
            fileName: 'Berita_Acara_Penyerahan',
        },
        {
            field: 'laporan_perkembangan_anak',
            label: 'Laporan Perkembangan Anak yang dibuat oleh Pekerja Sosial Instansi Sosial setempat dan Pekerja Sosial Panti/Yayasan',
            fileName: 'Laporan_Perkembangan_Anak',
        },
        { field: 'foto_cota_caa', label: 'Foto Calon Orang Tua Angkat dan Calon Anak Angkat', fileName: 'Foto_COTA_CAA' },
    ];

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title={`Detail - ${calonOrangTuaAsuh.nama_lengkap}`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header Section */}
                <div className="flex items-center gap-4">
                    <Link href="/dinsosriau/lks/cota/data-calon">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Detail Calon Orang Tua Asuh</h1>
                        <p className="mt-1 text-blue-600">Informasi lengkap calon orang tua asuh</p>
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Main Information */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Personal Information */}
                        <Card className="border-blue-200 shadow-lg">
                            <CardHeader className="border-b border-blue-200 bg-blue-50">
                                <CardTitle className="flex items-center text-blue-900">
                                    <User className="mr-2 h-5 w-5" />
                                    Informasi Personal
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4 p-6">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Nama Lengkap</label>
                                        <p className="font-medium text-blue-900">{calonOrangTuaAsuh.nama_lengkap}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">NIK</label>
                                        <p className="font-medium text-blue-900">{calonOrangTuaAsuh.nik}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Kabupaten/Kota</label>
                                        <p className="font-medium text-blue-900">{calonOrangTuaAsuh.kabupaten_kota}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Status</label>
                                        <div className="mt-1">
                                            <Badge className={calonOrangTuaAsuh.status_badge.class}>{calonOrangTuaAsuh.status_badge.label}</Badge>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Alamat</label>
                                    <div className="mt-1 flex items-start">
                                        <MapPin className="mt-0.5 mr-2 h-4 w-4 flex-shrink-0 text-muted-foreground" />
                                        <p className="text-blue-900">{calonOrangTuaAsuh.alamat}</p>
                                    </div>
                                </div>

                                <div className="grid gap-4 md:grid-cols-2">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Telepon</label>
                                        <div className="mt-1 flex items-center">
                                            <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                                            <p className="text-blue-900">{calonOrangTuaAsuh.telepon}</p>
                                        </div>
                                    </div>
                                    {calonOrangTuaAsuh.email && (
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Email</label>
                                            <div className="mt-1 flex items-center">
                                                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                                                <p className="text-blue-900">{calonOrangTuaAsuh.email}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {calonOrangTuaAsuh.keterangan && (
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Keterangan</label>
                                        <p className="mt-1 text-blue-900">{calonOrangTuaAsuh.keterangan}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Documents Section */}
                        <Card className="border-blue-200 shadow-lg">
                            <CardHeader className="border-b border-blue-200 bg-blue-50">
                                <CardTitle className="flex items-center text-blue-900">
                                    <FileText className="mr-2 h-5 w-5" />
                                    Dokumen Persyaratan
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                                <div className="space-y-4">
                                    {documentFields.map((doc) => {
                                        const filePath = calonOrangTuaAsuh[doc.field as keyof CalonOrangTuaAsuh] as string;
                                        return (
                                            <div key={doc.field} className="flex items-center justify-between rounded-lg border border-gray-200 p-4">
                                                <div className="flex items-center">
                                                    <FileText className="mr-3 h-5 w-5 text-blue-600" />
                                                    <div>
                                                        <p className="font-medium text-blue-900">{doc.label}</p>
                                                        {filePath ? (
                                                            <p className="text-sm text-green-600">Dokumen tersedia</p>
                                                        ) : (
                                                            <p className="text-sm text-red-600">Dokumen belum diupload</p>
                                                        )}
                                                    </div>
                                                </div>
                                                {filePath && (
                                                    <div className="flex gap-2">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handlePreview(filePath)}
                                                            className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                                        >
                                                            <Eye className="mr-1 h-3 w-3" />
                                                            Preview
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() =>
                                                                handleDownload(filePath, `${doc.fileName}_${calonOrangTuaAsuh.nama_lengkap}`)
                                                            }
                                                            className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                                        >
                                                            <Download className="mr-1 h-3 w-3" />
                                                            Download
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Status Card */}
                        <Card className="border-blue-200">
                            <CardHeader className="border-b border-blue-200 bg-blue-50">
                                <CardTitle className="text-lg text-blue-900">Status Pengajuan</CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                                <div className="text-center">
                                    <Badge className={`${calonOrangTuaAsuh.status_badge.class} px-4 py-2 text-lg`}>
                                        {calonOrangTuaAsuh.status_badge.label}
                                    </Badge>
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Tanggal Daftar: {new Date(calonOrangTuaAsuh.created_at).toLocaleDateString('id-ID')}
                                    </p>
                                    {calonOrangTuaAsuh.status === 'ditolak' && calonOrangTuaAsuh.keterangan && (
                                        <Button
                                            variant="outline"
                                            className="mt-3 border-red-300 text-red-600 hover:bg-red-50"
                                            onClick={() => alert(`Alasan penolakan: ${calonOrangTuaAsuh.keterangan}`)}
                                        >
                                            Info Penolakan
                                        </Button>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Action Buttons */}
                        <Card className="border-blue-200">
                            <CardHeader className="border-b border-blue-200 bg-blue-50">
                                <CardTitle className="text-lg text-blue-900">Aksi</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3 p-6">
                                <Button className="w-full bg-green-600 hover:bg-green-700">Terima Pengajuan</Button>
                                <Button variant="outline" className="w-full border-red-300 text-red-600 hover:bg-red-50">
                                    Tolak Pengajuan
                                </Button>
                                <Button variant="outline" className="w-full border-blue-300 text-blue-600 hover:bg-blue-50">
                                    Edit Data
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </DinsosRiauLayout>
    );
}
