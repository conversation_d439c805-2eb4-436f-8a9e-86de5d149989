import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, ChevronLeft, ChevronRight, Download, Eye, File, FileText, Image } from 'lucide-react';

interface DokumenPersyaratan {
    id: number;
    nama_dokumen: string;
    jenis_berkas: string;
    file_path: string;
    deskripsi?: string;
    is_active: boolean;
    file_extension: string;
    file_icon: string;
    created_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    dokumenPersyaratan: DokumenPersyaratan[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dinsosriau/dashboard',
    },
    {
        title: 'LKS COTA',
        href: '/dinsosriau/lks/cota',
    },
    {
        title: 'Dokumen Persyaratan',
        href: '/dinsosriau/lks/cota/dokumen-persyaratan',
    },
];

export default function DokumenPersyaratan({ user, dokumenPersyaratan, pagination }: Props) {
    const handleDownload = (filePath: string, fileName: string) => {
        const link = document.createElement('a');
        link.href = `/storage/${filePath}`;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handlePreview = (filePath: string) => {
        window.open(`/storage/${filePath}`, '_blank');
    };

    const getFileIcon = (iconType: string) => {
        switch (iconType) {
            case 'file-text':
                return <FileText className="h-5 w-5 text-blue-600" />;
            case 'image':
                return <Image className="h-5 w-5 text-green-600" />;
            default:
                return <File className="h-5 w-5 text-gray-600" />;
        }
    };

    const getFileExtensionColor = (extension: string) => {
        switch (extension.toLowerCase()) {
            case 'pdf':
                return 'bg-red-100 text-red-800';
            case 'doc':
            case 'docx':
                return 'bg-blue-100 text-blue-800';
            case 'jpg':
            case 'jpeg':
            case 'png':
                return 'bg-green-100 text-green-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Dokumen Persyaratan Pengangkatan Anak" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Info Card - moved to top and restyled */}
                <Card
                    className="mb-2 w-full border-0 bg-white/30 shadow-2xl ring-1 ring-white/40 backdrop-blur-lg ring-inset"
                    style={{ boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.18)' }}
                >
                    <CardContent className="flex items-center gap-4 p-6">
                        <div className="flex h-14 w-14 items-center justify-center rounded-full bg-blue-100/60">
                            <FileText className="h-7 w-7 text-blue-600" />
                        </div>
                        <div>
                            <h2 className="mb-1 text-xl font-bold text-blue-900">Informasi Dokumen</h2>
                            <p className="text-base text-blue-800">
                                Dokumen-dokumen di bawah merupakan persyaratan yang harus dipenuhi oleh calon orang tua asuh dalam proses pengangkatan
                                anak. Setiap dokumen dapat diunduh dan digunakan sebagai referensi atau template untuk permohonan pengangkatan anak
                                asuh.
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Header Section */}
                <div className="flex items-center gap-4">
                    <Link href="/dinsosriau/lks/cota">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Dokumen Persyaratan Pengangkatan Anak</h1>
                        <p className="mt-1 text-blue-600">Kelola dokumen persyaratan untuk pengangkatan anak asuh</p>
                    </div>
                </div>

                {/* Table Card */}
                <Card className="border-blue-200 shadow-lg">
                    <CardHeader className="border-b border-blue-200 bg-blue-50">
                        <CardTitle className="flex items-center justify-between text-blue-900">
                            <span>Daftar Dokumen Persyaratan</span>
                            <span className="text-sm font-normal text-blue-600">{dokumenPersyaratan.length} dokumen</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-blue-50">
                                        <TableHead className="w-16 font-semibold text-blue-900">No</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Berkas</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Dokumen</TableHead>
                                        <TableHead className="text-center font-semibold text-blue-900">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {dokumenPersyaratan.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={4} className="py-8 text-center text-blue-500">
                                                Tidak ada dokumen yang tersedia
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        dokumenPersyaratan.map((item, index) => (
                                            <TableRow key={item.id} className="transition-colors hover:bg-blue-50">
                                                <TableCell className="font-medium text-blue-900">{pagination.from + index}</TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-3">
                                                        {getFileIcon(item.file_icon)}
                                                        <div>
                                                            <div className="flex items-center gap-2">
                                                                <span className="font-medium text-blue-900">{item.jenis_berkas.toUpperCase()}</span>
                                                                <span
                                                                    className={`rounded-full px-2 py-1 text-xs ${getFileExtensionColor(item.file_extension)}`}
                                                                >
                                                                    {item.file_extension.toUpperCase()}
                                                                </span>
                                                            </div>
                                                            <p className="text-sm text-muted-foreground">{item.file_path.split('/').pop()}</p>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div>
                                                        <p className="font-medium text-blue-900">{item.nama_dokumen}</p>
                                                        {item.deskripsi && <p className="mt-1 text-sm text-muted-foreground">{item.deskripsi}</p>}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="flex justify-center gap-2">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handlePreview(item.file_path)}
                                                            className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                                        >
                                                            <Eye className="h-3 w-3" />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleDownload(item.file_path, item.nama_dokumen)}
                                                            className="border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                                        >
                                                            <Download className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {pagination.last_page > 1 && (
                            <div className="mt-4 flex items-center justify-between px-4 pb-4">
                                <div className="text-sm text-gray-700">
                                    Menampilkan {pagination.from} hingga {pagination.to} dari {pagination.total} data
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                        disabled={pagination.current_page === 1}
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        Sebelumnya
                                    </Button>
                                    <span className="flex items-center px-3 text-sm text-gray-600">
                                        Halaman {pagination.current_page} dari {pagination.last_page}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                        disabled={pagination.current_page === pagination.last_page}
                                    >
                                        Selanjutnya
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Info Card moved to top, see above */}
            </div>
        </DinsosRiauLayout>
    );
}
