import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, ChevronLeft, ChevronRight, Clock, MapPin, Pencil, Plus, Trash2, Users, X } from 'lucide-react';
import { useState } from 'react';

interface JadwalSidang {
    id: number;
    tanggal_sidang: string;
    waktu_mulai: string;
    waktu_selesai: string;
    tempat_sidang: string;
    agenda?: string;
    status: string;
    status_badge: {
        label: string;
        class: string;
    };
    kapasitas: number;
    keterangan?: string;
    created_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    jadwalSidang: JadwalSidang[];
    pagination: Pagination;
}

const JadwalSidang = ({ user, jadwalSidang, pagination }: Props) => {
    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dinsosriau/dashboard',
        },
        {
            title: 'LKS COTA',
            href: '/dinsosriau/lks/cota',
        },
        {
            title: 'Kelola Penjadwalan',
            href: '/dinsosriau/lks/cota/jadwal-sidang',
        },
    ];

    const [showForm, setShowForm] = useState(false);
    const [editingJadwal, setEditingJadwal] = useState<JadwalSidang | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<JadwalSidang | null>(null);

    const {
        data,
        setData,
        post,
        patch,
        delete: destroy,
        processing,
        errors,
        reset,
    } = useForm({
        tanggal_sidang: '',
        waktu_mulai: '',
        waktu_selesai: '',
        tempat_sidang: '',
        agenda: '',
        kapasitas: 10,
        keterangan: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingJadwal) {
            patch(`/dinsosriau/lks/cota/jadwal-sidang/${editingJadwal.id}`, {
                onSuccess: () => {
                    reset();
                    setShowForm(false);
                    setEditingJadwal(null);
                },
            });
        } else {
            post('/dinsosriau/lks/cota/jadwal-sidang', {
                onSuccess: () => {
                    reset();
                    setShowForm(false);
                },
            });
        }
    };

    const handleEdit = (jadwal: JadwalSidang) => {
        setEditingJadwal(jadwal);
        setData({
            tanggal_sidang: jadwal.tanggal_sidang,
            waktu_mulai: jadwal.waktu_mulai,
            waktu_selesai: jadwal.waktu_selesai,
            tempat_sidang: jadwal.tempat_sidang,
            agenda: jadwal.agenda || '',
            kapasitas: jadwal.kapasitas,
            keterangan: jadwal.keterangan || '',
        });
        setShowForm(true);
    };

    const handleDelete = (jadwal: JadwalSidang) => {
        setShowDeleteConfirm(jadwal);
    };

    const confirmDelete = () => {
        if (showDeleteConfirm) {
            destroy(`/dinsosriau/lks/cota/jadwal-sidang/${showDeleteConfirm.id}`, {
                onSuccess: () => {
                    setShowDeleteConfirm(null);
                },
            });
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatTime = (timeString: string) => {
        return new Date(`2000-01-01 ${timeString}`).toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Kelola Penjadwalan Sidang" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Informasi Penjadwalan Section (moved to top, full width) */}
                <Card className="mb-4 w-full border-blue-200 bg-blue-50 shadow-md">
                    <CardContent className="flex flex-col gap-6 p-8 md:flex-row md:items-center md:justify-between">
                        <div className="flex w-full items-start gap-4">
                            <Calendar className="mt-1 h-10 w-10 flex-shrink-0 text-blue-600" />
                            <div className="flex-1">
                                <h3 className="mb-2 text-2xl font-bold text-blue-900">Informasi Penjadwalan</h3>
                                <p className="text-base text-blue-700">
                                    Sistem penjadwalan sidang memungkinkan pengelolaan jadwal sidang pengangkatan anak secara terstruktur. Setiap
                                    jadwal dapat dikelola statusnya dari draft hingga final, dengan kapasitas dan agenda yang dapat disesuaikan.
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Header Section */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div className="flex items-center gap-4">
                        <Link href="/dinsosriau/lks/cota">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold text-blue-900">Kelola Penjadwalan</h1>
                            <p className="mt-1 text-blue-600">Atur jadwal sidang pengangkatan anak asuh</p>
                        </div>
                    </div>

                    <Button onClick={() => setShowForm(!showForm)} className="bg-blue-600 hover:bg-blue-700">
                        <Plus className="mr-2 h-4 w-4" />
                        Buat Jadwal Baru
                    </Button>
                </div>

                {/* Form Create Jadwal */}
                {showForm && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-lg border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 border-b border-blue-200 bg-blue-50 pb-2">
                                <CardTitle className="text-blue-900">{editingJadwal ? 'Edit Jadwal Sidang' : 'Buat Jadwal Sidang Baru'}</CardTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                        setShowForm(false);
                                        setEditingJadwal(null);
                                        reset();
                                    }}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="p-6">
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="tanggal_sidang">Tanggal Sidang</Label>
                                            <Input
                                                id="tanggal_sidang"
                                                type="date"
                                                value={data.tanggal_sidang}
                                                onChange={(e) => setData('tanggal_sidang', e.target.value)}
                                                className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            />
                                            {errors.tanggal_sidang && <p className="text-sm text-red-600">{errors.tanggal_sidang}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="tempat_sidang">Tempat Sidang</Label>
                                            <Input
                                                id="tempat_sidang"
                                                value={data.tempat_sidang}
                                                onChange={(e) => setData('tempat_sidang', e.target.value)}
                                                placeholder="Masukkan tempat sidang"
                                                className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            />
                                            {errors.tempat_sidang && <p className="text-sm text-red-600">{errors.tempat_sidang}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="waktu_mulai">Waktu Mulai</Label>
                                            <Input
                                                id="waktu_mulai"
                                                type="time"
                                                value={data.waktu_mulai}
                                                onChange={(e) => setData('waktu_mulai', e.target.value)}
                                                className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            />
                                            {errors.waktu_mulai && <p className="text-sm text-red-600">{errors.waktu_mulai}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="waktu_selesai">Waktu Selesai</Label>
                                            <Input
                                                id="waktu_selesai"
                                                type="time"
                                                value={data.waktu_selesai}
                                                onChange={(e) => setData('waktu_selesai', e.target.value)}
                                                className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            />
                                            {errors.waktu_selesai && <p className="text-sm text-red-600">{errors.waktu_selesai}</p>}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="kapasitas">Kapasitas</Label>
                                            <Input
                                                id="kapasitas"
                                                type="number"
                                                min="1"
                                                value={data.kapasitas}
                                                onChange={(e) => setData('kapasitas', parseInt(e.target.value))}
                                                className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            />
                                            {errors.kapasitas && <p className="text-sm text-red-600">{errors.kapasitas}</p>}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="agenda">Agenda (Opsional)</Label>
                                        <Textarea
                                            id="agenda"
                                            value={data.agenda}
                                            onChange={(e) => setData('agenda', e.target.value)}
                                            placeholder="Masukkan agenda sidang"
                                            className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            rows={3}
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="keterangan">Keterangan (Opsional)</Label>
                                        <Textarea
                                            id="keterangan"
                                            value={data.keterangan}
                                            onChange={(e) => setData('keterangan', e.target.value)}
                                            placeholder="Masukkan keterangan tambahan"
                                            className="border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                                            rows={3}
                                        />
                                    </div>

                                    <div className="flex justify-end gap-4">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => {
                                                setShowForm(false);
                                                setEditingJadwal(null);
                                                reset();
                                            }}
                                            className="border-gray-300"
                                        >
                                            Batal
                                        </Button>
                                        <Button type="submit" disabled={processing} className="bg-blue-600 hover:bg-blue-700">
                                            {processing ? 'Menyimpan...' : editingJadwal ? 'Perbarui Jadwal' : 'Simpan Jadwal'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Table Card */}
                <Card className="border-blue-200 shadow-lg">
                    <CardHeader className="border-b border-blue-200 bg-blue-50">
                        <CardTitle className="flex items-center justify-between text-blue-900">
                            <span>Daftar Jadwal Sidang</span>
                            <span className="text-sm font-normal text-blue-600">{jadwalSidang.length} jadwal</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-blue-50">
                                        <TableHead className="w-16 font-semibold text-blue-900">No</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Tanggal & Waktu</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Tempat</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Kapasitas</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Status</TableHead>
                                        <TableHead className="font-semibold text-blue-900">Agenda</TableHead>
                                        <TableHead className="text-center font-semibold text-blue-900">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {jadwalSidang.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={6} className="py-8 text-center text-blue-500">
                                                Belum ada jadwal sidang yang dibuat
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        jadwalSidang.map((item, index) => (
                                            <TableRow key={item.id} className="transition-colors hover:bg-blue-50">
                                                <TableCell className="font-medium text-blue-900">{pagination.from + index}</TableCell>
                                                <TableCell>
                                                    <div className="space-y-1">
                                                        <div className="flex items-center gap-2">
                                                            <Calendar className="h-4 w-4 text-blue-600" />
                                                            <span className="font-medium text-blue-900">{formatDate(item.tanggal_sidang)}</span>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <Clock className="h-4 w-4 text-blue-600" />
                                                            <span className="text-sm text-blue-700">
                                                                {formatTime(item.waktu_mulai)} - {formatTime(item.waktu_selesai)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <MapPin className="h-4 w-4 text-blue-600" />
                                                        <span className="text-blue-900">{item.tempat_sidang}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <Users className="h-4 w-4 text-blue-600" />
                                                        <span className="text-blue-900">{item.kapasitas} orang</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge className={item.status_badge.class}>{item.status_badge.label}</Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {item.agenda ? (
                                                        <p className="max-w-xs truncate text-sm text-blue-900">{item.agenda}</p>
                                                    ) : (
                                                        <span className="text-sm text-muted-foreground">-</span>
                                                    )}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <div className="flex justify-center gap-2">
                                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleEdit(item)}>
                                                            <Pencil className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-8 w-8 p-0 text-red-600"
                                                            onClick={() => handleDelete(item)}
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {pagination.last_page > 1 && (
                            <div className="mt-4 flex items-center justify-between px-4 pb-4">
                                <div className="text-sm text-gray-700">
                                    Menampilkan {pagination.from} hingga {pagination.to} dari {pagination.total} data
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                        disabled={pagination.current_page === 1}
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        Sebelumnya
                                    </Button>
                                    <span className="flex items-center px-3 text-sm text-gray-600">
                                        Halaman {pagination.current_page} dari {pagination.last_page}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                        disabled={pagination.current_page === pagination.last_page}
                                    >
                                        Selanjutnya
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* Modal Konfirmasi Hapus */}
            {showDeleteConfirm && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                    <Card className="m-4 w-full max-w-md border border-red-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-xl font-bold text-red-600">Konfirmasi Hapus</CardTitle>
                            <Button variant="ghost" size="sm" onClick={() => setShowDeleteConfirm(null)}>
                                <X className="h-4 w-4" />
                            </Button>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-gray-700">Apakah Anda yakin ingin menghapus jadwal sidang ini? Tindakan ini tidak dapat dibatalkan.</p>
                            <div className="flex justify-end gap-2">
                                <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                                    Batal
                                </Button>
                                <Button variant="destructive" onClick={confirmDelete} disabled={processing}>
                                    Hapus
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </DinsosRiauLayout>
    );
};

export default JadwalSidang;
