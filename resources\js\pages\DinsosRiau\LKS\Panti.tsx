import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Building2, Calendar, ClipboardList, DollarSign, FileText } from 'lucide-react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Panti Asuhan',
        href: '/dinsosriau/lks/panti',
    },
];

export default function LKSPanti({ user }: Props) {
    const menuItems = [
        {
            title: 'Daftar Panti Asuhan',
            description: 'Kelola data dan informasi panti asuhan yang terdaftar',
            icon: Building2,
            color: 'bg-blue-50 hover:bg-blue-100',
            iconColor: 'text-blue-600',
            href: '/dinsosriau/lks/panti/daftar',
        },
        {
            title: 'Atur Jadwal Kunjungan',
            description: '<PERSON><PERSON><PERSON><PERSON> dan kelola kunjungan ke panti asuhan',
            icon: Calendar,
            color: 'bg-green-50 hover:bg-green-100',
            iconColor: 'text-green-600',
            href: '/dinsosriau/lks/panti/jadwal',
        },
        {
            title: 'Verifikasi Pengajuan Dana',
            description: 'Verifikasi dan proses pengajuan dana bantuan',
            icon: DollarSign,
            color: 'bg-orange-50 hover:bg-orange-100',
            iconColor: 'text-orange-600',
            href: '/dinsosriau/lks/panti/verif',
        },
        {
            title: 'Cek Laporan Kegiatan',
            description: 'Review dan monitoring laporan kegiatan panti',
            icon: FileText,
            color: 'bg-purple-50 hover:bg-purple-100',
            iconColor: 'text-purple-600',
            href: '/dinsosriau/lks/panti/laporan',
        },
    ];

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="LKS Panti Asuhan" />
            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Informasi Layanan - Moved to top and full width */}
                <div className="w-full">
                    <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="p-6">
                            <div className="flex items-start gap-3">
                                <div className="rounded-full bg-blue-100 p-2">
                                    <Building2 className="h-5 w-5 text-blue-600" />
                                </div>
                                <div className="flex-1">
                                    <h3 className="mb-2 font-semibold text-blue-900">Informasi Layanan</h3>
                                    <p className="text-sm leading-relaxed text-blue-800">
                                        Sistem manajemen panti asuhan terintegrasi untuk mendukung pelayanan kesejahteraan sosial yang lebih efektif
                                        dan transparan. Untuk bantuan teknis, hubungi tim IT Dinas Sosial Provinsi Riau.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Header Section */}
                <div className="text-center">
                    <h1 className="mb-2 text-3xl font-bold text-gray-900">Layanan Kesejahteraan Sosial</h1>
                    <p className="mb-4 text-lg text-gray-600">Panti Asuhan</p>
                    <div className="flex items-center justify-center gap-2 text-blue-600">
                        <ClipboardList className="h-5 w-5" />
                        <span className="text-sm font-medium">Pilih layanan yang ingin Anda akses</span>
                    </div>
                </div>

                {/* Menu Cards Grid */}
                <div className="mx-auto grid max-w-4xl gap-6 md:grid-cols-2 lg:grid-cols-2">
                    {menuItems.map((item, index) => {
                        const IconComponent = item.icon;
                        return (
                            <Card
                                key={index}
                                className={`${item.color} group cursor-pointer border-0 shadow-md transition-all duration-200 hover:shadow-lg`}
                            >
                                <CardHeader className="pb-4">
                                    <div className="flex items-center gap-4">
                                        <div className="rounded-full bg-white p-3 shadow-sm">
                                            <IconComponent className={`h-8 w-8 ${item.iconColor}`} />
                                        </div>
                                        <div>
                                            <CardTitle className="text-xl font-semibold text-gray-900 group-hover:text-gray-700">
                                                {item.title}
                                            </CardTitle>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="pt-0">
                                    <CardDescription className="text-base leading-relaxed text-gray-700">{item.description}</CardDescription>
                                    <div className="mt-4">
                                        <Button
                                            variant="outline"
                                            className="w-full border-gray-200 bg-white font-medium text-gray-700 hover:bg-gray-50"
                                            asChild
                                        >
                                            <a href={item.href}>Akses Layanan</a>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>
            </div>
        </DinsosRiauLayout>
    );
}
