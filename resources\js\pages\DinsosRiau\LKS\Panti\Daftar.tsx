// Mapping pen<PERSON><PERSON>an jenis do<PERSON>
const DOKUMEN_EXPLANATIONS: Record<string, string> = {
    NPWP: 'Nomor Pokok Wajib <PERSON>',
    Akta: 'Akta Pendirian',
    'SK Izin Operasional': 'Surat Keputusan Izin Operasional',
    'SK Pengangkatan Pengurus': 'Surat Keputusan Pengangkatan Pengurus',
    'SK Domisili': 'Surat Keterangan Domisili',
    'SK Kemenkumham': 'Surat Keputusan Kemenkumham',
    'Rekening Bank': 'Buku Rekening Bank',
    'Profil Panti': 'Profil/Laporan Panti',
    // Tambahkan mapping lain sesuai kebutuhan
};

function getDokumenExplanation(nama: string) {
    if (!nama) return '';
    if (DOKUMEN_EXPLANATIONS[nama]) return DOKUMEN_EXPLANATIONS[nama];
    const found = Object.keys(DOKUMEN_EXPLANATIONS).find((key) => nama.toLowerCase().includes(key.toLowerCase()));
    return found ? DOKUMEN_EXPLANATIONS[found] : '';
}
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { ArrowLeft, Building2, CheckCircle, ChevronLeft, ChevronRight, Edit, Eye, Info, Users, X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Panti {
    id: number;
    nama: string;
    alamat: string;
    telepon: string;
    email?: string;
    pimpinan: string;
    tanggal_pendirian: string;
    kapasitas: number;
    jumlah_anak: number;
    status: 'pending' | 'diproses' | 'diverifikasi' | 'approved' | 'ditolak' | 'rejected';
    admin_notes?: string;
    created_at: string;
    updated_at: string;
    kabupaten?: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
        created_at: string;
    };
}

interface Pagination {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    panti: Panti[];
    pagination: Pagination;
    flash?: {
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Panti Asuhan',
        href: '/dinsosriau/lks/panti',
    },
    {
        title: 'Daftar Panti Asuhan',
        href: '/dinsosriau/lks/panti/daftar',
    },
];

export default function DaftarPanti({ user, panti, pagination, flash }: Props) {
    const [infoModal, setInfoModal] = useState<{ open: boolean; panti?: Panti | null }>({ open: false, panti: null });
    const [pantiInfo, setPantiInfo] = useState<any>(null);
    const [loadingPantiInfo, setLoadingPantiInfo] = useState(false);
    // Fetch panti info and documents when info modal is opened
    useEffect(() => {
        const fetchPantiInfo = async (pantiId: number) => {
            setLoadingPantiInfo(true);
            try {
                const response = await fetch(`/dinsosriau/lks/panti/${pantiId}/info`);
                const result = await response.json();
                if (result.success) {
                    setPantiInfo(result.data);
                } else {
                    setPantiInfo(null);
                }
            } catch (error) {
                setPantiInfo(null);
            } finally {
                setLoadingPantiInfo(false);
            }
        };
        if (infoModal.open && infoModal.panti) {
            fetchPantiInfo(infoModal.panti.id);
        } else {
            setPantiInfo(null);
        }
    }, [infoModal]);
    const [anakModal, setAnakModal] = useState<{ open: boolean; panti?: Panti | null }>({ open: false, panti: null });
    const [editModal, setEditModal] = useState<{ open: boolean; panti?: Panti | null }>({ open: false, panti: null });
    const [editForm, setEditForm] = useState({ status: '', admin_notes: '' });
    const [loading, setLoading] = useState(false);
    const [anakAsuhData, setAnakAsuhData] = useState<Record<number, any[]>>({});
    const [loadingAnakAsuh, setLoadingAnakAsuh] = useState(false);
    const [viewingAnak, setViewingAnak] = useState<any | null>(null);

    // Function to fetch anak asuh data from API
    const fetchAnakAsuh = async (pantiId: number, forceRefresh = false) => {
        if (anakAsuhData[pantiId] && !forceRefresh) {
            // Data already exists, no need to fetch again unless forced
            return;
        }

        setLoadingAnakAsuh(true);
        try {
            // Add timestamp to prevent caching
            const timestamp = new Date().getTime();
            const response = await fetch(`/dinsosriau/lks/panti/${pantiId}/anak-asuh?t=${timestamp}`);
            const result = await response.json();

            if (result.success) {
                setAnakAsuhData((prev) => ({
                    ...prev,
                    [pantiId]: result.data,
                }));
            }
        } catch (error) {
            console.error('Error fetching anak asuh data:', error);
        } finally {
            setLoadingAnakAsuh(false);
        }
    };

    // Handle opening anak modal and fetch data
    const handleAnakModalOpen = (panti: Panti) => {
        setAnakModal({ open: true, panti });
        // Always fetch fresh data when opening modal
        fetchAnakAsuh(panti.id, true);
    };

    const getStatusExplanation = (item: Panti) => {
        if (item.status === 'approved' || item.status === 'diverifikasi') {
            return `Panti ini telah disetujui dan terdaftar secara resmi.`;
        } else if (item.status === 'rejected' || item.status === 'ditolak') {
            return item.admin_notes ? `Panti ini ditolak. Alasan: ${item.admin_notes}` : 'Panti ini ditolak.';
        } else if (item.status === 'diproses') {
            return `Panti ini sedang dalam proses verifikasi.`;
        } else {
            return `Panti ini masih menunggu proses verifikasi.`;
        }
    };

    const handleEditClick = (pantiItem: Panti) => {
        setEditForm({
            status: pantiItem.status,
            admin_notes: pantiItem.admin_notes || '',
        });
        setEditModal({ open: true, panti: pantiItem });
    };

    const handleStatusUpdate = async () => {
        if (!editModal.panti) return;

        setLoading(true);
        try {
            router.put(`/dinsosriau/lks/panti/${editModal.panti.id}/status`, editForm, {
                onSuccess: () => {
                    setEditModal({ open: false, panti: null });
                    setEditForm({ status: '', admin_notes: '' });
                },
                onError: (errors) => {
                    console.error('Error updating status:', errors);
                },
                onFinish: () => setLoading(false),
            });
        } catch (error) {
            console.error('Error updating status:', error);
            setLoading(false);
        }
    };

    const getStatusDisplay = (status: string) => {
        switch (status) {
            case 'pending':
                return 'Menunggu';
            case 'diproses':
                return 'Diproses';
            case 'diverifikasi':
            case 'approved':
                return 'Diverifikasi';
            case 'ditolak':
            case 'rejected':
                return 'Ditolak';
            default:
                return status;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'diverifikasi':
            case 'approved':
                return 'bg-green-100 text-green-800';
            case 'diproses':
                return 'bg-blue-100 text-blue-800';
            case 'ditolak':
            case 'rejected':
                return 'bg-red-100 text-red-800';
            case 'pending':
            default:
                return 'bg-yellow-100 text-yellow-800';
        }
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Daftar Panti Asuhan" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <a href="/dinsosriau/lks/panti">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </a>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Daftar Panti Asuhan</h1>
                        <p className="mt-1 text-blue-600">Kelola data dan informasi panti asuhan yang terdaftar</p>
                    </div>
                </div>

                {/* Flash Messages */}
                {flash?.success && (
                    <div className="rounded-md border border-green-200 bg-green-50 p-4">
                        <div className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-green-800">{flash.success}</p>
                            </div>
                        </div>
                    </div>
                )}
                {flash?.error && (
                    <div className="rounded-md border border-red-200 bg-red-50 p-4">
                        <div className="flex items-center">
                            <X className="h-4 w-4 text-red-400" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-red-800">{flash.error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Content */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Building2 className="h-5 w-5" />
                            <span>Data Panti Asuhan Terdaftar</span>
                        </CardTitle>
                        <CardDescription>Informasi lengkap panti asuhan yang terdaftar di Provinsi Riau</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-12">No</TableHead>
                                        <TableHead>Nama Panti</TableHead>
                                        <TableHead>Pimpinan</TableHead>
                                        <TableHead>Alamat</TableHead>
                                        <TableHead>Kabupaten/Kota</TableHead>
                                        <TableHead>Tgl Daftar</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-center">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {panti.length > 0 ? (
                                        panti.map((item, index) => (
                                            <TableRow key={item.id}>
                                                <TableCell className="font-medium">
                                                    {(pagination.current_page - 1) * pagination.per_page + index + 1}
                                                </TableCell>
                                                <TableCell className="font-medium">
                                                    <div className="flex items-center gap-2">
                                                        <div>
                                                            <div className="flex items-center gap-2">
                                                                {item.nama}
                                                                {/* Tag online dihapus sesuai permintaan */}
                                                            </div>
                                                            {item.user && <div className="text-xs text-gray-500">Oleh: {item.user.name}</div>}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>{item.pimpinan}</TableCell>
                                                <TableCell className="max-w-xs truncate">{item.alamat}</TableCell>
                                                <TableCell>
                                                    {item.kabupaten
                                                        ? item.kabupaten
                                                        : item.alamat
                                                          ? item.alamat.split(',').pop()?.trim() || '-'
                                                          : '-'}
                                                </TableCell>
                                                <TableCell className="text-sm">
                                                    {new Date(item.created_at).toLocaleDateString('id-ID', {
                                                        day: '2-digit',
                                                        month: '2-digit',
                                                        year: 'numeric',
                                                    })}
                                                </TableCell>
                                                <TableCell>
                                                    <span
                                                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(item.status)}`}
                                                    >
                                                        <CheckCircle className="mr-1 h-4 w-4" />
                                                        {getStatusDisplay(item.status)}
                                                    </span>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex justify-center space-x-2">
                                                        {/* Tampilkan tombol edit hanya jika status belum diverifikasi/approved */}
                                                        {item.status !== 'diverifikasi' && item.status !== 'approved' && (
                                                            <Button variant="outline" size="sm" onClick={() => handleEditClick(item)}>
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                        )}
                                                        <Button variant="outline" size="sm" onClick={() => setInfoModal({ open: true, panti: item })}>
                                                            <Info className="h-4 w-4" />
                                                        </Button>
                                                        <Button variant="outline" size="sm" onClick={() => handleAnakModalOpen(item)}>
                                                            <Users className="h-4 w-4" />
                                                            <span className="ml-1 hidden md:inline">Lihat Anak Asuh</span>
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={8} className="py-8 text-center text-gray-500">
                                                Belum ada data panti terdaftar
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Modal Info Status */}
                        {infoModal.open && infoModal.panti && (
                            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                                <div className="relative max-h-[90vh] w-full max-w-3xl overflow-y-auto rounded-lg bg-white p-6 shadow-lg">
                                    <button
                                        className="sticky top-2 right-2 float-right text-gray-400 hover:text-gray-600"
                                        onClick={() => setInfoModal({ open: false, panti: null })}
                                    >
                                        <X className="h-5 w-5" />
                                    </button>
                                    <h2 className="mb-2 flex items-center gap-2 text-lg font-bold">
                                        <Info className="h-5 w-5 text-blue-600" />
                                        Info Panti & Dokumen Resmi
                                    </h2>
                                    {loadingPantiInfo ? (
                                        <div className="py-8 text-center text-gray-600">Memuat data...</div>
                                    ) : pantiInfo ? (
                                        <div className="mb-4 space-y-3 pb-8">
                                            {/* Identitas Panti */}
                                            <div>
                                                <span className="text-lg font-semibold">{pantiInfo.panti.nama}</span>
                                            </div>
                                            <div className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
                                                <div>
                                                    <strong>Pimpinan:</strong> {pantiInfo.panti.pimpinan}
                                                </div>
                                                <div>
                                                    <strong>Alamat:</strong> {pantiInfo.panti.alamat}
                                                </div>
                                                <div>
                                                    <strong>Kabupaten/Kota:</strong> {pantiInfo.panti.kabupaten || '-'}
                                                </div>
                                                <div>
                                                    <strong>Telepon:</strong> {pantiInfo.panti.telepon}
                                                </div>
                                                <div>
                                                    <strong>Email:</strong> {pantiInfo.panti.email || '-'}
                                                </div>
                                                <div>
                                                    <strong>Tanggal Pendirian:</strong> {pantiInfo.panti.tanggal_pendirian}
                                                </div>
                                                <div>
                                                    <strong>Kapasitas:</strong> {pantiInfo.panti.kapasitas}
                                                </div>
                                                <div>
                                                    <strong>Jumlah Anak:</strong> {pantiInfo.panti.jumlah_anak}
                                                </div>
                                            </div>
                                            <div>
                                                <span
                                                    className={`inline-block rounded px-2 py-1 text-xs font-medium ${getStatusColor(pantiInfo.panti.status)}`}
                                                >
                                                    {getStatusDisplay(pantiInfo.panti.status)}
                                                </span>
                                            </div>
                                            {pantiInfo.panti.user && (
                                                <div className="text-xs text-gray-600">
                                                    <div>
                                                        <strong>Didaftarkan oleh:</strong> {pantiInfo.panti.user.name}
                                                    </div>
                                                    <div>
                                                        <strong>Email:</strong> {pantiInfo.panti.user.email}
                                                    </div>
                                                    <div>
                                                        <strong>Tanggal daftar:</strong>{' '}
                                                        {new Date(pantiInfo.panti.user.created_at).toLocaleDateString('id-ID', {
                                                            day: 'numeric',
                                                            month: 'long',
                                                            year: 'numeric',
                                                        })}
                                                    </div>
                                                </div>
                                            )}
                                            <div className="text-gray-700">{getStatusExplanation(pantiInfo.panti)}</div>
                                            {/* Dokumen Resmi */}
                                            <div className="mt-4">
                                                <h3 className="mb-2 font-semibold">Daftar Dokumen Resmi</h3>
                                                {Array.isArray(pantiInfo.documents) && pantiInfo.documents.length > 0 ? (
                                                    <ul className="space-y-2">
                                                        {pantiInfo.documents.map((doc: any, idx: number) => {
                                                            const namaDokumen = doc.jenis_dokumen || doc.nama_dokumen || 'Dokumen';
                                                            const penjelasan = getDokumenExplanation(namaDokumen);
                                                            return (
                                                                <li key={doc.id || idx} className="flex items-center gap-2 border-b pb-1">
                                                                    <span className="font-medium">{namaDokumen}</span>
                                                                    {penjelasan && <span className="ml-2 text-xs text-gray-500">({penjelasan})</span>}
                                                                    {doc.file_path && (
                                                                        <a
                                                                            href={
                                                                                doc.file_path.startsWith('http')
                                                                                    ? doc.file_path
                                                                                    : `/storage/${doc.file_path}`
                                                                            }
                                                                            target="_blank"
                                                                            rel="noopener noreferrer"
                                                                            className="text-xs text-blue-600 underline"
                                                                        >
                                                                            Lihat
                                                                        </a>
                                                                    )}
                                                                    <span className="text-xs text-gray-500">{doc.keterangan || ''}</span>
                                                                </li>
                                                            );
                                                        })}
                                                    </ul>
                                                ) : (
                                                    <div className="text-xs text-gray-500">Belum ada dokumen resmi diunggah.</div>
                                                )}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="py-8 text-center text-gray-500">Gagal memuat data panti.</div>
                                    )}
                                    <div className="mt-4 flex justify-end">
                                        <Button variant="outline" size="sm" onClick={() => setInfoModal({ open: false, panti: null })}>
                                            Tutup
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Modal Daftar Anak Asuh */}
                        {anakModal.open && anakModal.panti && (
                            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                                <div className="relative w-full max-w-lg rounded-lg bg-white p-6 shadow-lg">
                                    <button
                                        className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                                        onClick={() => setAnakModal({ open: false, panti: null })}
                                    >
                                        <X className="h-5 w-5" />
                                    </button>
                                    <h2 className="mb-2 flex items-center gap-2 text-lg font-bold">
                                        <Users className="h-5 w-5 text-blue-600" />
                                        Daftar Anak Asuh
                                    </h2>
                                    <div className="mb-2 font-semibold">{anakModal.panti.nama}</div>

                                    {loadingAnakAsuh ? (
                                        <div className="py-8 text-center">
                                            <div className="inline-flex items-center gap-2">
                                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                                                <span className="text-sm text-gray-600">Memuat data anak asuh...</span>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full border text-sm">
                                                <thead>
                                                    <tr className="bg-gray-100">
                                                        <th className="border px-2 py-1">No</th>
                                                        <th className="border px-2 py-1">Nama</th>
                                                        <th className="border px-2 py-1">Usia</th>
                                                        <th className="border px-2 py-1">Jenis Kelamin</th>
                                                        <th className="border px-2 py-1">Status</th>
                                                        <th className="border px-2 py-1">Asal Daerah</th>
                                                        <th className="border px-2 py-1">Tanggal Masuk</th>
                                                        <th className="border px-2 py-1">Detail</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {(anakAsuhData[anakModal.panti.id] || []).length > 0 ? (
                                                        anakAsuhData[anakModal.panti.id].map((anak, idx) => (
                                                            <tr key={idx}>
                                                                <td className="border px-2 py-1">{idx + 1}</td>
                                                                <td className="border px-2 py-1">{anak.nama}</td>
                                                                <td className="border px-2 py-1">{Math.ceil(anak.usia)} tahun</td>
                                                                <td className="border px-2 py-1">{anak.jenis_kelamin}</td>
                                                                <td className="border px-2 py-1">
                                                                    <span
                                                                        className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                                                                            anak.status === 'aktif'
                                                                                ? 'bg-green-100 text-green-800'
                                                                                : 'bg-red-100 text-red-800'
                                                                        }`}
                                                                    >
                                                                        {anak.status}
                                                                    </span>
                                                                    {anak.status === 'tidak aktif' && anak.alasan_tidak_aktif && (
                                                                        <div className="mt-1 text-xs text-gray-600">{anak.alasan_tidak_aktif}</div>
                                                                    )}
                                                                </td>
                                                                <td className="border px-2 py-1">{anak.asal_daerah || '-'}</td>
                                                                <td className="border px-2 py-1">{anak.tanggal_masuk || '-'}</td>
                                                                <td className="border px-2 py-1">
                                                                    <Button variant="outline" size="sm" onClick={() => setViewingAnak(anak)}>
                                                                        <Eye className="h-4 w-4" /> View
                                                                    </Button>
                                                                </td>
                                                            </tr>
                                                        ))
                                                    ) : (
                                                        <tr>
                                                            <td colSpan={8} className="border px-2 py-4 text-center text-gray-500">
                                                                Belum ada data anak asuh
                                                            </td>
                                                        </tr>
                                                    )}
                                                </tbody>
                                            </table>

                                            {/* Modal Detail Anak Asuh */}
                                            {typeof viewingAnak === 'object' && viewingAnak !== null && (
                                                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                                                    <div className="relative w-full max-w-lg rounded-lg bg-white p-6 shadow-lg">
                                                        <button
                                                            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                                                            onClick={() => setViewingAnak(null)}
                                                        >
                                                            <X className="h-5 w-5" />
                                                        </button>
                                                        <h2 className="mb-2 flex items-center gap-2 text-lg font-bold">
                                                            <Eye className="h-5 w-5 text-blue-600" />
                                                            Detail Data Anak Asuh
                                                        </h2>
                                                        <div className="mb-2 font-semibold">{viewingAnak.nama_lengkap || viewingAnak.nama}</div>
                                                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                            <div>
                                                                <div className="mb-2">
                                                                    <strong>Nama Lengkap:</strong> {viewingAnak.nama_lengkap || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>NIK:</strong> {viewingAnak.nik || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Tempat Lahir:</strong> {viewingAnak.tempat_lahir || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Tanggal Lahir:</strong> {viewingAnak.tanggal_lahir || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Usia:</strong> {Math.ceil(viewingAnak.usia)} tahun
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Jenis Kelamin:</strong> {viewingAnak.jenis_kelamin || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Pendidikan:</strong> {viewingAnak.pendidikan || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Status Anak:</strong>{' '}
                                                                    {viewingAnak.status || viewingAnak.status_anak || '-'}
                                                                </div>
                                                                {viewingAnak.foto_anak && (
                                                                    <div className="mb-2">
                                                                        <strong>Foto Anak:</strong>
                                                                        <br />
                                                                        <img
                                                                            src={`/storage/${viewingAnak.foto_anak}`}
                                                                            alt="Foto anak"
                                                                            className="mt-2 h-24 w-24 rounded border object-cover"
                                                                        />
                                                                    </div>
                                                                )}
                                                            </div>
                                                            <div>
                                                                <div className="mb-2">
                                                                    <strong>Nama Ayah:</strong> {viewingAnak.nama_ayah || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Usia Ayah:</strong>{' '}
                                                                    {viewingAnak.usia_ayah ? `${viewingAnak.usia_ayah} tahun` : '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Pekerjaan Ayah:</strong> {viewingAnak.pekerjaan_ayah || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Alamat Ayah:</strong> {viewingAnak.alamat_ayah || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Nama Ibu:</strong> {viewingAnak.nama_ibu || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Usia Ibu:</strong>{' '}
                                                                    {viewingAnak.usia_ibu ? `${viewingAnak.usia_ibu} tahun` : '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Pekerjaan Ibu:</strong> {viewingAnak.pekerjaan_ibu || '-'}
                                                                </div>
                                                                <div className="mb-2">
                                                                    <strong>Alamat Ibu:</strong> {viewingAnak.alamat_ibu || '-'}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="mt-4 flex justify-end">
                                                            <Button variant="outline" size="sm" onClick={() => setViewingAnak(null)}>
                                                                Tutup
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                    <div className="mt-4 flex justify-end">
                                        <Button variant="outline" size="sm" onClick={() => setAnakModal({ open: false, panti: null })}>
                                            Tutup
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Modal Edit Status */}
                        {editModal.open && editModal.panti && (
                            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                                <div className="relative w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
                                    <button
                                        className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                                        onClick={() => setEditModal({ open: false, panti: null })}
                                    >
                                        <X className="h-5 w-5" />
                                    </button>
                                    <h2 className="mb-4 flex items-center gap-2 text-lg font-bold">
                                        <Edit className="h-5 w-5 text-blue-600" />
                                        Edit Status Panti
                                    </h2>
                                    <div className="mb-4">
                                        <span className="font-semibold">{editModal.panti.nama}</span>
                                    </div>

                                    <div className="mb-4">
                                        <Label htmlFor="status" className="text-sm font-medium">
                                            Status
                                        </Label>
                                        <Select value={editForm.status} onValueChange={(value) => setEditForm({ ...editForm, status: value })}>
                                            <SelectTrigger className="mt-1">
                                                <SelectValue placeholder="Pilih status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="pending">Menunggu</SelectItem>
                                                <SelectItem value="diproses">Diproses</SelectItem>
                                                <SelectItem value="diverifikasi">Diverifikasi</SelectItem>
                                                <SelectItem value="ditolak">Ditolak</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="mb-4">
                                        <Label htmlFor="admin_notes" className="text-sm font-medium">
                                            Catatan Admin (Opsional)
                                        </Label>
                                        <Textarea
                                            id="admin_notes"
                                            placeholder="Tambahkan catatan jika diperlukan..."
                                            value={editForm.admin_notes}
                                            onChange={(e) => setEditForm({ ...editForm, admin_notes: e.target.value })}
                                            className="mt-1"
                                            rows={3}
                                        />
                                    </div>

                                    <div className="flex justify-end space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setEditModal({ open: false, panti: null })}
                                            disabled={loading}
                                        >
                                            Batal
                                        </Button>
                                        <Button size="sm" onClick={handleStatusUpdate} disabled={loading || !editForm.status}>
                                            {loading ? 'Menyimpan...' : 'Simpan'}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Pagination */}
                        {pagination && pagination.last_page > 1 && (
                            <div className="flex items-center justify-between pt-4">
                                <p className="text-sm text-gray-700">
                                    Menampilkan {pagination.from} - {pagination.to} dari {pagination.total} data
                                </p>
                                <div className="flex space-x-2">
                                    {pagination.current_page > 1 && (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page - 1}`}>
                                                <ChevronLeft className="h-4 w-4" />
                                                Previous
                                            </a>
                                        </Button>
                                    )}
                                    {pagination.current_page < pagination.last_page && (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page + 1}`}>
                                                Next
                                                <ChevronRight className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </DinsosRiauLayout>
    );
}
