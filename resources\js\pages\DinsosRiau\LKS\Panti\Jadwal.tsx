// Untuk men<PERSON> error TypeScript pada window.Laravel
declare global {
    interface Window {
        Laravel?: { csrfToken?: string };
    }
}
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { ArrowLeft, Calendar, Eye, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Panti {
    id: number;
    user_id: number; // tambahkan user_id agar sesuai dengan filter
    nama: string;
    alamat: string;
    telepon: string;
    email?: string;
    pimpinan: string;
    tanggal_pendirian: string;
    kapasitas: number;
    jumlah_anak: number;
    status: 'pending' | 'approved' | 'rejected';
    admin_notes?: string;
    created_at: string;
    updated_at: string;
}

interface JadwalKunjungan {
    id: number;
    panti_id: number;
    panti: Panti;
    tanggal_kunjungan: string;
    waktu_kunjungan: string;
    tim_anggota_1: string;
    tim_anggota_2: string;
    tim_anggota_3: string;
    surat_perjalanan_dinas: string;
    deskripsi_kegiatan: string;
    catatan_hasil_kunjungan?: string;
    upload_dokumentasi?: string;
    status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
    created_at: string;
    updated_at: string;
}

interface Pagination {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    panti: Panti[];
    jadwalKunjungan?: JadwalKunjungan[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Panti Asuhan',
        href: '/dinsosriau/lks/panti',
    },
    {
        title: 'Atur Jadwal Kunjungan',
        href: '/dinsosriau/lks/panti/jadwal',
    },
];

export default function JadwalKunjungan({ user, panti, jadwalKunjungan = [], pagination }: Props) {
    // State untuk tambah jadwal
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [formData, setFormData] = useState({
        panti_id: '',
        tanggal_kunjungan: '',
        waktu_kunjungan: '',
        tim1: '',
        tim2: '',
        tim3: '',
        surat: null as File | null,
        deskripsi: '',
    });
    const [errors, setErrors] = useState<any>({});
    // State untuk edit jadwal
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [editData, setEditData] = useState<any>(null);
    const [editErrors, setEditErrors] = useState<any>({});
    // State jadwal kunjungan, default dari props
    const [jadwalList, setJadwalList] = useState<any[]>(jadwalKunjungan);

    // Untuk View dialog
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [selectedJadwal, setSelectedJadwal] = useState<any | null>(null);

    // Helper alasan status
    const getStatusReason = (jadwal: any) => {
        switch (jadwal.status) {
            case 'confirmed':
                return 'Jadwal ini sudah dikonfirmasi oleh pihak panti.';
            case 'cancelled':
                return 'Jadwal ini ditolak/dibatalkan oleh pihak panti.';
            case 'scheduled':
                return 'Jadwal ini masih menunggu konfirmasi dari pihak panti.';
            case 'completed':
                return 'Kunjungan sudah selesai dilaksanakan.';
            default:
                return '-';
        }
    };

    // Fetch data dari backend setelah submit
    const fetchJadwalList = () => {
        // Untuk Dinsos Provinsi, ambil semua jadwal kunjungan tanpa filter panti_id
        // Karena mereka perlu melihat jadwal kunjungan ke semua panti
        fetch('/dinsosriau/lks/panti/jadwal-kunjungan')
            .then((res) => res.json())
            .then((res) => {
                if (res.success && Array.isArray(res.data)) {
                    // Mapping field agar cocok dengan frontend
                    const mapped = res.data.map((item: any) => ({
                        id: item.id,
                        panti_id: item.panti_id,
                        panti: panti.find((p) => p.id === Number(item.panti_id)),
                        tanggal_kunjungan: item.tanggal_kunjungan,
                        waktu_kunjungan: item.waktu_kunjungan || '',
                        tim_anggota_1: item.tim_anggota_1 || '',
                        tim_anggota_2: item.tim_anggota_2 || '',
                        tim_anggota_3: item.tim_anggota_3 || '',
                        surat_perjalanan_dinas: item.surat_perjalanan_dinas || '',
                        deskripsi_kegiatan: item.keterangan || '',
                        status: item.status || 'scheduled',
                        created_at: item.created_at,
                        updated_at: item.updated_at,
                    }));
                    setJadwalList(mapped);
                }
            })
            .catch((error) => {
                console.error('Error fetching jadwal list:', error);
            });
    };

    // Tambah jadwal
    const handleOpenForm = () => {
        setFormData({
            panti_id: '',
            tanggal_kunjungan: '',
            waktu_kunjungan: '',
            tim1: '',
            tim2: '',
            tim3: '',
            surat: null,
            deskripsi: '',
        });
        setErrors({});
        setIsFormOpen(true);
    };
    const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value, files } = e.target as any;
        setFormData((prev) => ({
            ...prev,
            [name]: files ? files[0] : value,
        }));
    };
    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const newErrors: any = {};
        if (!formData.panti_id) newErrors.panti_id = 'Pilih panti tujuan';
        if (!formData.tanggal_kunjungan) newErrors.tanggal_kunjungan = 'Tanggal wajib diisi';
        if (!formData.waktu_kunjungan) newErrors.waktu_kunjungan = 'Waktu wajib diisi';
        if (!formData.tim1) newErrors.tim1 = 'Nama anggota 1 wajib diisi';
        if (!formData.tim2) newErrors.tim2 = 'Nama anggota 2 wajib diisi';
        if (!formData.tim3) newErrors.tim3 = 'Nama anggota 3 wajib diisi';
        if (!formData.surat) newErrors.surat = 'Surat perjalanan dinas wajib diupload';
        if (!formData.deskripsi) newErrors.deskripsi = 'Deskripsi wajib diisi';
        setErrors(newErrors);
        if (Object.keys(newErrors).length > 0) return;

        // Kirim ke backend dengan semua field yang diperlukan
        const payload = {
            panti_id: formData.panti_id,
            tanggal_kunjungan: formData.tanggal_kunjungan,
            waktu_kunjungan: formData.waktu_kunjungan,
            tim_anggota_1: formData.tim1,
            tim_anggota_2: formData.tim2,
            tim_anggota_3: formData.tim3,
            surat_perjalanan_dinas: formData.surat?.name || '',
            keterangan: formData.deskripsi,
        };

        // Ambil CSRF token dari window.Laravel atau meta tag
        let csrfToken = '';
        if (window.Laravel && window.Laravel.csrfToken) {
            csrfToken = window.Laravel.csrfToken;
        } else {
            const meta = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
            csrfToken = meta?.content || '';
        }
        if (!csrfToken) {
            alert('CSRF token tidak ditemukan di halaman. Silakan refresh atau login ulang.');
            console.error('CSRF token missing!');
            return;
        }

        fetch('/dinsosriau/lks/panti/jadwal-kunjungan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken,
            },
            body: JSON.stringify(payload),
        })
            .then(async (res) => {
                if (!res.ok) {
                    const data = await res.json();
                    setErrors(data.errors || { umum: 'Gagal simpan jadwal' });
                    return;
                }
                // Setelah submit sukses, fetch ulang data dari backend dan update tabel langsung
                fetchJadwalList();
                setIsFormOpen(false);
            })
            .catch((error) => {
                console.error('Submit error:', error);
                setErrors({ umum: 'Gagal simpan jadwal' });
            });
    };

    // Fetch data awal dari backend saat komponen mount
    useEffect(() => {
        fetchJadwalList();
        // eslint-disable-next-line
    }, []);

    // Edit jadwal
    const handleOpenEdit = (jadwal: any) => {
        setEditData({
            ...jadwal,
            tim1: jadwal.tim_anggota_1,
            tim2: jadwal.tim_anggota_2,
            tim3: jadwal.tim_anggota_3,
            surat: null,
            deskripsi: jadwal.deskripsi_kegiatan,
            catatanHasil: jadwal.catatan_hasil_kunjungan || '',
            dokumentasi: null,
        });
        setEditErrors({});
        setIsEditOpen(true);
    };
    const handleEditChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value, files } = e.target as any;
        setEditData((prev: any) => ({
            ...prev,
            [name]: files ? files[0] : value,
        }));
    };
    const handleEditSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const newErrors: any = {};
        if (!editData.panti_id) newErrors.panti_id = 'Pilih panti tujuan';
        if (!editData.tanggal_kunjungan) newErrors.tanggal_kunjungan = 'Tanggal wajib diisi';
        if (!editData.waktu_kunjungan) newErrors.waktu_kunjungan = 'Waktu wajib diisi';
        if (!editData.tim1) newErrors.tim1 = 'Nama anggota 1 wajib diisi';
        if (!editData.tim2) newErrors.tim2 = 'Nama anggota 2 wajib diisi';
        if (!editData.tim3) newErrors.tim3 = 'Nama anggota 3 wajib diisi';
        if (!editData.deskripsi) newErrors.deskripsi = 'Deskripsi wajib diisi';
        setEditErrors(newErrors);
        if (Object.keys(newErrors).length > 0) return;
        // Update jadwal
        const updatedJadwal = {
            ...editData,
            panti: panti.find((p) => p.id === Number(editData.panti_id)),
            tim_anggota_1: editData.tim1,
            tim_anggota_2: editData.tim2,
            tim_anggota_3: editData.tim3,
            surat_perjalanan_dinas: editData.surat?.name || editData.surat_perjalanan_dinas,
            deskripsi_kegiatan: editData.deskripsi,
            catatan_hasil_kunjungan: editData.catatanHasil,
            upload_dokumentasi: editData.dokumentasi?.name || editData.upload_dokumentasi,
            updated_at: new Date().toISOString(),
        };
        const updatedList = jadwalList.map((j) => (j.id === updatedJadwal.id ? updatedJadwal : j));
        setJadwalList(updatedList);
        localStorage.setItem('jadwalKunjungan', JSON.stringify(updatedList));
        window.dispatchEvent(new Event('jadwalUpdated'));
        setIsEditOpen(false);
    };

    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Jadwal Kunjungan Panti Asuhan" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <a href="/dinsosriau/lks/panti">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali
                            </a>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold text-blue-900">Jadwal Kunjungan Panti Asuhan</h1>
                        </div>
                    </div>
                    <Button onClick={handleOpenForm} className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Tambah Jadwal Kunjungan
                    </Button>
                </div>

                {/* Modal Form */}
                <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
                    <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>Tambah Jadwal Kunjungan</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleFormSubmit} className="space-y-6">
                            <div>
                                <Label htmlFor="panti_id">Pilih Panti Tujuan</Label>
                                <select
                                    id="panti_id"
                                    name="panti_id"
                                    value={formData.panti_id}
                                    onChange={handleFormChange}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                >
                                    <option value="">-- Pilih Panti --</option>
                                    {panti.map((item) => (
                                        <option key={item.id} value={item.id}>
                                            {item.nama}
                                        </option>
                                    ))}
                                </select>
                                {errors.panti_id && <p className="mt-1 text-sm text-red-600">{errors.panti_id}</p>}
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="tanggal_kunjungan">Tanggal Kunjungan</Label>
                                    <Input
                                        id="tanggal_kunjungan"
                                        name="tanggal_kunjungan"
                                        type="date"
                                        value={formData.tanggal_kunjungan}
                                        onChange={handleFormChange}
                                    />
                                    {errors.tanggal_kunjungan && <p className="mt-1 text-sm text-red-600">{errors.tanggal_kunjungan}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="waktu_kunjungan">Waktu Kunjungan</Label>
                                    <Input
                                        id="waktu_kunjungan"
                                        name="waktu_kunjungan"
                                        type="time"
                                        value={formData.waktu_kunjungan}
                                        onChange={handleFormChange}
                                    />
                                    {errors.waktu_kunjungan && <p className="mt-1 text-sm text-red-600">{errors.waktu_kunjungan}</p>}
                                </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="tim1">Anggota Tim 1</Label>
                                    <Input id="tim1" name="tim1" value={formData.tim1} onChange={handleFormChange} />
                                    {errors.tim1 && <p className="mt-1 text-sm text-red-600">{errors.tim1}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="tim2">Anggota Tim 2</Label>
                                    <Input id="tim2" name="tim2" value={formData.tim2} onChange={handleFormChange} />
                                    {errors.tim2 && <p className="mt-1 text-sm text-red-600">{errors.tim2}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="tim3">Anggota Tim 3</Label>
                                    <Input id="tim3" name="tim3" value={formData.tim3} onChange={handleFormChange} />
                                    {errors.tim3 && <p className="mt-1 text-sm text-red-600">{errors.tim3}</p>}
                                </div>
                            </div>
                            <div>
                                <Label htmlFor="surat">Surat Perjalanan Dinas</Label>
                                <Input id="surat" name="surat" type="file" accept=".pdf,.doc,.docx" onChange={handleFormChange} />
                                {errors.surat && <p className="mt-1 text-sm text-red-600">{errors.surat}</p>}
                            </div>
                            <div>
                                <Label htmlFor="deskripsi">Deskripsi Kegiatan</Label>
                                <Textarea id="deskripsi" name="deskripsi" value={formData.deskripsi} onChange={handleFormChange} rows={3} />
                                {errors.deskripsi && <p className="mt-1 text-sm text-red-600">{errors.deskripsi}</p>}
                            </div>
                            <div className="flex justify-end space-x-2 pt-2">
                                <Button variant="outline" type="button" onClick={() => setIsFormOpen(false)}>
                                    Batal
                                </Button>
                                <Button type="submit">Simpan Jadwal</Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>

                {/* Tabel Jadwal Kunjungan */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Calendar className="h-5 w-5" />
                            <span>Daftar Jadwal Kunjungan</span>
                        </CardTitle>
                        <CardDescription>Daftar jadwal kunjungan yang telah dijadwalkan</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-12">No</TableHead>
                                        <TableHead>Nama Panti</TableHead>
                                        <TableHead>Kabupaten/Kota</TableHead>
                                        <TableHead>Tanggal Kunjungan</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-center">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {jadwalList.length > 0 ? (
                                        jadwalList.map((item, idx) => (
                                            <TableRow key={item.id}>
                                                <TableCell>{idx + 1}</TableCell>
                                                <TableCell>{item.panti?.nama || '-'}</TableCell>
                                                <TableCell>{item.panti?.kabupaten || item.panti?.alamat?.split(',').pop()?.trim() || '-'}</TableCell>
                                                <TableCell>
                                                    {item.tanggal_kunjungan} {item.waktu_kunjungan}
                                                </TableCell>
                                                <TableCell>
                                                    <span
                                                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                                            item.status === 'confirmed'
                                                                ? 'bg-green-100 text-green-800'
                                                                : item.status === 'scheduled'
                                                                  ? 'bg-yellow-100 text-yellow-800'
                                                                  : item.status === 'completed'
                                                                    ? 'bg-blue-100 text-blue-800'
                                                                    : 'bg-red-100 text-red-800'
                                                        }`}
                                                    >
                                                        {item.status === 'scheduled'
                                                            ? 'Menunggu Konfirmasi'
                                                            : item.status === 'confirmed'
                                                              ? 'Dikonfirmasi'
                                                              : item.status === 'completed'
                                                                ? 'Selesai'
                                                                : 'Dibatalkan'}
                                                    </span>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex justify-center space-x-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            disabled={item.status !== 'confirmed'}
                                                            onClick={() => {
                                                                if (item.status === 'confirmed') {
                                                                    handleOpenEdit(item);
                                                                }
                                                            }}
                                                        >
                                                            Edit
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => {
                                                                setSelectedJadwal(item);
                                                                setIsViewOpen(true);
                                                            }}
                                                        >
                                                            View
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={6} className="py-8 text-center text-gray-500">
                                                Belum ada jadwal kunjungan
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
                {/* Modal Edit Jadwal */}
                <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
                    <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle>Edit Jadwal Kunjungan</DialogTitle>
                        </DialogHeader>
                        {editData && (
                            <form onSubmit={handleEditSubmit} className="space-y-6">
                                <div>
                                    <Label htmlFor="edit_panti_id">Pilih Panti Tujuan</Label>
                                    <select
                                        id="edit_panti_id"
                                        name="panti_id"
                                        value={editData.panti_id}
                                        onChange={handleEditChange}
                                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                    >
                                        <option value="">-- Pilih Panti --</option>
                                        {panti.map((item) => (
                                            <option key={item.id} value={item.id}>
                                                {item.nama}
                                            </option>
                                        ))}
                                    </select>
                                    {editErrors.panti_id && <p className="mt-1 text-sm text-red-600">{editErrors.panti_id}</p>}
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="edit_tanggal_kunjungan">Tanggal Kunjungan</Label>
                                        <Input
                                            id="edit_tanggal_kunjungan"
                                            name="tanggal_kunjungan"
                                            type="date"
                                            value={editData.tanggal_kunjungan}
                                            onChange={handleEditChange}
                                        />
                                        {editErrors.tanggal_kunjungan && <p className="mt-1 text-sm text-red-600">{editErrors.tanggal_kunjungan}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_waktu_kunjungan">Waktu Kunjungan</Label>
                                        <Input
                                            id="edit_waktu_kunjungan"
                                            name="waktu_kunjungan"
                                            type="time"
                                            value={editData.waktu_kunjungan}
                                            onChange={handleEditChange}
                                        />
                                        {editErrors.waktu_kunjungan && <p className="mt-1 text-sm text-red-600">{editErrors.waktu_kunjungan}</p>}
                                    </div>
                                </div>
                                <div className="grid grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="edit_tim1">Anggota Tim 1</Label>
                                        <Input id="edit_tim1" name="tim1" value={editData.tim1} onChange={handleEditChange} />
                                        {editErrors.tim1 && <p className="mt-1 text-sm text-red-600">{editErrors.tim1}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_tim2">Anggota Tim 2</Label>
                                        <Input id="edit_tim2" name="tim2" value={editData.tim2} onChange={handleEditChange} />
                                        {editErrors.tim2 && <p className="mt-1 text-sm text-red-600">{editErrors.tim2}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_tim3">Anggota Tim 3</Label>
                                        <Input id="edit_tim3" name="tim3" value={editData.tim3} onChange={handleEditChange} />
                                        {editErrors.tim3 && <p className="mt-1 text-sm text-red-600">{editErrors.tim3}</p>}
                                    </div>
                                </div>
                                <div>
                                    <Label htmlFor="edit_surat">Surat Perjalanan Dinas (opsional)</Label>
                                    <Input id="edit_surat" name="surat" type="file" accept=".pdf,.doc,.docx" onChange={handleEditChange} />
                                </div>
                                <div>
                                    <Label htmlFor="edit_deskripsi">Deskripsi Kegiatan</Label>
                                    <Textarea id="edit_deskripsi" name="deskripsi" value={editData.deskripsi} onChange={handleEditChange} rows={3} />
                                    {editErrors.deskripsi && <p className="mt-1 text-sm text-red-600">{editErrors.deskripsi}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="edit_catatan_hasil">Catatan Hasil Kunjungan</Label>
                                    <Textarea
                                        id="edit_catatan_hasil"
                                        name="catatanHasil"
                                        value={editData.catatanHasil || ''}
                                        onChange={handleEditChange}
                                        rows={4}
                                        placeholder="Tuliskan catatan hasil kunjungan (opsional)"
                                    />
                                    {editErrors.catatanHasil && <p className="mt-1 text-sm text-red-600">{editErrors.catatanHasil}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="edit_dokumentasi">Upload Dokumentasi</Label>
                                    <Input
                                        id="edit_dokumentasi"
                                        name="dokumentasi"
                                        type="file"
                                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                        onChange={handleEditChange}
                                    />
                                    <p className="mt-1 text-sm text-gray-500">Format yang didukung: PDF, DOC, DOCX, JPG, PNG (opsional)</p>
                                    {editErrors.dokumentasi && <p className="mt-1 text-sm text-red-600">{editErrors.dokumentasi}</p>}
                                </div>
                                <div className="flex justify-end space-x-2 pt-2">
                                    <Button variant="outline" type="button" onClick={() => setIsEditOpen(false)}>
                                        Batal
                                    </Button>
                                    <Button type="submit">Simpan Perubahan</Button>
                                </div>
                            </form>
                        )}
                    </DialogContent>
                </Dialog>

                {/* View Dialog untuk detail jadwal */}
                <Dialog open={isViewOpen} onOpenChange={setIsViewOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Detail Jadwal Kunjungan</DialogTitle>
                        </DialogHeader>
                        {selectedJadwal && (
                            <div className="space-y-2">
                                <div>
                                    <b>Status:</b>{' '}
                                    {selectedJadwal.status === 'scheduled'
                                        ? 'Menunggu Konfirmasi'
                                        : selectedJadwal.status === 'confirmed'
                                          ? 'Dikonfirmasi'
                                          : selectedJadwal.status === 'completed'
                                            ? 'Selesai'
                                            : 'Dibatalkan'}
                                </div>
                                <div>
                                    <b>Alasan:</b> {getStatusReason(selectedJadwal)}
                                </div>
                                <div>
                                    <b>Panti:</b> {selectedJadwal.panti?.nama || '-'}
                                </div>
                                <div>
                                    <b>Tanggal:</b> {selectedJadwal.tanggal_kunjungan}
                                </div>
                                <div>
                                    <b>Waktu:</b> {selectedJadwal.waktu_kunjungan}
                                </div>
                                <div>
                                    <b>Tim:</b> {selectedJadwal.tim_anggota_1}, {selectedJadwal.tim_anggota_2}, {selectedJadwal.tim_anggota_3}
                                </div>
                                <div>
                                    <b>Surat:</b>{' '}
                                    {selectedJadwal.surat_perjalanan_dinas ? (
                                        <>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="ml-2"
                                                onClick={() => window.open(`/storage/${selectedJadwal.surat_perjalanan_dinas}`, '_blank')}
                                            >
                                                <Eye className="mr-1 inline h-4 w-4" /> View
                                            </Button>
                                        </>
                                    ) : selectedJadwal.surat ? (
                                        <>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="ml-2"
                                                onClick={() => window.open(`/storage/${selectedJadwal.surat}`, '_blank')}
                                            >
                                                <Eye className="mr-1 inline h-4 w-4" /> View
                                            </Button>
                                        </>
                                    ) : (
                                        '-'
                                    )}
                                </div>
                                <div>
                                    <b>Deskripsi:</b> {selectedJadwal.deskripsi_kegiatan}
                                </div>
                                <div>
                                    <b>Catatan Hasil Kunjungan:</b> {selectedJadwal.catatan_hasil_kunjungan || '-'}
                                </div>
                                <div>
                                    <b>Dokumentasi:</b>{' '}
                                    {selectedJadwal.upload_dokumentasi ? (
                                        <>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="ml-2"
                                                onClick={() => window.open(`/storage/${selectedJadwal.upload_dokumentasi}`, '_blank')}
                                            >
                                                <Eye className="mr-1 inline h-4 w-4" /> View
                                            </Button>
                                        </>
                                    ) : (
                                        '-'
                                    )}
                                </div>
                            </div>
                        )}
                        <div className="flex justify-end pt-4">
                            <Button variant="outline" onClick={() => setIsViewOpen(false)}>
                                Tutup
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </DinsosRiauLayout>
    );
}
