import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { ArrowLeft, Calendar, ChevronLeft, ChevronRight, Clock, Eye, FileText, MapPin, Users } from 'lucide-react';
import { useState } from 'react';

interface LaporanKegiatan {
    id: number;
    user_id: number;
    nama_kegiatan: string;
    jenis_kegiatan: string;
    tanggal_pelaksanaan: string;
    waktu_mulai: string;
    waktu_selesai: string;
    lokasi_pelaksanaan: string;
    jumlah_peserta: number;
    penanggung_jawab: string;
    deskripsi_kegiatan: string;
    hasil_kegiatan: string;
    kendala_tantangan: string;
    foto_kegiatan?: string;
    laporan_kegiatan?: string;
    admin_feedback?: string;
    reviewed_by?: string;
    reviewed_at?: string;
    created_at: string;
    updated_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface Pagination {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    laporan: LaporanKegiatan[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Panti Asuhan',
        href: '/dinsosriau/lks/panti',
    },
    {
        title: 'Cek Laporan Kegiatan',
        href: '/dinsosriau/lks/panti/laporan',
    },
];

export default function LaporanKegiatan({ user, laporan, pagination }: Props) {
    const [selectedLaporan, setSelectedLaporan] = useState<LaporanKegiatan | null>(null);
    const [isDetailOpen, setIsDetailOpen] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        admin_feedback: '',
    });

    const getJenisKegiatanBadge = (jenis: string) => {
        switch (jenis) {
            case 'keterampilan':
                return <Badge className="border-blue-200 bg-blue-100 text-blue-800">Keterampilan</Badge>;
            case 'kegiatan sosial':
                return <Badge className="border-green-200 bg-green-100 text-green-800">Kegiatan Sosial</Badge>;
            case 'pendidikan':
                return <Badge className="border-purple-200 bg-purple-100 text-purple-800">Pendidikan</Badge>;
            case 'kesehatan':
                return <Badge className="border-red-200 bg-red-100 text-red-800">Kesehatan</Badge>;
            default:
                return <Badge className="border-gray-200 bg-gray-100 text-gray-800">{jenis}</Badge>;
        }
    };

    const handleViewDetail = (item: LaporanKegiatan) => {
        setSelectedLaporan(item);
        setData({
            admin_feedback: item.admin_feedback || '',
        });
        setIsDetailOpen(true);
    };

    const handleSaveFeedback = () => {
        if (!selectedLaporan) return;

        put(`/dinsosriau/lks/panti/laporan/${selectedLaporan.id}/feedback`, {
            onSuccess: () => {
                setIsDetailOpen(false);
                reset();
            },
        });
    };
    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Cek Laporan Kegiatan" />

            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <a href="/dinsosriau/lks/panti">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </a>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Cek Laporan Kegiatan</h1>
                        <p className="mt-1 text-blue-600">Review dan monitoring laporan kegiatan panti</p>
                    </div>
                </div>

                {/* Content */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <FileText className="h-5 w-5" />
                            <span>Laporan Kegiatan Panti Asuhan</span>
                        </CardTitle>
                        <CardDescription>Review dan monitoring laporan kegiatan dari panti asuhan</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-12">No</TableHead>
                                        <TableHead>Tanggal</TableHead>
                                        <TableHead>Nama Kegiatan</TableHead>
                                        <TableHead>Panti</TableHead>
                                        <TableHead>Jenis</TableHead>
                                        <TableHead>Peserta</TableHead>
                                        <TableHead>Feedback</TableHead>
                                        <TableHead className="text-center">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {laporan.length > 0 ? (
                                        laporan.map((item, index) => (
                                            <TableRow key={item.id}>
                                                <TableCell className="font-medium">
                                                    {(pagination.current_page - 1) * pagination.per_page + index + 1}
                                                </TableCell>
                                                <TableCell>{new Date(item.tanggal_pelaksanaan).toLocaleDateString('id-ID')}</TableCell>
                                                <TableCell className="font-medium">{item.nama_kegiatan}</TableCell>
                                                <TableCell>{item.user.name}</TableCell>
                                                <TableCell>{getJenisKegiatanBadge(item.jenis_kegiatan)}</TableCell>
                                                <TableCell>
                                                    <div className="flex items-center space-x-1">
                                                        <Users className="h-4 w-4 text-gray-500" />
                                                        <span>{item.jumlah_peserta}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {item.admin_feedback ? (
                                                        <Badge className="border-green-200 bg-green-100 text-green-800">Sudah Review</Badge>
                                                    ) : (
                                                        <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800">Belum Review</Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex justify-center space-x-2">
                                                        <Button variant="outline" size="sm" onClick={() => handleViewDetail(item)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={8} className="py-8 text-center text-gray-500">
                                                Belum ada laporan kegiatan yang masuk
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {pagination && pagination.last_page > 1 && (
                            <div className="flex items-center justify-between pt-4">
                                <p className="text-sm text-gray-700">
                                    Menampilkan {pagination.from} - {pagination.to} dari {pagination.total} data
                                </p>
                                <div className="flex space-x-2">
                                    {pagination.current_page > 1 && (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page - 1}`}>
                                                <ChevronLeft className="h-4 w-4" />
                                                Previous
                                            </a>
                                        </Button>
                                    )}
                                    {pagination.current_page < pagination.last_page && (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page + 1}`}>
                                                Next
                                                <ChevronRight className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>

                    {/* Detail Modal */}
                    <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
                        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>Detail Laporan Kegiatan</DialogTitle>
                                <DialogDescription>Review dan berikan feedback pada laporan kegiatan panti</DialogDescription>
                            </DialogHeader>

                            {selectedLaporan && (
                                <div className="space-y-6">
                                    {/* Basic Info */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Nama Panti</Label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedLaporan.user.name}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Nama Kegiatan</Label>
                                            <p className="mt-1 text-sm font-semibold text-gray-900">{selectedLaporan.nama_kegiatan}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Jenis Kegiatan</Label>
                                            <div className="mt-1">{getJenisKegiatanBadge(selectedLaporan.jenis_kegiatan)}</div>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Tanggal Pelaksanaan</Label>
                                            <div className="mt-1 flex items-center space-x-1">
                                                <Calendar className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm text-gray-900">
                                                    {new Date(selectedLaporan.tanggal_pelaksanaan).toLocaleDateString('id-ID')}
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Waktu</Label>
                                            <div className="mt-1 flex items-center space-x-1">
                                                <Clock className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm text-gray-900">
                                                    {selectedLaporan.waktu_mulai} - {selectedLaporan.waktu_selesai}
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Lokasi</Label>
                                            <div className="mt-1 flex items-center space-x-1">
                                                <MapPin className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm text-gray-900">{selectedLaporan.lokasi_pelaksanaan}</span>
                                            </div>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Jumlah Peserta</Label>
                                            <div className="mt-1 flex items-center space-x-1">
                                                <Users className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm text-gray-900">{selectedLaporan.jumlah_peserta} orang</span>
                                            </div>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Penanggung Jawab</Label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedLaporan.penanggung_jawab}</p>
                                        </div>
                                    </div>

                                    {/* Descriptions */}
                                    <div className="space-y-4">
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Deskripsi Kegiatan</Label>
                                            <p className="mt-1 rounded-md bg-gray-50 p-3 text-sm text-gray-900">
                                                {selectedLaporan.deskripsi_kegiatan}
                                            </p>
                                        </div>

                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Hasil Kegiatan</Label>
                                            <p className="mt-1 rounded-md bg-green-50 p-3 text-sm text-gray-900">{selectedLaporan.hasil_kegiatan}</p>
                                        </div>

                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Kendala/Tantangan</Label>
                                            <p className="mt-1 rounded-md bg-yellow-50 p-3 text-sm text-gray-900">
                                                {selectedLaporan.kendala_tantangan}
                                            </p>
                                        </div>
                                    </div>

                                    {/* Files */}
                                    {(selectedLaporan.foto_kegiatan || selectedLaporan.laporan_kegiatan) && (
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Dokumen Pendukung</Label>
                                            <div className="mt-2 space-y-3">
                                                {selectedLaporan.foto_kegiatan && (
                                                    <div className="rounded border p-3">
                                                        <div className="mb-2 flex items-center justify-between">
                                                            <div className="flex items-center space-x-2">
                                                                <FileText className="h-4 w-4 text-blue-500" />
                                                                <span className="text-sm font-medium">Foto Kegiatan</span>
                                                            </div>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => window.open(`/storage/${selectedLaporan.foto_kegiatan}`, '_blank')}
                                                            >
                                                                <Eye className="mr-1 h-4 w-4" />
                                                                Lihat
                                                            </Button>
                                                        </div>
                                                        {selectedLaporan.foto_kegiatan.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                                                            <div className="mt-2">
                                                                <img
                                                                    src={`/storage/${selectedLaporan.foto_kegiatan}`}
                                                                    alt="Foto Kegiatan"
                                                                    className="max-h-48 w-full rounded border object-cover"
                                                                    onError={(e) => {
                                                                        e.currentTarget.style.display = 'none';
                                                                    }}
                                                                />
                                                            </div>
                                                        ) : selectedLaporan.foto_kegiatan.toLowerCase().endsWith('.pdf') ? (
                                                            <div className="mt-2">
                                                                <iframe
                                                                    src={`/storage/${selectedLaporan.foto_kegiatan}`}
                                                                    className="h-32 w-full rounded border"
                                                                    title="Preview Foto Kegiatan"
                                                                />
                                                            </div>
                                                        ) : (
                                                            <div className="mt-2 text-xs text-gray-500">
                                                                Preview tidak tersedia untuk tipe file ini
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                                {selectedLaporan.laporan_kegiatan && (
                                                    <div className="rounded border p-3">
                                                        <div className="mb-2 flex items-center justify-between">
                                                            <div className="flex items-center space-x-2">
                                                                <FileText className="h-4 w-4 text-blue-500" />
                                                                <span className="text-sm font-medium">Laporan Dokumen</span>
                                                            </div>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => window.open(`/storage/${selectedLaporan.laporan_kegiatan}`, '_blank')}
                                                            >
                                                                <Eye className="mr-1 h-4 w-4" />
                                                                Lihat
                                                            </Button>
                                                        </div>
                                                        {selectedLaporan.laporan_kegiatan.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                                                            <div className="mt-2">
                                                                <img
                                                                    src={`/storage/${selectedLaporan.laporan_kegiatan}`}
                                                                    alt="Laporan Dokumen"
                                                                    className="max-h-48 w-full rounded border object-cover"
                                                                    onError={(e) => {
                                                                        e.currentTarget.style.display = 'none';
                                                                    }}
                                                                />
                                                            </div>
                                                        ) : selectedLaporan.laporan_kegiatan.toLowerCase().endsWith('.pdf') ? (
                                                            <div className="mt-2">
                                                                <iframe
                                                                    src={`/storage/${selectedLaporan.laporan_kegiatan}`}
                                                                    className="h-32 w-full rounded border"
                                                                    title="Preview Laporan Dokumen"
                                                                />
                                                            </div>
                                                        ) : (
                                                            <div className="mt-2 text-xs text-gray-500">
                                                                Preview tidak tersedia untuk tipe file ini
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Admin Feedback */}
                                    <div className="border-t pt-4">
                                        <div>
                                            <Label htmlFor="admin_feedback">Feedback Admin</Label>
                                            {selectedLaporan?.admin_feedback ? (
                                                <Textarea
                                                    id="admin_feedback"
                                                    value={selectedLaporan.admin_feedback}
                                                    readOnly
                                                    className="cursor-not-allowed bg-gray-100"
                                                    rows={4}
                                                />
                                            ) : (
                                                <Textarea
                                                    id="admin_feedback"
                                                    placeholder="Berikan feedback, saran, atau evaluasi untuk kegiatan ini..."
                                                    value={data.admin_feedback}
                                                    onChange={(e) => setData('admin_feedback', e.target.value)}
                                                    rows={4}
                                                />
                                            )}
                                        </div>
                                        <div className="mt-4 flex justify-end space-x-2">
                                            <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                                                Tutup
                                            </Button>
                                            {!selectedLaporan?.admin_feedback && (
                                                <Button onClick={handleSaveFeedback} disabled={processing}>
                                                    {processing ? 'Menyimpan...' : 'Simpan Feedback'}
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </DialogContent>
                    </Dialog>
                </Card>
            </div>
        </DinsosRiauLayout>
    );
}
