import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import DinsosRiauLayout from '@/layouts/dinsosriau-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, ChevronLeft, ChevronRight, Clock, DollarSign, Eye, FileText, XCircle } from 'lucide-react';
import { useState } from 'react';

interface PengajuanDana {
    id: number;
    user_id: number;
    tanggal_pengajuan: string;
    tujuan_penggunaan: string;
    periode_mulai: string;
    periode_selesai: string;
    total_dana: string;
    deskripsi_kebutuhan: string;
    file_proposal?: string;
    file_rekening?: string;
    file_ktp?: string;
    file_foto_kegiatan?: string;
    file_rab?: string;
    status: 'pending' | 'diterima' | 'ditolak';
    catatan_admin?: string;
    reviewed_by?: string;
    reviewed_at?: string;
    file_laporan_penggunaan?: string;
    tanggal_upload_laporan?: string;
    status_laporan?: 'belum_upload' | 'menunggu_verifikasi' | 'diterima' | 'ditolak';
    catatan_laporan?: string;
    created_at: string;
    updated_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    panti?: {
        id: number;
        nama: string;
        pimpinan?: string;
        email?: string;
        telepon?: string;
    };
}

interface Pagination {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pengajuan: PengajuanDana[];
    pagination: Pagination;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'LKS Panti Asuhan',
        href: '/dinsosriau/lks/panti',
    },
    {
        title: 'Verifikasi Pengajuan Dana',
        href: '/dinsosriau/lks/panti/verif',
    },
];

export default function VerifikasiPengajuan({ user, pengajuan, pagination }: Props) {
    const [selectedPengajuan, setSelectedPengajuan] = useState<PengajuanDana | null>(null);
    const [isDetailOpen, setIsDetailOpen] = useState(false);
    const [showLaporanModal, setShowLaporanModal] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        status: '',
        catatan_admin: '',
    });

    const {
        data: laporanData,
        setData: setLaporanData,
        put: putLaporan,
        processing: processingLaporan,
        errors: laporanErrors,
        reset: resetLaporan,
    } = useForm({
        status_laporan: '',
        catatan_laporan: '',
    });

    const formatCurrency = (amount: string) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
        }).format(Number(amount));
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'diterima':
                return (
                    <Badge className="border-green-200 bg-green-100 text-green-800">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Disetujui
                    </Badge>
                );
            case 'ditolak':
                return (
                    <Badge className="border-red-200 bg-red-100 text-red-800">
                        <XCircle className="mr-1 h-3 w-3" />
                        Ditolak
                    </Badge>
                );
            default:
                return (
                    <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800">
                        <Clock className="mr-1 h-3 w-3" />
                        Menunggu
                    </Badge>
                );
        }
    };

    const handleViewDetail = (item: PengajuanDana) => {
        setSelectedPengajuan(item);
        setData({
            status: item.status,
            catatan_admin: item.catatan_admin || '',
        });
        setIsDetailOpen(true);
    };

    const handleUpdateStatus = () => {
        if (!selectedPengajuan) return;

        put(`/dinsosriau/lks/panti/verif/${selectedPengajuan.id}/status`, {
            onSuccess: () => {
                setIsDetailOpen(false);
                reset();
                // Refresh the page to show updated data
                window.location.reload();
            },
            onError: (errors) => {
                console.error('Update failed:', errors);
            },
        });
    };

    const handleLaporanInfo = (pengajuan: PengajuanDana) => {
        setSelectedPengajuan(pengajuan);
        setLaporanData({
            status_laporan: pengajuan.status_laporan || '',
            catatan_laporan: pengajuan.catatan_laporan || '',
        });
        setShowLaporanModal(true);
    };

    const handleUpdateLaporan = () => {
        if (!selectedPengajuan) return;

        putLaporan(`/dinsosriau/lks/panti/verif/${selectedPengajuan.id}/laporan`, {
            onSuccess: () => {
                setShowLaporanModal(false);
                resetLaporan();
                // Refresh the page to show updated data
                window.location.reload();
            },
            onError: (errors) => {
                console.error('Error updating laporan status:', errors);
            },
        });
    };
    return (
        <DinsosRiauLayout breadcrumbs={breadcrumbs}>
            <Head title="Verifikasi Pengajuan Dana" />

            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <a href="/dinsosriau/lks/panti">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </a>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Verifikasi Pengajuan Dana</h1>
                        <p className="mt-1 text-blue-600">Verifikasi dan proses pengajuan dana bantuan</p>
                    </div>
                </div>

                {/* Content */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <DollarSign className="h-5 w-5" />
                            <span>Pengajuan Dana Bantuan</span>
                        </CardTitle>
                        <CardDescription>Verifikasi dan review pengajuan dana bantuan dari panti asuhan</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-12">No</TableHead>
                                        <TableHead>Tanggal</TableHead>
                                        <TableHead>Panti</TableHead>
                                        {/* <TableHead>Nama Panti</TableHead> */}
                                        <TableHead>Tujuan</TableHead>
                                        <TableHead>Total Dana</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-center">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {pengajuan.length > 0 ? (
                                        pengajuan.map((item, index) => (
                                            <TableRow key={item.id}>
                                                <TableCell className="font-medium">
                                                    {(pagination.current_page - 1) * pagination.per_page + index + 1}
                                                </TableCell>
                                                <TableCell>{new Date(item.tanggal_pengajuan).toLocaleDateString('id-ID')}</TableCell>
                                                <TableCell className="font-medium">{item.panti?.nama || '-'}</TableCell>
                                                {/* <TableCell className="font-medium">{item.panti?.nama || '-'}</TableCell> */}
                                                <TableCell className="capitalize">{item.tujuan_penggunaan}</TableCell>
                                                <TableCell className="font-medium">{formatCurrency(item.total_dana)}</TableCell>
                                                <TableCell>{getStatusBadge(item.status)}</TableCell>
                                                <TableCell>
                                                    <div className="flex justify-center space-x-2">
                                                        <Button variant="outline" size="sm" onClick={() => handleViewDetail(item)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                        {item.status === 'diterima' && (
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleLaporanInfo(item)}
                                                                className="border-blue-500 text-blue-600 hover:bg-blue-50"
                                                                title="Info Laporan"
                                                            >
                                                                <FileText className="h-4 w-4" />
                                                            </Button>
                                                        )}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={7} className="py-8 text-center text-gray-500">
                                                Belum ada pengajuan dana yang masuk
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination - Tetap tampil walau data belum mencukupi */}
                        {pagination && (
                            <div className="flex items-center justify-between pt-4">
                                <div className="text-sm text-gray-700">
                                    <p>
                                        Menampilkan {pagination.from || 0} - {pagination.to || 0} dari {pagination.total} data
                                    </p>
                                    <p>
                                        Halaman {pagination.current_page} dari {pagination.last_page}
                                    </p>
                                </div>
                                <div className="flex space-x-2">
                                    {pagination.current_page > 1 ? (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page - 1}`}>
                                                <ChevronLeft className="h-4 w-4" />
                                                Previous
                                            </a>
                                        </Button>
                                    ) : (
                                        <Button variant="outline" size="sm" disabled>
                                            <ChevronLeft className="h-4 w-4" />
                                            Previous
                                        </Button>
                                    )}
                                    {pagination.current_page < pagination.last_page ? (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page + 1}`}>
                                                Next
                                                <ChevronRight className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    ) : (
                                        <Button variant="outline" size="sm" disabled>
                                            Next
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>

                    {/* Detail Modal */}
                    <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
                        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>Detail Pengajuan Dana</DialogTitle>
                                <DialogDescription>Review dan verifikasi pengajuan dana bantuan</DialogDescription>
                            </DialogHeader>

                            {selectedPengajuan && (
                                <div className="space-y-6">
                                    {/* Basic Info */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Nama Panti</Label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedPengajuan.panti?.nama || '-'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Tanggal Pengajuan</Label>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {new Date(selectedPengajuan.tanggal_pengajuan).toLocaleDateString('id-ID')}
                                            </p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Tujuan Penggunaan</Label>
                                            <p className="mt-1 text-sm text-gray-900 capitalize">{selectedPengajuan.tujuan_penggunaan}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Total Dana</Label>
                                            <p className="mt-1 text-sm font-semibold text-gray-900">{formatCurrency(selectedPengajuan.total_dana)}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Periode</Label>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {selectedPengajuan.periode_mulai} - {selectedPengajuan.periode_selesai}
                                            </p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Status Saat Ini</Label>
                                            <div className="mt-1">{getStatusBadge(selectedPengajuan.status)}</div>
                                        </div>
                                    </div>

                                    {/* Description */}
                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Deskripsi Kebutuhan</Label>
                                        <p className="mt-1 rounded-md bg-gray-50 p-3 text-sm text-gray-900">
                                            {selectedPengajuan.deskripsi_kebutuhan}
                                        </p>
                                    </div>

                                    {/* Files */}
                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Dokumen Pendukung</Label>
                                        <div className="mt-2 grid grid-cols-2 gap-3">
                                            {selectedPengajuan.file_proposal && (
                                                <div className="flex items-center space-x-2 rounded border p-2">
                                                    <FileText className="h-4 w-4 text-blue-500" />
                                                    <span className="text-sm">Proposal</span>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        className="ml-2 px-2 py-1 text-xs"
                                                        onClick={() => window.open(`/storage/${selectedPengajuan.file_proposal}`, '_blank')}
                                                    >
                                                        View
                                                    </Button>
                                                </div>
                                            )}
                                            {selectedPengajuan.file_rekening && (
                                                <div className="flex items-center space-x-2 rounded border p-2">
                                                    <FileText className="h-4 w-4 text-blue-500" />
                                                    <span className="text-sm">Rekening</span>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        className="ml-2 px-2 py-1 text-xs"
                                                        onClick={() => window.open(`/storage/${selectedPengajuan.file_rekening}`, '_blank')}
                                                    >
                                                        View
                                                    </Button>
                                                </div>
                                            )}
                                            {selectedPengajuan.file_ktp && (
                                                <div className="flex items-center space-x-2 rounded border p-2">
                                                    <FileText className="h-4 w-4 text-blue-500" />
                                                    <span className="text-sm">KTP</span>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        className="ml-2 px-2 py-1 text-xs"
                                                        onClick={() => window.open(`/storage/${selectedPengajuan.file_ktp}`, '_blank')}
                                                    >
                                                        View
                                                    </Button>
                                                </div>
                                            )}
                                            {selectedPengajuan.file_rab && (
                                                <div className="flex items-center space-x-2 rounded border p-2">
                                                    <FileText className="h-4 w-4 text-blue-500" />
                                                    <span className="text-sm">RAB</span>
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        className="ml-2 px-2 py-1 text-xs"
                                                        onClick={() => window.open(`/storage/${selectedPengajuan.file_rab}`, '_blank')}
                                                    >
                                                        View
                                                    </Button>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Admin Review */}
                                    <div className="border-t pt-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="status">Status Verifikasi</Label>
                                                <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Pilih status" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="pending">Menunggu</SelectItem>
                                                        <SelectItem value="diterima">Disetujui</SelectItem>
                                                        <SelectItem value="ditolak">Ditolak</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>

                                        <div className="mt-4">
                                            <Label htmlFor="catatan_admin">Catatan Admin</Label>
                                            <Textarea
                                                id="catatan_admin"
                                                placeholder="Berikan catatan atau alasan keputusan..."
                                                value={data.catatan_admin}
                                                onChange={(e) => setData('catatan_admin', e.target.value)}
                                                rows={3}
                                            />
                                        </div>

                                        <div className="mt-4 flex justify-end space-x-2">
                                            <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                                                Batal
                                            </Button>
                                            <Button onClick={handleUpdateStatus} disabled={processing}>
                                                {processing ? 'Menyimpan...' : 'Simpan Verifikasi'}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </DialogContent>
                    </Dialog>

                    {/* Modal Info Laporan */}
                    <Dialog open={showLaporanModal} onOpenChange={setShowLaporanModal}>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Info Laporan Penggunaan Dana</DialogTitle>
                                <DialogDescription>Verifikasi laporan penggunaan dana yang telah diupload</DialogDescription>
                            </DialogHeader>

                            {selectedPengajuan && (
                                <div className="space-y-6">
                                    {/* Basic Info */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Nama Panti</Label>
                                            <p className="mt-1 text-sm text-gray-900">{selectedPengajuan.panti?.nama || '-'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Total Dana</Label>
                                            <p className="mt-1 text-sm font-semibold text-gray-900">{formatCurrency(selectedPengajuan.total_dana)}</p>
                                        </div>
                                    </div>

                                    {/* Status Laporan */}
                                    <div>
                                        <Label className="text-sm font-medium text-gray-700">Status Laporan Saat Ini</Label>
                                        <div className="mt-2">
                                            {selectedPengajuan.status_laporan === 'belum_upload' && (
                                                <Badge className="border-orange-200 bg-orange-100 text-orange-800">
                                                    <Clock className="mr-1 h-3 w-3" />
                                                    Belum Upload
                                                </Badge>
                                            )}
                                            {selectedPengajuan.status_laporan === 'menunggu_verifikasi' && (
                                                <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800">
                                                    <Clock className="mr-1 h-3 w-3" />
                                                    Menunggu Verifikasi
                                                </Badge>
                                            )}
                                            {selectedPengajuan.status_laporan === 'diterima' && (
                                                <Badge className="border-green-200 bg-green-100 text-green-800">
                                                    <CheckCircle className="mr-1 h-3 w-3" />
                                                    Diterima
                                                </Badge>
                                            )}
                                            {selectedPengajuan.status_laporan === 'ditolak' && (
                                                <Badge className="border-red-200 bg-red-100 text-red-800">
                                                    <XCircle className="mr-1 h-3 w-3" />
                                                    Ditolak
                                                </Badge>
                                            )}
                                        </div>
                                    </div>

                                    {/* File Laporan */}
                                    {selectedPengajuan.file_laporan_penggunaan && (
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">File Laporan</Label>
                                            <div className="mt-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => window.open(`/storage/${selectedPengajuan.file_laporan_penggunaan}`, '_blank')}
                                                >
                                                    <FileText className="mr-2 h-4 w-4" />
                                                    Lihat Laporan
                                                </Button>
                                            </div>
                                        </div>
                                    )}

                                    {/* Tanggal Upload */}
                                    {selectedPengajuan.tanggal_upload_laporan && (
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Tanggal Upload</Label>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {new Date(selectedPengajuan.tanggal_upload_laporan).toLocaleDateString('id-ID', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                })}
                                            </p>
                                        </div>
                                    )}

                                    {/* Form Verifikasi Laporan */}
                                    {selectedPengajuan.status_laporan === 'menunggu_verifikasi' && (
                                        <div className="border-t pt-4">
                                            <h4 className="mb-4 text-lg font-medium text-gray-900">Verifikasi Laporan</h4>

                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="status_laporan">Status Verifikasi</Label>
                                                    <select
                                                        id="status_laporan"
                                                        value={laporanData.status_laporan}
                                                        onChange={(e) => setLaporanData('status_laporan', e.target.value)}
                                                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                                    >
                                                        <option value="">Pilih Status</option>
                                                        <option value="diterima">Terima Laporan</option>
                                                        <option value="ditolak">Tolak Laporan</option>
                                                    </select>
                                                </div>

                                                <div>
                                                    <Label htmlFor="catatan_laporan">Catatan (Opsional)</Label>
                                                    <textarea
                                                        id="catatan_laporan"
                                                        value={laporanData.catatan_laporan}
                                                        onChange={(e) => setLaporanData('catatan_laporan', e.target.value)}
                                                        rows={3}
                                                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                                        placeholder="Berikan catatan untuk laporan ini..."
                                                    />
                                                </div>

                                                <div className="flex justify-end space-x-2">
                                                    <Button variant="outline" onClick={() => setShowLaporanModal(false)}>
                                                        Batal
                                                    </Button>
                                                    <Button onClick={handleUpdateLaporan} disabled={processingLaporan}>
                                                        {processingLaporan ? 'Menyimpan...' : 'Simpan Verifikasi'}
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Catatan Laporan (jika ada) */}
                                    {selectedPengajuan.catatan_laporan && (
                                        <div>
                                            <Label className="text-sm font-medium text-gray-700">Catatan Verifikasi</Label>
                                            <p className="mt-1 text-sm whitespace-pre-wrap text-gray-900">{selectedPengajuan.catatan_laporan}</p>
                                        </div>
                                    )}
                                </div>
                            )}
                        </DialogContent>
                    </Dialog>
                </Card>
            </div>
        </DinsosRiauLayout>
    );
}
