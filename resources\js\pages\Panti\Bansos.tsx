import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Calendar, Download, FileText, Plus, Search, UserCheck, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

interface LaporanSpj {
    id: number;
    tanggal: string;
    status: string;
    catatan_admin?: string;
}

interface JadwalEvaluasi {
    id: number;
    panti_id: number; // tambahkan agar bisa filter
    nama_tim: string;
    file_tim: string | null;
    nama_evaluator: string;
    nip: string;
    bidang: string;
    tanggal_evaluasi: string;
    jam_evaluasi: string;
    status: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    jadwalEvaluasi: JadwalEvaluasi[];
    canCreatePengajuan: boolean;
    pantiId?: number | null;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Bantuan Sosial',
        href: '/panti/bansos',
    },
];
function Bansos({ user, jadwalEvaluasi, canCreatePengajuan, pantiId }: Props) {
    // Ensure jadwalEvaluasi is always an array
    const safeJadwalEvaluasi = Array.isArray(jadwalEvaluasi) ? jadwalEvaluasi : [];
    // State untuk modal/modal terkait
    const [showPengajuanModal, setShowPengajuanModal] = useState(false);
    const [showStatusModal, setShowStatusModal] = useState(false);
    const [showJadwalModal, setShowJadwalModal] = useState(false);
    // State untuk jadwal evaluasi DINSOS
    const [jadwalDinsos, setJadwalDinsos] = useState<JadwalEvaluasi[]>([]);
    const [loadingJadwal, setLoadingJadwal] = useState(false);
    const filteredJadwalEvaluasi =
        jadwalDinsos.length > 0
            ? jadwalDinsos // Tampilkan semua jadwal evaluasi dari DINSOS (tidak difilter berdasarkan panti)
            : safeJadwalEvaluasi.filter((jadwal: JadwalEvaluasi) => jadwal.panti_id === pantiId);

    useEffect(() => {
        if (showJadwalModal) {
            setLoadingJadwal(true);
            fetch('/dinsosriau/lks/bansos/jadwal-evaluasi', {
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
            })
                .then((res) => res.json())
                .then((res) => {
                    if (res.success && Array.isArray(res.data)) {
                        setJadwalDinsos(res.data);
                    }
                })
                .finally(() => setLoadingJadwal(false));
        }
    }, [showJadwalModal]);

    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    // Data dummy persyaratan dan prosedur
    const persyaratanList = [
        'Menyiapkan proposal yang terdiri dari Latar Belakang, Maksud dan Tujuan, Hasil yang diharapkan, Lokasi Pelaksanaan, Waktu Pelaksanaan, Data Umum Organisasi / Lembaga, Alamat Lengkap, Susunan Kepengurusan, RAB, Nomor Rekening Bank yang Masih Berlaku, Penutup.',
        'Akta notaris mengenai pendirian lembaga atau dokumen lain yang dipersamakan.',
        'Surat pernyataan tanggungjawab.',
        'Pakta Integritas.',
        'NPWP.',
        'Surat keterangan domisili desa/kelurahan setempat.',
        'Izin operasional/ tanda daftar lembaga dari instasi yang berwenang.',
        'Foto Kartu Tanda Penduduk yang masih berlaku atas nama ketua dan sekretaris atau sebutan lain.',
        'Foto rekening bank yang masih aktif atas nama lembaga.',
        'SK Pengurus panti',
        'Surat pengesahan dari Kemenkumham',
        'Tanda Daftar LKS',
        'Surat Rekomendasi Kabupaten/Kota',
        'Data Anak.',
        'Sarana dan prasarana panti.',
        'Rencana anggaran biaya',
    ];

    // State untuk toggle lihat selengkapnya
    const [showAllSyarat, setShowAllSyarat] = useState(false);
    const maxShowSyarat = 5;

    // Data dummy pengajuan
    const pengajuanData = [
        { id: 1, nama: 'Panti Asuhan Kasih Sayang', kabupaten: 'Pekanbaru', tanggal: '2024-01-15', status: 'Diterima' },
        { id: 2, nama: 'Panti Asuhan Harapan Bangsa', kabupaten: 'Dumai', tanggal: '2024-02-10', status: 'Diproses' },
        { id: 3, nama: 'Panti Asuhan Sinar Mentari', kabupaten: 'Kampar', tanggal: '2024-03-05', status: 'Ditolak' },
        { id: 4, nama: 'Panti Asuhan Cahaya Hati', kabupaten: 'Rokan Hulu', tanggal: '2024-03-20', status: 'Diterima' },
        { id: 5, nama: 'Panti Asuhan Bintang Timur', kabupaten: 'Bengkalis', tanggal: '2024-04-01', status: 'Diproses' },
        { id: 6, nama: 'Panti Asuhan Mutiara Hati', kabupaten: 'Indragiri Hulu', tanggal: '2024-04-15', status: 'Diterima' },
        { id: 7, nama: 'Panti Asuhan Sejahtera', kabupaten: 'Kuantan Singingi', tanggal: '2024-05-01', status: 'Ditolak' },
    ];

    // Pagination
    const totalPages = Math.ceil(pengajuanData.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentData = pengajuanData.slice(startIndex, startIndex + itemsPerPage);

    const handleDownloadFile = (fileName: string) => {
        // Direct download from the specified source
        if (fileName === 'Panduan_Persyaratan_Bansos.pdf') {
            const link = document.createElement('a');
            link.href = 'https://peraturan.bpk.go.id/Download/200760/pergub-no-2-tahun-2022-tentang-hibah-bansos.pdf';
            link.download = 'Pergub No 2 Tahun 2022 - Hibah Bansos.pdf';
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            // Placeholder untuk download file lainnya
            alert(`Download file: ${fileName} (sumber menyusul)`);
        }
    };

    const handleSubmitPengajuan = () => {
        alert('Pengajuan berhasil dikirim!');
        setShowPengajuanModal(false);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'Diterima':
                return 'bg-green-100 text-green-700';
            case 'Ditolak':
                return 'bg-red-100 text-red-700';
            case 'Diproses':
                return 'bg-yellow-100 text-yellow-700';
            default:
                return 'bg-gray-100 text-gray-700';
        }
    };

    const getStatusInfo = (status: string, nama: string) => {
        switch (status) {
            case 'Diterima':
                return `Pengajuan ${nama} telah disetujui. Dana bantuan akan segera dicairkan ke rekening panti.`;
            case 'Ditolak':
                return `Pengajuan ${nama} ditolak karena dokumen belum lengkap. Silakan lengkapi dokumen dan ajukan kembali.`;
            case 'Diproses':
                return `Pengajuan ${nama} sedang dalam tahap verifikasi. Proses ini membutuhkan waktu 7-14 hari kerja.`;
            default:
                return 'Status tidak diketahui.';
        }
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Persyaratan Section */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <FileText className="h-4 w-4" />
                            <span>Persyaratan Pengajuan Bantuan Sosial</span>
                        </CardTitle>
                        <CardDescription>Dokumen-dokumen yang harus disiapkan untuk mengajukan bantuan sosial</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {(showAllSyarat ? persyaratanList : persyaratanList.slice(0, maxShowSyarat)).map((item, idx) => (
                                <div key={idx} className="flex items-start space-x-3">
                                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-xs font-semibold text-blue-600">
                                        {idx + 1}
                                    </div>
                                    <p className="text-sm text-gray-700">{item}</p>
                                </div>
                            ))}
                        </div>
                        {persyaratanList.length > maxShowSyarat && (
                            <div className="mt-2">
                                {!showAllSyarat ? (
                                    <button className="text-sm text-blue-700 hover:underline" onClick={() => setShowAllSyarat(true)}>
                                        Lihat Selengkapnya
                                    </button>
                                ) : (
                                    <button className="text-sm text-blue-700 hover:underline" onClick={() => setShowAllSyarat(false)}>
                                        Tampilkan Lebih Sedikit
                                    </button>
                                )}
                            </div>
                        )}
                        <div className="mt-6 flex gap-3">
                            <Button
                                onClick={() => handleDownloadFile('Panduan_Persyaratan_Bansos.pdf')}
                                variant="outline"
                                className="flex items-center space-x-2"
                            >
                                <Download className="h-4 w-4" />
                                <span>Download Panduan</span>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card className="cursor-pointer transition-shadow hover:shadow-lg">
                        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
                            <div className="mb-3 rounded-full bg-green-100 p-3">
                                <Plus className="h-6 w-6 text-green-600" />
                            </div>
                            <h3 className="mb-2 font-semibold text-gray-900">Ajukan Bantuan Sosial</h3>
                            <p className="mb-4 text-sm text-gray-600">
                                {canCreatePengajuan
                                    ? 'Buat pengajuan bantuan sosial baru'
                                    : 'Anda masih memiliki pengajuan yang sedang diproses atau menunggu pencairan dana'}
                            </p>
                            {canCreatePengajuan ? (
                                <a
                                    href="/panti/bansos/pengajuan"
                                    className="inline-flex h-9 w-full items-center justify-center gap-2 rounded-md bg-primary px-4 py-2 text-sm font-medium whitespace-nowrap text-primary-foreground shadow-xs transition-[color,box-shadow] hover:bg-primary/90"
                                    style={{ textAlign: 'center', textDecoration: 'none' }}
                                >
                                    Mulai Pengajuan
                                </a>
                            ) : (
                                <Button className="w-full" disabled variant="secondary">
                                    Pengajuan Ditangguhkan
                                </Button>
                            )}
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer transition-shadow hover:shadow-lg">
                        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
                            <div className="mb-3 rounded-full bg-blue-100 p-3">
                                <Search className="h-6 w-6 text-blue-600" />
                            </div>
                            <h3 className="mb-2 font-semibold text-gray-900">Status Pengajuan</h3>
                            <p className="mb-4 text-sm text-gray-600">Cek status pengajuan bantuan Anda</p>
                            <Dialog open={showStatusModal} onOpenChange={setShowStatusModal}>
                                <DialogTrigger asChild>
                                    <Button variant="outline" className="w-full" asChild>
                                        <a href="/panti/statuspengajuan">Lihat Status</a>
                                    </Button>
                                </DialogTrigger>
                            </Dialog>
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer transition-shadow hover:shadow-lg">
                        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
                            <div className="mb-3 rounded-full bg-orange-100 p-3">
                                <UserCheck className="h-6 w-6 text-orange-600" />
                            </div>
                            <h3 className="mb-2 font-semibold text-gray-900">Surat Pertanggungjawaban</h3>
                            <p className="mb-4 text-sm text-gray-600">Upload laporan penggunaan bantuan</p>
                            <Button variant="outline" className="w-full" asChild>
                                <a href="/panti/bansos/laporan">Upload SPJ</a>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card className="cursor-pointer transition-shadow hover:shadow-lg">
                        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
                            <div className="mb-3 rounded-full bg-purple-100 p-3">
                                <Calendar className="h-6 w-6 text-purple-600" />
                            </div>
                            <h3 className="mb-2 font-semibold text-gray-900">Jadwal Evaluasi</h3>
                            <p className="mb-4 text-sm text-gray-600">Lihat jadwal evaluasi dari DINSOS</p>
                            <Button variant="outline" className="w-full" onClick={() => setShowJadwalModal(true)}>
                                Lihat Jadwal
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Modal Jadwal Evaluasi */}
                <Dialog open={showJadwalModal} onOpenChange={setShowJadwalModal}>
                    <DialogContent className="max-w-4xl">
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5 text-purple-600" />
                                Jadwal Evaluasi DINSOS Provinsi Riau
                            </DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <p className="text-sm text-gray-600">
                                Berikut adalah jadwal evaluasi yang telah ditetapkan oleh DINSOS Provinsi Riau untuk program bantuan sosial.
                            </p>

                            <div className="rounded-lg border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>No</TableHead>
                                            <TableHead>Tanggal</TableHead>
                                            <TableHead>Jam</TableHead>
                                            <TableHead>Status</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {loadingJadwal ? (
                                            <TableRow>
                                                <TableCell colSpan={4} className="py-8 text-center text-gray-500">
                                                    Memuat data jadwal evaluasi...
                                                </TableCell>
                                            </TableRow>
                                        ) : filteredJadwalEvaluasi.length > 0 ? (
                                            filteredJadwalEvaluasi.map((jadwal, index) => (
                                                <TableRow key={jadwal.id}>
                                                    <TableCell className="font-medium">{index + 1}</TableCell>
                                                    <TableCell>
                                                        {new Date(jadwal.tanggal_evaluasi).toLocaleDateString('id-ID', {
                                                            year: 'numeric',
                                                            month: 'long',
                                                            day: 'numeric',
                                                        })}
                                                    </TableCell>
                                                    <TableCell className="font-medium">{jadwal.jam_evaluasi}</TableCell>
                                                    <TableCell>
                                                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                                            {jadwal.status}
                                                        </span>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        ) : (
                                            <TableRow>
                                                <TableCell colSpan={4} className="py-8 text-center text-gray-500">
                                                    Belum ada jadwal evaluasi yang ditetapkan oleh DINSOS
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            <div className="rounded-lg bg-blue-50 p-4">
                                <h4 className="mb-2 font-medium text-blue-900">Informasi Penting:</h4>
                                <ul className="space-y-1 text-sm text-blue-800">
                                    <li>• Pastikan panti siap menerima kunjungan tim evaluasi sesuai jadwal</li>
                                    <li>• Siapkan dokumen-dokumen yang diperlukan untuk proses evaluasi</li>
                                    <li>• Hubungi DINSOS jika ada perubahan jadwal atau kendala lainnya</li>
                                    <li>• Evaluasi akan berlangsung selama 2-3 jam untuk setiap panti</li>
                                </ul>
                            </div>

                            <div className="flex justify-end">
                                <Button onClick={() => setShowJadwalModal(false)}>Tutup</Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </PantiLayout>
    );
}

export default Bansos;
