import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Upload } from 'lucide-react';

interface PengajuanBansos {
    id: number;
    nama_panti: string;
    nama_ketua: string;
    kabupaten_kota: string;
    no_hp: string;
    tanggal_pengajuan: string;
    proposal?: string;
    sk_pengurus?: string;
    rencana_anggaran?: string;
    akta_notaris?: string;
    surat_pengesahan?: string;
    tanda_daftar_lks?: string;
    data_anak?: string;
    sarana_prasarana?: string;
    surat_pernyataan?: string;
    pakta_integritas?: string;
    npwp?: string;
    surat_domisil?: string;
    izin_operasional?: string;
    foto_ktp?: string;
    foto_rekening?: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pengajuan: PengajuanBansos;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Bantuan Sosial',
        href: '/panti/bansos',
    },
    {
        title: 'Status Pengajuan',
        href: '/panti/bansos/statuspengajuan',
    },
    {
        title: 'Edit Pengajuan',
        href: '#',
    },
];

export default function EditPengajuan({ user, pengajuan }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        nama_panti: pengajuan.nama_panti || '',
        nama_ketua: pengajuan.nama_ketua || '',
        kabupaten_kota: pengajuan.kabupaten_kota || '',
        no_hp: pengajuan.no_hp || '',
        tanggal_pengajuan: pengajuan.tanggal_pengajuan?.split('T')[0] || '',
        proposal: null as File | null,
        sk_pengurus: null as File | null,
        rencana_anggaran: null as File | null,
        akta_notaris: null as File | null,
        surat_pengesahan: null as File | null,
        tanda_daftar_lks: null as File | null,
        data_anak: null as File | null,
        sarana_prasarana: null as File | null,
        surat_pernyataan: null as File | null,
        pakta_integritas: null as File | null,
        npwp: null as File | null,
        surat_domisil: null as File | null,
        izin_operasional: null as File | null,
        foto_ktp: null as File | null,
        foto_rekening: null as File | null,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('panti.bansos.update', pengajuan.id));
    };

    const handleFileChange = (field: string, file: File | null) => {
        setData(field as any, file);
    };

    const documentFields = [
        { key: 'proposal', label: 'Proposal Kegiatan' },
        { key: 'sk_pengurus', label: 'SK Pengurus' },
        { key: 'rencana_anggaran', label: 'Rencana Anggaran Belanja (RAB)' },
        { key: 'akta_notaris', label: 'Akta Notaris' },
        { key: 'surat_pengesahan', label: 'Surat Pengesahan Kemenkumham' },
        { key: 'tanda_daftar_lks', label: 'Tanda Daftar LKS' },
        { key: 'data_anak', label: 'Data Anak Asuh' },
        { key: 'sarana_prasarana', label: 'Sarana Prasarana' },
        { key: 'surat_pernyataan', label: 'Surat Pernyataan' },
        { key: 'pakta_integritas', label: 'Pakta Integritas' },
        { key: 'npwp', label: 'NPWP' },
        { key: 'surat_domisil', label: 'Surat Domisil' },
        { key: 'izin_operasional', label: 'Izin Operasional' },
        { key: 'foto_ktp', label: 'Foto KTP Ketua' },
        { key: 'foto_rekening', label: 'Foto Rekening Bank' },
    ];

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Pengajuan Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <Link href={route('panti.bansos.status')}>
                            <ArrowLeft className="h-4 w-4" />
                            Kembali
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-xl font-semibold text-gray-900">Edit Pengajuan Bantuan Sosial</h1>
                        <p className="text-sm text-gray-600">Perbarui data pengajuan bantuan sosial Anda</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="grid gap-6">
                        {/* Data Dasar */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Data Dasar</CardTitle>
                            </CardHeader>
                            <CardContent className="grid gap-4">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="nama_panti">Nama Panti Asuhan *</Label>
                                        <Input
                                            id="nama_panti"
                                            value={data.nama_panti}
                                            onChange={(e) => setData('nama_panti', e.target.value)}
                                            className={errors.nama_panti ? 'border-red-500' : ''}
                                        />
                                        {errors.nama_panti && <p className="text-sm text-red-500">{errors.nama_panti}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="nama_ketua">Nama Ketua *</Label>
                                        <Input
                                            id="nama_ketua"
                                            value={data.nama_ketua}
                                            onChange={(e) => setData('nama_ketua', e.target.value)}
                                            className={errors.nama_ketua ? 'border-red-500' : ''}
                                        />
                                        {errors.nama_ketua && <p className="text-sm text-red-500">{errors.nama_ketua}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="kabupaten_kota">Kabupaten/Kota *</Label>
                                        <Input
                                            id="kabupaten_kota"
                                            value={data.kabupaten_kota}
                                            onChange={(e) => setData('kabupaten_kota', e.target.value)}
                                            className={errors.kabupaten_kota ? 'border-red-500' : ''}
                                        />
                                        {errors.kabupaten_kota && <p className="text-sm text-red-500">{errors.kabupaten_kota}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="no_hp">No. HP *</Label>
                                        <Input
                                            id="no_hp"
                                            value={data.no_hp}
                                            onChange={(e) => setData('no_hp', e.target.value)}
                                            className={errors.no_hp ? 'border-red-500' : ''}
                                        />
                                        {errors.no_hp && <p className="text-sm text-red-500">{errors.no_hp}</p>}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="tanggal_pengajuan">Tanggal Pengajuan *</Label>
                                        <Input
                                            id="tanggal_pengajuan"
                                            type="date"
                                            value={data.tanggal_pengajuan}
                                            onChange={(e) => setData('tanggal_pengajuan', e.target.value)}
                                            className={errors.tanggal_pengajuan ? 'border-red-500' : ''}
                                        />
                                        {errors.tanggal_pengajuan && <p className="text-sm text-red-500">{errors.tanggal_pengajuan}</p>}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Dokumen Persyaratan */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Dokumen Persyaratan</CardTitle>
                                <p className="text-sm text-gray-600">Kosongkan file jika tidak ingin mengubah dokumen yang sudah ada</p>
                            </CardHeader>
                            <CardContent className="grid gap-4">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    {documentFields.map((field) => (
                                        <div key={field.key} className="space-y-2">
                                            <Label htmlFor={field.key}>{field.label}</Label>
                                            <div className="flex items-center gap-2">
                                                <div className="relative flex-1">
                                                    <Input
                                                        id={field.key}
                                                        type="file"
                                                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                                        onChange={(e) => {
                                                            const file = e.target.files?.[0] || null;
                                                            handleFileChange(field.key, file);
                                                        }}
                                                        className="file:mr-4 file:rounded-full file:border-0 file:bg-blue-50 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-blue-700 hover:file:bg-blue-100"
                                                    />
                                                </div>
                                                {pengajuan[field.key as keyof PengajuanBansos] && (
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => {
                                                            const url = `/storage/${pengajuan[field.key as keyof PengajuanBansos]}`;
                                                            window.open(url, '_blank');
                                                        }}
                                                    >
                                                        Lihat File
                                                    </Button>
                                                )}
                                            </div>
                                            {errors[field.key as keyof typeof errors] && (
                                                <p className="text-sm text-red-500">{errors[field.key as keyof typeof errors]}</p>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Submit Button */}
                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="outline" asChild>
                                <Link href={route('panti.bansos.status')}>Batal</Link>
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Menyimpan...' : 'Simpan Perubahan'}
                                <Upload className="ml-2 h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </form>
            </div>
        </PantiLayout>
    );
}
