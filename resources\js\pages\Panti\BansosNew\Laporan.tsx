import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Edit, Info, Search, Upload } from 'lucide-react';
import { useMemo, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Bantuan Sosial',
        href: '/panti/bansos',
    },
    {
        title: 'Surat Pertanggungjawaban',
        href: '/panti/bansos/laporan',
    },
];

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    laporanList: Array<{
        id: number;
        nama_panti: string;
        kabupaten_kota: string;
        file_laporan: string;
        status: string;
        keterangan?: string;
        catatan_admin?: string;
        created_at: string;
    }>;
    pagination: {
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    filters: {
        status: string;
    };
}

export default function Laporan({ user, laporanList, pagination, filters }: Props) {
    const [showInfoModal, setShowInfoModal] = useState(false);
    const [selectedItem, setSelectedItem] = useState<Props['laporanList'][0] | null>(null);

    // Search, status filter, and pagination state
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [statusFilter, setStatusFilter] = useState<string>('');
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    // Filter and paginate data
    const filteredData = useMemo(() => {
        let filtered = laporanList;

        // Filter by status if selected
        if (statusFilter && statusFilter !== 'semua') {
            filtered = filtered.filter((item) => item.status?.toLowerCase() === statusFilter.toLowerCase());
        }

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(
                (item) =>
                    item.nama_panti?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    item.kabupaten_kota?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    item.status?.toLowerCase().includes(searchTerm.toLowerCase()),
            );
        }

        return filtered;
    }, [laporanList, searchTerm, statusFilter]);

    const totalPages = Math.max(1, Math.ceil(filteredData.length / itemsPerPage));
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

    // Reset to first page when search or filter changes
    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
        setCurrentPage(1);
    };
    const handleStatusFilterChange = (value: string) => {
        setStatusFilter(value);
        setCurrentPage(1);
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'diterima':
                return 'bg-green-100 text-green-700 border-green-300';
            case 'ditolak':
                return 'bg-red-100 text-red-700 border-red-300';
            case 'diproses':
                return 'bg-yellow-100 text-yellow-700 border-yellow-300';
            default:
                return 'bg-gray-100 text-gray-700 border-gray-300';
        }
    };

    const getStatusInfo = (item: Props['laporanList'][0]) => {
        switch (item.status.toLowerCase()) {
            case 'diterima':
                return `SPJ ${item.nama_panti} telah disetujui. ${item.keterangan ? `Catatan: ${item.keterangan}` : ''}`;
            case 'ditolak':
                return `SPJ ${item.nama_panti} ditolak. ${item.catatan_admin ? `Catatan Penolakan: ${item.catatan_admin}` : item.keterangan ? `Alasan: ${item.keterangan}` : 'Silakan hubungi admin untuk informasi lebih lanjut.'}`;
            case 'diproses':
                return `SPJ ${item.nama_panti} sedang dalam tahap verifikasi. Proses ini membutuhkan waktu 7-14 hari kerja. ${item.keterangan ? `Catatan: ${item.keterangan}` : ''}`;
            default:
                return 'Status tidak diketahui.';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const handleShowInfo = (item: Props['laporanList'][0]) => {
        setSelectedItem(item);
        setShowInfoModal(true);
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Surat Pertanggungjawaban" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Surat Pertanggungjawaban</h1>
                        <p className="text-gray-600">Kelola surat pertanggungjawaban bantuan sosial</p>
                    </div>
                    <Button asChild>
                        <Link href="/panti/bansos/laporan/upload">
                            <Upload className="mr-2 h-4 w-4" />
                            Upload SPJ
                        </Link>
                    </Button>
                </div>

                {/* Search & Status Filter Bar */}
                <div className="flex items-center justify-between gap-4">
                    <div className="flex flex-1 gap-2">
                        <div className="relative max-w-md flex-1">
                            <Search className="absolute top-2.5 left-2 h-4 w-4 text-gray-500" />
                            <Input
                                placeholder="Cari berdasarkan nama panti, kabupaten, atau status..."
                                value={searchTerm}
                                onChange={(e) => handleSearchChange(e.target.value)}
                                className="pl-8"
                            />
                        </div>
                        <select
                            value={statusFilter}
                            onChange={(e) => handleStatusFilterChange(e.target.value)}
                            className="rounded border bg-white px-2 py-1 text-sm text-gray-700"
                            style={{ minWidth: 120 }}
                        >
                            <option value="">Semua Status</option>
                            <option value="diterima">Diterima</option>
                            <option value="ditolak">Ditolak</option>
                            <option value="diproses">Diproses</option>
                        </select>
                    </div>
                    <div className="text-sm text-gray-600">Total: {filteredData.length} SPJ</div>
                </div>

                {/* Table */}
                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-12">No</TableHead>
                                <TableHead>Nama Panti</TableHead>
                                <TableHead>Kabupaten/Kota</TableHead>
                                <TableHead>Tanggal</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Aksi</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {paginatedData.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="py-8 text-center text-gray-500">
                                        {searchTerm ? 'Tidak ada SPJ yang sesuai dengan pencarian' : 'Belum ada laporan SPJ'}
                                    </TableCell>
                                </TableRow>
                            ) : (
                                paginatedData.map((item, index) => (
                                    <TableRow key={item.id}>
                                        <TableCell className="font-medium">{startIndex + index + 1}</TableCell>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{item.nama_panti}</div>
                                                <div className="text-sm text-gray-500">{item.file_laporan}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>{item.kabupaten_kota}</TableCell>
                                        <TableCell>{formatDate(item.created_at)}</TableCell>
                                        <TableCell>
                                            <Badge className={getStatusColor(item.status)}>{item.status}</Badge>
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <div className="flex justify-end gap-2">
                                                {/* Edit button hanya muncul untuk status ditolak */}
                                                {item.status.toLowerCase() === 'ditolak' && (
                                                    <Button variant="outline" size="sm" onClick={() => router.visit('/panti/bansos/laporan/upload')}>
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                )}
                                                <Button variant="outline" size="sm" onClick={() => handleShowInfo(item)}>
                                                    <Info className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))
                            )}
                        </TableBody>
                    </Table>

                    {/* Pagination - Always Show */}
                    <div className="flex items-center justify-between border-t p-4">
                        <div className="text-sm text-gray-600">
                            Halaman {currentPage} dari {totalPages} | Total: {filteredData.length} SPJ
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                disabled={currentPage === 1 || totalPages <= 1}
                            >
                                Sebelumnya
                            </Button>

                            {/* Page numbers - Always show at least page 1 */}
                            <div className="flex items-center gap-1">
                                {Array.from({ length: Math.max(1, totalPages) }, (_, i) => i + 1).map((page) => (
                                    <Button
                                        key={page}
                                        variant={currentPage === page ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => setCurrentPage(page)}
                                        className="h-8 w-8"
                                        disabled={totalPages <= 1}
                                    >
                                        {page}
                                    </Button>
                                ))}
                            </div>

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                disabled={currentPage === totalPages || totalPages <= 1}
                            >
                                Selanjutnya
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Bottom Action Buttons */}
                <div className="flex items-center justify-start">
                    <Button variant="outline" asChild>
                        <Link href="/panti/bansos" className="flex items-center gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Kembali
                        </Link>
                    </Button>
                </div>

                {/* Info Modal */}
                <Dialog open={showInfoModal} onOpenChange={setShowInfoModal}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Informasi Status SPJ</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            {selectedItem && (
                                <>
                                    <div>
                                        <h4 className="font-medium text-gray-900">{selectedItem.nama_panti}</h4>
                                        <p className="text-sm text-gray-600">{selectedItem.kabupaten_kota}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">File Laporan:</p>
                                        <p className="text-sm">{selectedItem.file_laporan}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Tanggal Upload:</p>
                                        <p className="text-sm">{formatDate(selectedItem.created_at)}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Status:</p>
                                        <Badge className={getStatusColor(selectedItem.status)}>{selectedItem.status}</Badge>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Informasi:</p>
                                        <p className="text-sm">{getStatusInfo(selectedItem)}</p>
                                        {selectedItem.status.toLowerCase() === 'ditolak' && selectedItem.catatan_admin && (
                                            <div className="mt-2 rounded border border-red-200 bg-red-50 p-3">
                                                <span className="font-medium text-red-700">Catatan Penolakan:</span>
                                                <div className="mt-1 whitespace-pre-line text-red-800">{selectedItem.catatan_admin}</div>
                                            </div>
                                        )}
                                    </div>
                                </>
                            )}
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
        </PantiLayout>
    );
}
