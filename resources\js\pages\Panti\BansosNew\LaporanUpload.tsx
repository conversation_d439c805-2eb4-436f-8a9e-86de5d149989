import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { ArrowLeft } from 'lucide-react';
import { FormEventHandler } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Bantuan Sosial',
        href: '/panti/bansos',
    },
    {
        title: 'Laporan SPJ',
        href: '/panti/bansos/laporan',
    },
    {
        title: 'Upload SPJ',
        href: '/panti/bansos/laporan/upload',
    },
];

interface PantiDropdown {
    id: number;
    nama: string;
}
interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    pantiList: PantiDropdown[];
    kabupatenKotaList: string[];
}

export default function LaporanUpload({ user, pantiList, kabupatenKotaList }: Props) {
    // Jika hanya ada satu panti, set default value otomatis (pakai nama)
    const initialPanti = pantiList && pantiList.length === 1 ? pantiList[0].nama : '';
    const { data, setData, post, processing, errors, reset } = useForm({
        nama_panti: initialPanti,
        kabupaten_kota: '',
        tanggal_upload: '',
        file_laporan: null as File | null,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('panti.bansos.laporan.store'), {
            forceFormData: true,
            onSuccess: () => reset(),
        });
    };

    // Hapus blok return jika pantiList kosong, lanjutkan ke form

    // Render form upload SPJ jika ada minimal satu panti
    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Upload Surat Pertanggungjawaban" />
            <div className="flex h-full flex-1 flex-col items-center justify-center px-4 py-6 lg:px-8">
                <div className="w-full max-w-lg">
                    <h1 className="mb-4 text-2xl font-bold text-gray-900">Upload Surat Pertanggungjawaban (SPJ)</h1>
                    <form onSubmit={submit} className="space-y-6">
                        <div>
                            <Label htmlFor="nama_panti">Nama Panti</Label>
                            <Input
                                id="nama_panti"
                                name="nama_panti"
                                type="text"
                                value={data.nama_panti}
                                onChange={(e) => setData('nama_panti', e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                required
                            />
                            {errors.nama_panti && <p className="mt-1 text-sm text-red-600">{errors.nama_panti}</p>}
                        </div>
                        <div>
                            <Label htmlFor="kabupaten_kota">Kabupaten/Kota</Label>
                            <select
                                id="kabupaten_kota"
                                name="kabupaten_kota"
                                value={data.kabupaten_kota}
                                onChange={(e) => setData('kabupaten_kota', e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                required
                            >
                                <option value="">-- Pilih Kabupaten/Kota --</option>
                                {kabupatenKotaList.map((kab) => (
                                    <option key={kab} value={kab}>
                                        {kab}
                                    </option>
                                ))}
                            </select>
                            {errors.kabupaten_kota && <p className="mt-1 text-sm text-red-600">{errors.kabupaten_kota}</p>}
                        </div>
                        <div>
                            <Label htmlFor="tanggal_upload">Tanggal Upload</Label>
                            <Input
                                id="tanggal_upload"
                                name="tanggal_upload"
                                type="date"
                                value={data.tanggal_upload}
                                onChange={(e) => setData('tanggal_upload', e.target.value)}
                                required
                            />
                            {errors.tanggal_upload && <p className="mt-1 text-sm text-red-600">{errors.tanggal_upload}</p>}
                        </div>
                        <div>
                            <Label htmlFor="file_laporan">File Laporan SPJ</Label>
                            <Input
                                id="file_laporan"
                                name="file_laporan"
                                type="file"
                                accept=".pdf,.doc,.docx"
                                onChange={(e) => setData('file_laporan', e.target.files?.[0] || null)}
                                required
                            />
                            {errors.file_laporan && <p className="mt-1 text-sm text-red-600">{errors.file_laporan}</p>}
                        </div>
                        <div className="flex items-center justify-between">
                            <Button variant="outline" asChild>
                                <a href="/panti/bansos/laporan" className="flex items-center gap-2">
                                    <ArrowLeft className="h-4 w-4" />
                                    Kembali
                                </a>
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Mengupload...' : 'Upload SPJ'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </PantiLayout>
    );
}
