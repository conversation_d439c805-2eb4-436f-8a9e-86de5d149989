import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { CheckCircle, Clock, CreditCard, Download, Eye } from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Bantuan Sosial',
        href: '/panti/bansos',
    },
    {
        title: 'Informasi Pencairan',
        href: '/panti/bansos/pencairan',
    },
];

interface FilePencairan {
    id: number;
    nama_file: string;
    file_path: string;
    jenis_file: string;
    keterangan?: string;
    created_at: string;
}

interface PengajuanBansos {
    id: number;
    nama_panti: string;
    kabupaten_kota: string;
    tanggal_pengajuan: string;
    status: string;
    file_pencairans: FilePencairan[];
    created_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
    };
    pengajuanList: PengajuanBansos[];
}

export default function Pencairan({ user, pengajuanList }: Props) {
    const [showDetailModal, setShowDetailModal] = useState<PengajuanBansos | null>(null);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getJenisFileLabel = (jenisFile: string) => {
        const labels: { [key: string]: string } = {
            bukti_transfer: 'Bukti Transfer',
            surat_pencairan: 'Surat Pencairan',
            dokumen_pendukung: 'Dokumen Pendukung',
        };
        return labels[jenisFile] || jenisFile;
    };

    const handleViewDetail = (pengajuan: PengajuanBansos) => {
        setShowDetailModal(pengajuan);
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Informasi Pencairan" />

            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {/* Header */}
                <div className="mb-4">
                    <h1 className="mb-2 text-3xl font-bold text-gray-900">Informasi Pencairan</h1>
                    <p className="text-gray-600">Pantau status dan informasi pencairan dana bantuan sosial</p>
                </div>

                {/* Info Card */}
                <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                            <div className="rounded-full bg-blue-100 p-2">
                                <CreditCard className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="flex-1">
                                <h3 className="mb-1 font-semibold text-blue-900">Informasi Pencairan</h3>
                                <p className="text-xs leading-relaxed text-blue-800">
                                    Halaman ini menampilkan informasi pencairan untuk pengajuan bantuan sosial yang telah disetujui. Anda dapat
                                    melihat dokumen pencairan yang telah diunggah oleh tim DINSOS.
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="h-5 w-5" />
                            Daftar Pengajuan yang Disetujui
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {pengajuanList.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-12">No</TableHead>
                                        <TableHead>Nama Panti</TableHead>
                                        <TableHead>Kabupaten/Kota</TableHead>
                                        <TableHead>Tanggal Pengajuan</TableHead>
                                        <TableHead>Status Pencairan</TableHead>
                                        <TableHead className="text-center">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {pengajuanList.map((item, index) => (
                                        <TableRow key={item.id}>
                                            <TableCell className="font-medium">{index + 1}</TableCell>
                                            <TableCell className="font-medium">{item.nama_panti}</TableCell>
                                            <TableCell>{item.kabupaten_kota}</TableCell>
                                            <TableCell>{formatDate(item.tanggal_pengajuan)}</TableCell>
                                            <TableCell>
                                                {item.file_pencairans.length > 0 ? (
                                                    <Badge className="border-green-300 bg-green-100 text-green-700">
                                                        <CheckCircle className="mr-1 h-3 w-3" />
                                                        File Tersedia
                                                    </Badge>
                                                ) : (
                                                    <Badge className="border-yellow-300 bg-yellow-100 text-yellow-700">
                                                        <Clock className="mr-1 h-3 w-3" />
                                                        Menunggu
                                                    </Badge>
                                                )}
                                            </TableCell>
                                            <TableCell className="text-center">
                                                {item.file_pencairans.length === 0 ? (
                                                    <Button
                                                        variant="default"
                                                        size="sm"
                                                        onClick={() => {
                                                            // TODO: Implementasi open modal/form upload file
                                                            window.alert('Fitur unggah file belum diimplementasikan di halaman ini.');
                                                        }}
                                                    >
                                                        Unggah File
                                                    </Button>
                                                ) : (
                                                    <Button variant="outline" size="sm" onClick={() => handleViewDetail(item)}>
                                                        <Eye className="mr-1 h-4 w-4" />
                                                        View
                                                    </Button>
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <CreditCard className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                <p className="text-gray-500">Belum ada pengajuan yang disetujui</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Detail Modal */}
                <Dialog open={!!showDetailModal} onOpenChange={() => setShowDetailModal(null)}>
                    <DialogContent className="max-w-4xl">
                        <DialogHeader>
                            <DialogTitle>Detail Informasi Pencairan</DialogTitle>
                        </DialogHeader>
                        {showDetailModal && (
                            <div className="space-y-6">
                                {/* Pengajuan Info */}
                                <div className="grid grid-cols-1 gap-4 rounded-lg bg-gray-50 p-4 md:grid-cols-2">
                                    <div>
                                        <p className="text-sm text-gray-600">Nama Panti</p>
                                        <p className="font-medium">{showDetailModal.nama_panti}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Kabupaten/Kota</p>
                                        <p className="font-medium">{showDetailModal.kabupaten_kota}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Tanggal Pengajuan</p>
                                        <p className="font-medium">{formatDate(showDetailModal.tanggal_pengajuan)}</p>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Status</p>
                                        <Badge className="border-green-300 bg-green-100 text-green-700">{showDetailModal.status}</Badge>
                                    </div>
                                </div>

                                {/* File Pencairan */}
                                <div>
                                    <h4 className="mb-3 font-semibold">Dokumen Pencairan</h4>
                                    {showDetailModal.file_pencairans.length > 0 ? (
                                        <div className="space-y-3">
                                            {showDetailModal.file_pencairans.map((file) => (
                                                <div key={file.id} className="flex items-center justify-between rounded-lg border p-3">
                                                    <div className="flex-1">
                                                        <p className="font-medium">{file.nama_file}</p>
                                                        <p className="text-sm text-gray-600">{getJenisFileLabel(file.jenis_file)}</p>
                                                        {file.keterangan && <p className="mt-1 text-sm text-gray-500">{file.keterangan}</p>}
                                                        <p className="mt-1 text-xs text-gray-400">Diunggah pada: {formatDate(file.created_at)}</p>
                                                    </div>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <a
                                                            href={`/storage/${file.file_path}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="flex items-center gap-1"
                                                        >
                                                            <Download className="h-4 w-4" />
                                                            Download
                                                        </a>
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="rounded-lg border-2 border-dashed border-gray-300 py-8 text-center">
                                            <Clock className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                                            <p className="text-gray-500">Belum ada dokumen pencairan yang diunggah</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
            </div>
        </PantiLayout>
    );
}
