import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useEffect, useState } from 'react';

interface PantiData {
    id: number;
    nama: string;
    pimpinan: string;
    email: string;
    telepon: string;
    user_name: string | null;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pantiList: PantiData[];
    userPanti?: {
        id: number;
        nama: string;
        pimpinan: string;
        email: string;
        telepon: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: '<PERSON><PERSON><PERSON>',
        href: '/panti/bansos',
    },
    {
        title: 'Pengajuan',
        href: '/panti/bansos/pengajuan',
    },
];

// Data kabupaten/kota di Provinsi Riau
const kabupatenKotaRiau = [
    'Pekanbaru',
    'Dumai',
    'Kampar',
    'Rokan Hulu',
    'Rokan Hilir',
    'Bengkalis',
    'Siak',
    'Pelalawan',
    'Indragiri Hulu',
    'Indragiri Hilir',
    'Kuantan Singingi',
    'Kepulauan Meranti',
];

export default function PengajuanBansos({ user, pantiList, userPanti }: Props) {
    const [showSuccess, setShowSuccess] = useState(false);
    const { data, setData, post, processing, errors, reset } = useForm({
        nama_panti: '',
        nama_ketua: '',
        kabupaten_kota: '',
        no_hp: '',
        tanggal_pengajuan: '',
        proposal: null as File | null,
        sk_pengurus: null as File | null,
        rencana_anggaran: null as File | null,
        akta_notaris: null as File | null,
        surat_pengesahan: null as File | null,
        tanda_daftar_lks: null as File | null,
        data_anak: null as File | null,
        sarana_prasarana: null as File | null,
        surat_pernyataan: null as File | null,
        pakta_integritas: null as File | null,
        npwp: null as File | null,
        surat_domisil: null as File | null,
        surat_rekomendasi: null as File | null,
        izin_operasional: null as File | null,
        foto_ktp: null as File | null,
        foto_rekening: null as File | null,
    });

    // Auto-fill user panti data if available
    useEffect(() => {
        if (userPanti) {
            setData((prev) => ({
                ...prev,
                nama_panti: userPanti.nama,
                nama_ketua: userPanti.pimpinan,
                no_hp: userPanti.telepon || '',
            }));
        }
    }, [userPanti]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        post('/panti/bansos/pengajuan', {
            onSuccess: () => {
                setShowSuccess(true);
                reset();
            },
        });
    };

    const handleFileChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        setData(field as any, file);
    };

    const dokumentFields = [
        { key: 'proposal', label: 'Proposal Bantuan Sosial', required: true },
        { key: 'sk_pengurus', label: 'SK Pengurus Panti', required: true },
        { key: 'rencana_anggaran', label: 'Rencana Anggaran Biaya', required: true },
        { key: 'akta_notaris', label: 'Akta Notaris', required: true },
        { key: 'surat_pengesahan', label: 'Surat Pengesahan Kemenkumham', required: true },
        { key: 'tanda_daftar_lks', label: 'Tanda Daftar LKS', required: true },
        { key: 'data_anak', label: 'Data Anak', required: true },
        { key: 'sarana_prasarana', label: 'Sarana dan Prasarana Panti', required: true },
        { key: 'surat_pernyataan', label: 'Surat Pernyataan Tanggung Jawab', required: true },
        { key: 'pakta_integritas', label: 'Pakta Integritas', required: true },
        { key: 'npwp', label: 'NPWP', required: true },
        { key: 'surat_domisil', label: 'Surat Keterangan Domisil', required: true },
        { key: 'surat_rekomendasi', label: 'Surat Rekomendasi Kabupaten/Kota', required: true },
        { key: 'izin_operasional', label: 'Izin Operasional', required: true },
        { key: 'foto_ktp', label: 'Foto KTP (Ketua, Sekretaris, Bendahara)', required: true },
        { key: 'foto_rekening', label: 'Foto Rekening Bank Panti Asuhan', required: true },
    ];

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            {/* Success Modal */}
            {showSuccess && (
                <div className="bg-opacity-40 fixed inset-0 z-50 flex items-center justify-center bg-black">
                    <div className="w-full max-w-md rounded-lg bg-white p-8 text-center shadow-lg">
                        <h2 className="mb-2 text-2xl font-bold text-green-600">Pengajuan Berhasil!</h2>
                        <p className="mb-4 text-gray-700">
                            Pengajuan bantuan sosial Anda telah berhasil dikirim. Silakan cek status pengajuan secara berkala.
                        </p>
                        <div className="flex justify-center gap-2">
                            <Button onClick={() => setShowSuccess(false)} autoFocus>
                                Tutup
                            </Button>
                            <Button variant="outline" asChild>
                                <a href="/panti/bansos/status">Lihat Status Pengajuan</a>
                            </Button>
                        </div>
                    </div>
                </div>
            )}
            <Head title="Pengajuan Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center gap-4">
                    {/* Kembali button removed */}
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Form Pengajuan Bantuan Sosial</h1>
                        <p className="mt-1 text-blue-600">Isi formulir berikut dengan lengkap dan upload semua dokumen yang diperlukan</p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6" encType="multipart/form-data">
                    {/* Data Panti */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Data Panti Asuhan</CardTitle>
                            <CardDescription>Informasi dasar panti asuhan</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <Label htmlFor="nama_panti">Nama Panti Asuhan *</Label>
                                    <Select
                                        value={data.nama_panti}
                                        onValueChange={(value) => {
                                            setData('nama_panti', value);
                                            // Auto-fill nama ketua berdasarkan panti yang dipilih
                                            const selectedPanti = pantiList.find((p) => p.nama === value);
                                            if (selectedPanti) {
                                                setData('nama_ketua', selectedPanti.pimpinan);
                                            }
                                        }}
                                    >
                                        <SelectTrigger className={errors.nama_panti ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Pilih nama panti asuhan" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {pantiList.map((panti) => (
                                                <SelectItem key={panti.id} value={panti.nama}>
                                                    {panti.nama}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.nama_panti && <p className="mt-1 text-sm text-red-600">{errors.nama_panti}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="nama_ketua">Nama Ketua *</Label>
                                    <Select value={data.nama_ketua} onValueChange={(value) => setData('nama_ketua', value)}>
                                        <SelectTrigger className={errors.nama_ketua ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Pilih nama ketua panti" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {pantiList.map((panti) => (
                                                <SelectItem key={panti.id} value={panti.pimpinan}>
                                                    {panti.pimpinan}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.nama_ketua && <p className="mt-1 text-sm text-red-600">{errors.nama_ketua}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="kabupaten_kota">Kabupaten/Kota *</Label>
                                    <Select value={data.kabupaten_kota} onValueChange={(value) => setData('kabupaten_kota', value)}>
                                        <SelectTrigger className={errors.kabupaten_kota ? 'border-red-500' : ''}>
                                            <SelectValue placeholder="Pilih kabupaten/kota" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {kabupatenKotaRiau.map((kabupaten, index) => (
                                                <SelectItem key={index} value={kabupaten}>
                                                    {kabupaten}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.kabupaten_kota && <p className="mt-1 text-sm text-red-600">{errors.kabupaten_kota}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="no_hp">No HP Panti *</Label>
                                    <Input
                                        id="no_hp"
                                        value={data.no_hp}
                                        onChange={(e) => setData('no_hp', e.target.value)}
                                        placeholder="Masukkan nomor HP panti"
                                        required
                                        className={errors.no_hp ? 'border-red-500' : ''}
                                    />
                                    {errors.no_hp && <p className="mt-1 text-sm text-red-600">{errors.no_hp}</p>}
                                </div>
                                <div>
                                    <Label htmlFor="tanggal_pengajuan">Tanggal Pengajuan *</Label>
                                    <Input
                                        id="tanggal_pengajuan"
                                        type="date"
                                        value={data.tanggal_pengajuan}
                                        onChange={(e) => setData('tanggal_pengajuan', e.target.value)}
                                        required
                                        className={errors.tanggal_pengajuan ? 'border-red-500' : ''}
                                    />
                                    {errors.tanggal_pengajuan && <p className="mt-1 text-sm text-red-600">{errors.tanggal_pengajuan}</p>}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Upload Dokumen */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Upload Dokumen Persyaratan</CardTitle>
                            <CardDescription>Upload semua dokumen yang diperlukan (format: PDF, DOC, DOCX, JPG, JPEG, PNG)</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2">
                                {dokumentFields.map((field) => (
                                    <div key={field.key} className="space-y-2">
                                        <Label htmlFor={field.key}>
                                            {field.label} {field.required && '*'}
                                        </Label>
                                        <div className="flex items-center space-x-2">
                                            <Input
                                                id={field.key}
                                                type="file"
                                                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                                onChange={handleFileChange(field.key)}
                                                required={field.required}
                                                className={errors[field.key as keyof typeof errors] ? 'border-red-500' : ''}
                                            />
                                            {/* Upload button removed */}
                                        </div>
                                        {errors[field.key as keyof typeof errors] && (
                                            <p className="mt-1 text-sm text-red-600">{errors[field.key as keyof typeof errors]}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" asChild>
                            <a href="/panti/bansos">Batal</a>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Mengirim...' : 'Submit Pengajuan'}
                        </Button>
                    </div>
                </form>
            </div>
        </PantiLayout>
    );
}
