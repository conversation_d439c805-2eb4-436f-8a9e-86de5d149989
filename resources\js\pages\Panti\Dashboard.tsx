import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip } from '@/components/ui/chart';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import {
    Activity,
    Banknote,
    BarChart3,
    Calendar,
    CheckCircle,
    ChevronLeft,
    ChevronRight,
    Clock,
    TrendingUp,
    UserCheck,
    UserPlus,
    Users,
    UserX,
} from 'lucide-react';
import { useState } from 'react';
import { <PERSON>, BarChart, CartesianGrid, XAxis, YAxis } from 'recharts';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    modules: {
        panti_asuhan: boolean;
        bantuan_sosial: boolean;
    };
    statsData: {
        totalAnak: number;
        anakAktif: number;
        prosesPengangkatan: number;
        tidakAktif: number;
    };
    pendidikanData: Array<{
        name: string;
        jumlah: number;
    }>;
    usiaData: Array<{
        name: string;
        jumlah: number;
    }>;
    bansosStats?: {
        totalBansosThisYear: number;
        totalNominalBansos: number;
        bansosStatus: Record<string, number>;
        currentYear: number;
    };
    userActivities?: Array<{
        id: number;
        title: string;
        description: string;
        date: string;
        progress: number;
        status: string;
        type: string;
    }>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard Panti Asuhan',
        href: '/panti/dashboard',
    },
];

export default function PantiDashboard({
    user,
    modules,
    statsData,
    pendidikanData,
    usiaData,
    bansosStats = {
        totalBansosThisYear: 0,
        totalNominalBansos: 0,
        bansosStatus: {},
        currentYear: new Date().getFullYear(),
    },
    userActivities = [],
}: Props) {
    // Pagination state for riwayat kegiatan
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 3; // Show 3 activities per page

    // Helper function to format currency to Rupiah
    const formatRupiah = (amount: number): string => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount);
    };

    // Helper function to get status badge with color
    const getStatusBadge = (status: string, count: number) => {
        const normalizedStatus = status.toLowerCase();

        if (normalizedStatus.includes('diterima') || normalizedStatus.includes('disetujui') || normalizedStatus.includes('approved')) {
            return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Diterima: {count}</Badge>;
        } else if (normalizedStatus.includes('proses') || normalizedStatus.includes('pending') || normalizedStatus.includes('menunggu')) {
            return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">Sedang Diproses: {count}</Badge>;
        } else if (normalizedStatus.includes('ditolak') || normalizedStatus.includes('rejected')) {
            return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Ditolak: {count}</Badge>;
        } else {
            return (
                <Badge variant="outline">
                    {status}: {count}
                </Badge>
            );
        }
    };

    // Chart configurations for shadcn
    const pendidikanChartConfig = {
        jumlah: {
            label: 'Jumlah Anak',
            color: '#2563eb', // Blue-600 (same as login form)
        },
    } satisfies ChartConfig;

    const usiaChartConfig = {
        jumlah: {
            label: 'Jumlah Anak',
            color: '#2563eb', // Blue-600 (same as login form)
        },
    } satisfies ChartConfig;

    // Use real user activities from backend
    const activityHistory = userActivities;

    // Filter completed activities for pagination
    const completedActivities = activityHistory.filter((activity) => activity.status === 'completed');

    // Pagination logic
    const totalPages = Math.max(1, Math.ceil(completedActivities.length / itemsPerPage)); // Always show at least 1 page
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentPageActivities = completedActivities.slice(startIndex, endIndex);

    // Pagination handlers
    const handlePreviousPage = () => {
        setCurrentPage((prev) => Math.max(1, prev - 1));
    };

    const handleNextPage = () => {
        setCurrentPage((prev) => Math.min(totalPages, prev + 1));
    };

    const handlePageClick = (page: number) => {
        setCurrentPage(page);
    };

    // Helper function untuk mendapatkan icon berdasarkan tipe kegiatan
    const getActivityIcon = (type: string) => {
        switch (type) {
            case 'data_anak':
                return <Users className="h-4 w-4 text-blue-600" />;
            case 'data_panti':
                return <UserCheck className="h-4 w-4 text-purple-600" />;
            case 'pengajuan_dana':
                return <Banknote className="h-4 w-4 text-green-600" />;
            case 'laporan_kegiatan':
                return <Calendar className="h-4 w-4 text-orange-600" />;
            case 'bantuan_sosial':
                return <CheckCircle className="h-4 w-4 text-indigo-600" />;
            default:
                return <Activity className="h-4 w-4 text-gray-600" />;
        }
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard Panti Asuhan" />

            {/* Year indicator in top-right corner */}
            <div className="absolute top-4 right-4 z-10">
                <div className="rounded-md bg-blue-600 px-3 py-1 text-sm font-semibold text-white shadow-lg">2025</div>
            </div>

            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Statistics Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-md bg-blue-100 p-2">
                                    <Users className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Anak Asuh</p>
                                    <p className="text-2xl font-bold">{statsData.totalAnak}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-md bg-blue-100 p-2">
                                    <UserCheck className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Anak Aktif</p>
                                    <p className="text-2xl font-bold text-blue-600">{statsData.anakAktif}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-md bg-blue-100 p-2">
                                    <UserPlus className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Proses Pengangkatan</p>
                                    <p className="text-2xl font-bold text-blue-600">{statsData.prosesPengangkatan}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center space-x-4">
                                <div className="rounded-md bg-blue-100 p-2">
                                    <UserX className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Tidak Aktif</p>
                                    <p className="text-2xl font-bold text-blue-600">{statsData.tidakAktif}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Card bantuan sosial dihapus sesuai permintaan user. Grid otomatis menyesuaikan. */}

                {/* Analytics Section */}
                <div className="grid gap-4 md:grid-cols-2">
                    {/* Education Distribution Chart */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <BarChart3 className="h-4 w-4" />
                                <span>Distribusi Pendidikan</span>
                            </CardTitle>
                            <CardDescription>Sebaran tingkat pendidikan anak asuh berdasarkan jenjang pendidikan</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ChartContainer config={pendidikanChartConfig} className="h-[300px] w-full">
                                <BarChart data={pendidikanData.length > 0 ? pendidikanData : [{ name: 'Belum ada data', jumlah: 0 }]}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="name" tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
                                    <YAxis
                                        tick={{ fontSize: 12 }}
                                        tickLine={false}
                                        axisLine={false}
                                        domain={[1, 'dataMax + 1']}
                                        allowDecimals={false}
                                        tickCount={6}
                                    />
                                    <ChartTooltip
                                        content={({ active, payload, label }) => {
                                            if (active && payload && payload.length) {
                                                return (
                                                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                                                        <div className="grid grid-cols-2 gap-2">
                                                            <div className="flex flex-col">
                                                                <span className="text-[0.70rem] text-muted-foreground uppercase">{label}</span>
                                                                <span className="font-bold text-muted-foreground">{payload[0].value} anak</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            return null;
                                        }}
                                    />
                                    <Bar dataKey="jumlah" fill="var(--color-jumlah)" radius={[4, 4, 0, 0]} />
                                </BarChart>
                            </ChartContainer>
                        </CardContent>
                    </Card>

                    {/* Age Distribution Chart */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <TrendingUp className="h-4 w-4" />
                                <span>Distribusi Usia</span>
                            </CardTitle>
                            <CardDescription>Sebaran usia anak asuh per tahun dari umur 5 hingga 18 tahun</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <ChartContainer config={usiaChartConfig} className="h-[300px] w-full">
                                <BarChart data={usiaData.length > 0 ? usiaData : [{ name: 'Belum ada data', jumlah: 0 }]}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="name" tick={{ fontSize: 12 }} tickLine={false} axisLine={false} />
                                    <YAxis
                                        tick={{ fontSize: 12 }}
                                        tickLine={false}
                                        axisLine={false}
                                        domain={[1, 'dataMax + 1']}
                                        allowDecimals={false}
                                        tickCount={6}
                                    />
                                    <ChartTooltip
                                        content={({ active, payload, label }) => {
                                            if (active && payload && payload.length) {
                                                return (
                                                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                                                        <div className="grid grid-cols-2 gap-2">
                                                            <div className="flex flex-col">
                                                                <span className="text-[0.70rem] text-muted-foreground uppercase">
                                                                    Usia {label} tahun
                                                                </span>
                                                                <span className="font-bold text-muted-foreground">{payload[0].value} anak</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            return null;
                                        }}
                                    />
                                    <Bar dataKey="jumlah" fill="var(--color-jumlah)" radius={[4, 4, 0, 0]} />
                                </BarChart>
                            </ChartContainer>
                        </CardContent>
                    </Card>
                </div>

                {/* Riwayat Kegiatan Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            <span>Riwayat Kegiatan</span>
                        </CardTitle>
                        <CardDescription>Kegiatan yang telah selesai dilakukan oleh {user.name}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {completedActivities.length > 0 ? (
                                currentPageActivities.map((activity) => (
                                    <div key={activity.id} className="rounded-lg border p-4 transition-colors hover:bg-gray-50">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start space-x-3">
                                                <div className="rounded-md bg-green-100 p-2">
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                </div>
                                                <div className="flex-1 space-y-1">
                                                    <div className="flex items-center space-x-2">
                                                        <h4 className="font-medium text-gray-900">{activity.title}</h4>
                                                        <Badge className="bg-green-100 text-green-800">Selesai</Badge>
                                                    </div>
                                                    <p className="text-sm text-gray-600">{activity.description}</p>
                                                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                                                        <Clock className="h-3 w-3" />
                                                        <span>
                                                            {new Date(activity.date).toLocaleDateString('id-ID', {
                                                                day: 'numeric',
                                                                month: 'long',
                                                                year: 'numeric',
                                                            })}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="rounded-lg border border-dashed border-gray-300 p-8 text-center">
                                    <CheckCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                    <h3 className="mb-2 text-lg font-medium text-gray-900">Belum ada kegiatan selesai</h3>
                                    <p className="text-gray-500">Kegiatan yang telah selesai akan muncul di sini.</p>
                                </div>
                            )}
                        </div>

                        {/* Pagination - Always show */}
                        <div className="mt-6 flex items-center justify-between border-t pt-4">
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handlePreviousPage}
                                    disabled={currentPage === 1}
                                    className="flex items-center space-x-1"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    <span>Sebelumnya</span>
                                </Button>
                            </div>

                            <div className="flex items-center space-x-2">
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                                    <Button
                                        key={page}
                                        variant={currentPage === page ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handlePageClick(page)}
                                        className="h-8 w-8 p-0"
                                    >
                                        {page}
                                    </Button>
                                ))}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleNextPage}
                                    disabled={currentPage === totalPages}
                                    className="flex items-center space-x-1"
                                >
                                    <span>Selanjutnya</span>
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                        {/* Summary Statistics */}
                        <div className="mt-6 grid grid-cols-2 gap-4 rounded-lg bg-green-50 p-4">
                            <div className="text-center">
                                <p className="text-2xl font-bold text-green-600">{completedActivities.length}</p>
                                <p className="text-sm text-green-700">Total Kegiatan Selesai</p>
                            </div>
                            <div className="text-center">
                                <p className="text-2xl font-bold text-blue-600">
                                    {completedActivities.length > 0
                                        ? new Date(Math.max(...completedActivities.map((a) => new Date(a.date).getTime()))).toLocaleDateString(
                                              'id-ID',
                                              { day: 'numeric', month: 'short' },
                                          )
                                        : '-'}
                                </p>
                                <p className="text-sm text-blue-700">Terakhir Selesai</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </PantiLayout>
    );
}
