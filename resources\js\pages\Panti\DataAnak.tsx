import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, Eye, Pencil, Plus, Trash2, X } from 'lucide-react';
import { ChangeEvent, FormEvent, useEffect, useState } from 'react';

interface Anak {
    id: number;
    nama_lengkap: string;
    tempat_lahir: string;
    tanggal_lahir: string;
    nik: string;
    usia: number;
    jenis_kelamin: string;
    pendidikan: string;
    status_anak: string;
    alasan_tidak_aktif?: string;
    foto_anak?: string;
    nama_ayah?: string;
    usia_ayah?: number;
    pekerjaan_ayah?: string;
    alamat_ayah?: string;
    nama_ibu?: string;
    usia_ibu?: number;
    pekerjaan_ibu?: string;
    alamat_ibu?: string;
    created_at: string;
    updated_at: string;
    tahap_pengangkatan?: string; // <--- Tambahan untuk progress bar
}

interface Pagination {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
    from: number;
    to: number;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    anaks: Anak[];
    pagination: Pagination;
}

interface FormState {
    [key: string]: string | number | File | undefined;
    nama_lengkap: string;
    tempat_lahir: string;
    tanggal_lahir: string;
    nik: string;
    usia: number;
    jenis_kelamin: string;
    pendidikan: string;
    status_anak: string;
    alasan_tidak_aktif: string;
    alasan_tidak_aktif_custom?: string;
    foto_anak?: File;
    nama_ayah: string;
    usia_ayah: number;
    pekerjaan_ayah: string;
    alamat_ayah: string;
    nama_ibu: string;
    usia_ibu: number;
    pekerjaan_ibu: string;
    alamat_ibu: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Data Anak',
        href: '/panti/dataanak',
    },
];

export default function DataAnak({
    user,
    anaks,
    pagination,
    filters = { status: 'all', search: '' },
}: Props & { filters?: { status?: string; search?: string } }) {
    const [showForm, setShowForm] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingId, setEditingId] = useState<number | null>(null);
    const [viewingAnak, setViewingAnak] = useState<Anak | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);
    const [filterStatus, setFilterStatus] = useState<string>(filters?.status || 'all');
    const [searchNama, setSearchNama] = useState<string>(filters?.search || '');
    const [form, setForm] = useState<FormState>({
        nama_lengkap: '',
        tempat_lahir: '',
        tanggal_lahir: '',
        nik: '',
        usia: 0,
        jenis_kelamin: '',
        pendidikan: '',
        status_anak: '',
        alasan_tidak_aktif: '',
        alasan_tidak_aktif_custom: '',
        nama_ayah: '',
        usia_ayah: 0,
        pekerjaan_ayah: '',
        alamat_ayah: '',
        nama_ibu: '',
        usia_ibu: 0,
        pekerjaan_ibu: '',
        alamat_ibu: '',
    });
    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [successMsg, setSuccessMsg] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const perPage = 10;
    const [dataAnak, setDataAnak] = useState(anaks);
    const totalPage = Math.ceil(dataAnak.length / perPage);
    const [progressPolling, setProgressPolling] = useState<NodeJS.Timeout | null>(null);

    // Sync data anak setiap kali props anaks berubah (misal setelah update)
    useEffect(() => {
        setDataAnak([...anaks]); // Force new array reference
        // Jika modal view sedang terbuka, update juga dengan data terbaru
        if (viewingAnak) {
            const latest = anaks.find((a) => a.id === viewingAnak.id);
            if (latest) setViewingAnak(latest);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [anaks]);

    // Helper function to get status badge with appropriate color
    const getStatusBadge = (status: string, alasan?: string) => {
        const normalizedStatus = status.toLowerCase();

        if (normalizedStatus === 'aktif') {
            return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Aktif</Badge>;
        } else if (normalizedStatus === 'proses pengangkatan') {
            return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">Proses Pengangkatan</Badge>;
        } else if (normalizedStatus === 'tidak aktif') {
            return (
                <div className="flex flex-col gap-1">
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Tidak Aktif</Badge>
                    {alasan && <span className="text-xs text-gray-600 italic">({alasan})</span>}
                </div>
            );
        } else {
            return <Badge variant="outline">{status}</Badge>;
        }
    };

    // Function to poll progress status for real-time updates
    const pollProgressStatus = async (anakId: number) => {
        try {
            const response = await fetch(`/panti/dataanak/${anakId}/progress`);
            if (response.ok) {
                const data = await response.json();

                // Update viewingAnak if it's the same child
                if (viewingAnak && viewingAnak.id === anakId) {
                    setViewingAnak((prev) =>
                        prev
                            ? {
                                  ...prev,
                                  tahap_pengangkatan: data.tahap_pengangkatan,
                                  status_anak: data.status_anak,
                                  updated_at: data.updated_at,
                              }
                            : null,
                    );
                }

                // Update dataAnak list
                setDataAnak((prev) =>
                    prev.map((anak) =>
                        anak.id === anakId
                            ? {
                                  ...anak,
                                  tahap_pengangkatan: data.tahap_pengangkatan,
                                  status_anak: data.status_anak,
                                  updated_at: data.updated_at,
                              }
                            : anak,
                    ),
                );
            }
        } catch (error) {
            console.error('Error polling progress status:', error);
        }
    };

    // Start polling when viewing a child with "proses pengangkatan" status
    useEffect(() => {
        if (viewingAnak && viewingAnak.status_anak.toLowerCase() === 'proses pengangkatan') {
            const interval = setInterval(() => {
                pollProgressStatus(viewingAnak.id);
            }, 5000); // Poll every 5 seconds

            setProgressPolling(interval);

            return () => {
                clearInterval(interval);
                setProgressPolling(null);
            };
        } else if (progressPolling) {
            clearInterval(progressPolling);
            setProgressPolling(null);
        }
    }, [viewingAnak]);

    // Cleanup polling on component unmount
    useEffect(() => {
        return () => {
            if (progressPolling) {
                clearInterval(progressPolling);
            }
        };
    }, [progressPolling]);

    // Data sudah difilter di backend, tinggal paginasi lokal jika perlu (opsional)
    const pagedData = dataAnak;

    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setForm({
            ...form,
            [name]: type === 'number' ? Number(value) : value,
        });
    };

    const handleSelectChange = (name: string, value: string) => {
        setForm({ ...form, [name]: value });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setForm({ ...form, foto_anak: e.target.files[0] });
        }
    };

    const handleTambahData = () => {
        setShowForm(true);
        setEditingId(null);
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleEditData = (anak: Anak) => {
        setForm({
            nama_lengkap: anak.nama_lengkap,
            tempat_lahir: anak.tempat_lahir,
            tanggal_lahir: anak.tanggal_lahir,
            nik: anak.nik,
            usia: anak.usia,
            jenis_kelamin: anak.jenis_kelamin,
            pendidikan: anak.pendidikan,
            status_anak: anak.status_anak,
            alasan_tidak_aktif: anak.alasan_tidak_aktif || '',
            alasan_tidak_aktif_custom: '',
            nama_ayah: anak.nama_ayah || '',
            usia_ayah: anak.usia_ayah || 0,
            pekerjaan_ayah: anak.pekerjaan_ayah || '',
            alamat_ayah: anak.alamat_ayah || '',
            nama_ibu: anak.nama_ibu || '',
            usia_ibu: anak.usia_ibu || 0,
            pekerjaan_ibu: anak.pekerjaan_ibu || '',
            alamat_ibu: anak.alamat_ibu || '',
        });
        setEditingId(anak.id);
        setShowEditModal(true);
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleViewData = (anak: Anak) => {
        // Ambil data anak terbaru dari state dataAnak (yang sudah terupdate)
        const latest = dataAnak.find((a) => a.id === anak.id);
        setViewingAnak(latest || anak);
    };

    const handleDeleteData = (id: number) => {
        setShowDeleteConfirm(id);
    };

    const confirmDelete = () => {
        if (showDeleteConfirm) {
            router.delete(`/panti/dataanak/${showDeleteConfirm}`, {
                onSuccess: (page: any) => {
                    setSuccessMsg('Data anak berhasil dihapus!');
                    setShowDeleteConfirm(null);
                    // Update data lokal dengan data terbaru dari response
                    if (page && page.props && page.props.anaks) {
                        setDataAnak(page.props.anaks);
                        // Force re-render dengan timeout kecil
                        setTimeout(() => {
                            setDataAnak([...page.props.anaks]);
                        }, 100);
                    }
                },
                onError: () => {
                    setErrorMsg('Gagal menghapus data anak.');
                    setShowDeleteConfirm(null);
                },
            });
        }
    };

    const handleBatal = () => {
        setShowForm(false);
        setEditingId(null);
        setForm({
            nama_lengkap: '',
            tempat_lahir: '',
            tanggal_lahir: '',
            nik: '',
            usia: 0,
            jenis_kelamin: '',
            pendidikan: '',
            status_anak: '',
            alasan_tidak_aktif: '',
            alasan_tidak_aktif_custom: '',
            nama_ayah: '',
            usia_ayah: 0,
            pekerjaan_ayah: '',
            alamat_ayah: '',
            nama_ibu: '',
            usia_ibu: 0,
            pekerjaan_ibu: '',
            alamat_ibu: '',
        });
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleBatalEdit = () => {
        setShowEditModal(false);
        setEditingId(null);
        setForm({
            nama_lengkap: '',
            tempat_lahir: '',
            tanggal_lahir: '',
            nik: '',
            usia: 0,
            jenis_kelamin: '',
            pendidikan: '',
            status_anak: 'aktif',
            alasan_tidak_aktif: '',
            alasan_tidak_aktif_custom: '',
            nama_ayah: '',
            usia_ayah: 0,
            pekerjaan_ayah: '',
            alamat_ayah: '',
            nama_ibu: '',
            usia_ibu: 0,
            pekerjaan_ibu: '',
            alamat_ibu: '',
        });
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleSubmitEdit = (e: FormEvent) => {
        e.preventDefault();

        if (!editingId) return;

        const formData = new FormData();
        Object.entries(form).forEach(([key, value]) => {
            formData.append(key, value?.toString() || '');
        });

        router.put(`/panti/dataanak/${editingId}`, formData, {
            forceFormData: true,
            preserveState: false,
            preserveScroll: true,
            onSuccess: (page: any) => {
                console.log('Update success, page response:', page);

                // Close modal and reset form first
                setShowEditModal(false);
                setEditingId(null);
                setErrorMsg(null);

                // Reset form
                setForm({
                    nama_lengkap: '',
                    tempat_lahir: '',
                    tanggal_lahir: '',
                    nik: '',
                    usia: 0,
                    jenis_kelamin: '',
                    pendidikan: '',
                    status_anak: 'aktif',
                    alasan_tidak_aktif: '',
                    alasan_tidak_aktif_custom: '',
                    nama_ayah: '',
                    usia_ayah: 0,
                    pekerjaan_ayah: '',
                    alamat_ayah: '',
                    nama_ibu: '',
                    usia_ibu: 0,
                    pekerjaan_ibu: '',
                    alamat_ibu: '',
                });

                setSuccessMsg('Data anak berhasil diperbarui!');
            },
            onFinish: () => {
                // This runs after the page has been updated by Inertia
                console.log('Request finished, forcing data refresh');

                // Force reload data to ensure fresh data
                router.reload({
                    only: ['anaks', 'pagination'],
                    onSuccess: (page: any) => {
                        console.log('Data reloaded:', page.props.anaks);
                        if (page.props.anaks) {
                            setDataAnak([...page.props.anaks]);
                        }
                    },
                });
            },
            onError: (errors: any) => {
                console.error('Form errors:', errors);
                if (errors && typeof errors === 'object') {
                    const errorMessages = Object.values(errors).flat().join(', ');
                    setErrorMsg(`Gagal memperbarui data: ${errorMessages}`);
                } else {
                    setErrorMsg('Gagal memperbarui data. Pastikan semua field wajib sudah diisi dengan benar.');
                }
            },
        });
    };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorMsg(null);
        setSuccessMsg(null);

        const formData = new FormData();
        Object.keys(form).forEach((key) => {
            const value = form[key];
            if (value !== undefined && value !== '') {
                if (value instanceof File) {
                    formData.append(key, value);
                } else if (key === 'alasan_tidak_aktif') {
                    // Handle custom reason
                    if (value === 'Lainnya' && form.alasan_tidak_aktif_custom) {
                        formData.append(key, String(form.alasan_tidak_aktif_custom));
                    } else if (value !== 'Lainnya') {
                        formData.append(key, String(value));
                    }
                } else if (key !== 'alasan_tidak_aktif_custom') {
                    // Skip the custom field as it's handled above
                    formData.append(key, String(value));
                }
            }
        });

        if (editingId) {
            // Update existing data
            router.put(`/panti/dataanak/${editingId}`, formData, {
                forceFormData: true,
                onSuccess: (page: any) => {
                    setShowForm(false);
                    setSuccessMsg('Data anak berhasil diperbarui!');
                    handleBatal();
                    // Update data lokal dengan data terbaru dari response
                    if (page && page.props && page.props.anaks) {
                        setDataAnak(page.props.anaks);
                        // Force re-render dengan timeout kecil
                        setTimeout(() => {
                            setDataAnak([...page.props.anaks]);
                        }, 100);
                    }
                },
                onError: (errors: any) => {
                    console.error('Form errors:', errors);
                    if (errors && typeof errors === 'object') {
                        const errorMessages = Object.values(errors).flat().join(', ');
                        setErrorMsg(`Gagal memperbarui data: ${errorMessages}`);
                    } else {
                        setErrorMsg('Gagal memperbarui data. Pastikan semua field wajib sudah diisi dengan benar.');
                    }
                },
            });
        } else {
            // Create new data
            router.post('/panti/dataanak', formData, {
                forceFormData: true,
                onSuccess: (page: any) => {
                    setShowForm(false);
                    setSuccessMsg('Data anak berhasil ditambahkan!');
                    handleBatal();
                    // Update data lokal dengan data terbaru dari response
                    if (page && page.props && page.props.anaks) {
                        setDataAnak(page.props.anaks);
                        // Force re-render dengan timeout kecil
                        setTimeout(() => {
                            setDataAnak([...page.props.anaks]);
                        }, 100);
                    }
                },
                onError: (errors: any) => {
                    console.error('Form errors:', errors);
                    // Display specific validation errors if available
                    if (errors && typeof errors === 'object') {
                        const errorMessages = Object.values(errors).flat().join(', ');
                        setErrorMsg(`Gagal menyimpan data: ${errorMessages}`);
                    } else {
                        setErrorMsg('Gagal menyimpan data. Pastikan semua field wajib sudah diisi dengan benar.');
                    }
                },
            });
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID');
    };

    // Komponen Progress sederhana
    function Progress({ value }: { value: number }) {
        return (
            <div className="h-2 w-full rounded-full bg-gray-200">
                <div className="h-2 rounded-full bg-blue-500 transition-all" style={{ width: `${value}%` }} />
            </div>
        );
    }

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Anak Asuh" />
            <div className="flex flex-col gap-6 p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <h1 className="text-2xl font-bold tracking-tight text-blue-900">Data Anak Asuh</h1>
                    <div className="flex flex-col gap-2 md:flex-row md:items-center">
                        {/* Search Bar Nama Anak */}
                        <Input
                            type="text"
                            placeholder="Cari nama anak..."
                            value={searchNama}
                            onChange={(e) => {
                                const value = e.target.value;
                                setSearchNama(value);
                                setCurrentPage(1);
                                // Kirim ke backend
                                router.get('/panti/dataanak', { status: filterStatus, search: value }, { preserveState: true, replace: true });
                            }}
                            className="w-48 border-blue-200"
                        />
                        {/* Filter Status Anak */}
                        <Select
                            value={filterStatus}
                            onValueChange={(value) => {
                                setFilterStatus(value);
                                setCurrentPage(1);
                                // Kirim ke backend
                                router.get('/panti/dataanak', { status: value, search: searchNama }, { preserveState: true, replace: true });
                            }}
                        >
                            <SelectTrigger className="w-48 border-blue-200">
                                <SelectValue placeholder="Filter status anak" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Semua Status</SelectItem>
                                <SelectItem value="aktif">Aktif</SelectItem>
                                <SelectItem value="proses pengangkatan">Proses Pengangkatan</SelectItem>
                                <SelectItem value="tidak aktif">Tidak Aktif</SelectItem>
                            </SelectContent>
                        </Select>
                        {!showForm && (
                            <Button variant="default" size="sm" className="gap-2 bg-blue-600 text-white hover:bg-blue-700" onClick={handleTambahData}>
                                <Plus className="h-4 w-4" />
                                Tambah Data Anak
                            </Button>
                        )}
                    </div>
                </div>

                {/* Form Tambah Data Anak */}
                {showForm && (
                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900">{editingId ? 'Form Edit Data Anak' : 'Form Tambah Data Anak'}</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {errorMsg && (
                                <div className="mb-4 rounded border border-red-200 bg-red-100 px-4 py-2 text-sm text-red-700">{errorMsg}</div>
                            )}
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Identitas Diri */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Identitas Diri</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="nama_lengkap" className="text-blue-800">
                                                Nama Lengkap *
                                            </Label>
                                            <Input
                                                id="nama_lengkap"
                                                name="nama_lengkap"
                                                value={form.nama_lengkap}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="tempat_lahir" className="text-blue-800">
                                                Tempat Lahir *
                                            </Label>
                                            <Input
                                                id="tempat_lahir"
                                                name="tempat_lahir"
                                                value={form.tempat_lahir}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="tanggal_lahir" className="text-blue-800">
                                                Tanggal Lahir *
                                            </Label>
                                            <Input
                                                id="tanggal_lahir"
                                                name="tanggal_lahir"
                                                type="date"
                                                value={form.tanggal_lahir}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="nik" className="text-blue-800">
                                                NIK *
                                            </Label>
                                            <Input
                                                id="nik"
                                                name="nik"
                                                value={form.nik}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                maxLength={16}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="usia" className="text-blue-800">
                                                Usia *
                                            </Label>
                                            <Input
                                                id="usia"
                                                name="usia"
                                                type="number"
                                                value={form.usia}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                min="0"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="jenis_kelamin" className="text-blue-800">
                                                Jenis Kelamin *
                                            </Label>
                                            <Select
                                                value={form.jenis_kelamin}
                                                onValueChange={(value) => handleSelectChange('jenis_kelamin', value)}
                                                required
                                            >
                                                <SelectTrigger className="border-blue-200">
                                                    <SelectValue placeholder="Pilih jenis kelamin" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="laki-laki">Laki-laki</SelectItem>
                                                    <SelectItem value="perempuan">Perempuan</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div>
                                            <Label htmlFor="pendidikan" className="text-blue-800">
                                                Pendidikan *
                                            </Label>
                                            <Select
                                                value={form.pendidikan}
                                                onValueChange={(value) => handleSelectChange('pendidikan', value)}
                                                required
                                            >
                                                <SelectTrigger className="border-blue-200">
                                                    <SelectValue placeholder="Pilih pendidikan" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="Belum sekolah">Belum sekolah</SelectItem>
                                                    <SelectItem value="TK">TK</SelectItem>
                                                    <SelectItem value="SD">SD</SelectItem>
                                                    <SelectItem value="SMP">SMP</SelectItem>
                                                    <SelectItem value="SMA">SMA</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div>
                                            <Label htmlFor="status_anak" className="text-blue-800">
                                                Status Anak *
                                            </Label>
                                            <Select
                                                value={form.status_anak}
                                                onValueChange={(value) => handleSelectChange('status_anak', value)}
                                                required
                                            >
                                                <SelectTrigger className="border-blue-200">
                                                    <SelectValue placeholder="Pilih status anak" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="aktif">Aktif</SelectItem>
                                                    <SelectItem value="proses pengangkatan">Proses Pengangkatan</SelectItem>
                                                    <SelectItem value="tidak aktif">Tidak Aktif</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        {form.status_anak === 'tidak aktif' && (
                                            <>
                                                <div>
                                                    <Label htmlFor="alasan_tidak_aktif" className="text-blue-800">
                                                        Alasan Tidak Aktif *
                                                    </Label>
                                                    <Select
                                                        value={form.alasan_tidak_aktif}
                                                        onValueChange={(value) => handleSelectChange('alasan_tidak_aktif', value)}
                                                        required
                                                    >
                                                        <SelectTrigger className="border-blue-200">
                                                            <SelectValue placeholder="Pilih alasan tidak aktif" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="Anak sudah di tingkat perguruan tinggi">
                                                                Anak sudah di tingkat perguruan tinggi
                                                            </SelectItem>
                                                            <SelectItem value="Anak sudah diambil kembali oleh keluarga">
                                                                Anak sudah diambil kembali oleh keluarga
                                                            </SelectItem>
                                                            <SelectItem value="Anak sudah bekerja">Anak sudah bekerja</SelectItem>
                                                            <SelectItem value="Lainnya">Lainnya</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </div>
                                                {form.alasan_tidak_aktif === 'Lainnya' && (
                                                    <div>
                                                        <Label htmlFor="alasan_tidak_aktif_custom" className="text-blue-800">
                                                            Alasan Lainnya *
                                                        </Label>
                                                        <Textarea
                                                            id="alasan_tidak_aktif_custom"
                                                            name="alasan_tidak_aktif_custom"
                                                            placeholder="Masukkan alasan lainnya..."
                                                            value={form.alasan_tidak_aktif_custom || ''}
                                                            onChange={handleInputChange}
                                                            required
                                                            className="border-blue-200"
                                                        />
                                                    </div>
                                                )}
                                            </>
                                        )}
                                        <div className="md:col-span-2">
                                            <Label htmlFor="foto_anak" className="text-blue-800">
                                                Foto Anak
                                            </Label>
                                            <Input
                                                id="foto_anak"
                                                name="foto_anak"
                                                type="file"
                                                accept="image/*"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Identitas Orang Tua */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Identitas Orang Tua</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="nama_ayah" className="text-blue-800">
                                                Nama Ayah
                                            </Label>
                                            <Input
                                                id="nama_ayah"
                                                name="nama_ayah"
                                                value={form.nama_ayah}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="usia_ayah" className="text-blue-800">
                                                Usia Ayah
                                            </Label>
                                            <Input
                                                id="usia_ayah"
                                                name="usia_ayah"
                                                type="number"
                                                value={form.usia_ayah}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                min="0"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="pekerjaan_ayah" className="text-blue-800">
                                                Pekerjaan Ayah
                                            </Label>
                                            <Input
                                                id="pekerjaan_ayah"
                                                name="pekerjaan_ayah"
                                                value={form.pekerjaan_ayah}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="alamat_ayah" className="text-blue-800">
                                                Alamat Ayah
                                            </Label>
                                            <Textarea
                                                id="alamat_ayah"
                                                name="alamat_ayah"
                                                value={form.alamat_ayah}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                rows={2}
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="nama_ibu" className="text-blue-800">
                                                Nama Ibu
                                            </Label>
                                            <Input
                                                id="nama_ibu"
                                                name="nama_ibu"
                                                value={form.nama_ibu}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="usia_ibu" className="text-blue-800">
                                                Usia Ibu
                                            </Label>
                                            <Input
                                                id="usia_ibu"
                                                name="usia_ibu"
                                                type="number"
                                                value={form.usia_ibu}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                min="0"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="pekerjaan_ibu" className="text-blue-800">
                                                Pekerjaan Ibu
                                            </Label>
                                            <Input
                                                id="pekerjaan_ibu"
                                                name="pekerjaan_ibu"
                                                value={form.pekerjaan_ibu}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="alamat_ibu" className="text-blue-800">
                                                Alamat Ibu
                                            </Label>
                                            <Textarea
                                                id="alamat_ibu"
                                                name="alamat_ibu"
                                                value={form.alamat_ibu}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                rows={2}
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-end gap-2">
                                    <Button type="button" variant="outline" onClick={handleBatal} className="border-blue-200">
                                        Batal
                                    </Button>
                                    <Button type="submit" className="bg-blue-600 text-white hover:bg-blue-700">
                                        {editingId ? 'Perbarui Data' : 'Simpan Data'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}

                {/* Success Message */}
                {successMsg && <div className="rounded border border-green-200 bg-green-100 px-4 py-2 text-sm text-green-700">{successMsg}</div>}

                {/* Tabel Data Anak */}
                <Card className="border-blue-200">
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="text-blue-800">No</TableHead>
                                    <TableHead className="text-blue-800">Nama Lengkap</TableHead>
                                    <TableHead className="text-blue-800">NIK</TableHead>
                                    <TableHead className="text-blue-800">Usia</TableHead>
                                    <TableHead className="text-blue-800">Jenis Kelamin</TableHead>
                                    <TableHead className="text-blue-800">Status</TableHead>
                                    <TableHead className="text-blue-800">Tanggal Lahir</TableHead>
                                    <TableHead className="text-blue-800">Aksi</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {dataAnak.length > 0 ? (
                                    dataAnak.map((anak, index) => (
                                        <TableRow key={anak.id}>
                                            <TableCell>{(pagination.current_page - 1) * pagination.per_page + index + 1}</TableCell>
                                            <TableCell className="font-medium">{anak.nama_lengkap}</TableCell>
                                            <TableCell>{anak.nik}</TableCell>
                                            <TableCell>{anak.usia} tahun</TableCell>
                                            <TableCell className="capitalize">{anak.jenis_kelamin}</TableCell>
                                            <TableCell>{getStatusBadge(anak.status_anak, anak.alasan_tidak_aktif)}</TableCell>
                                            <TableCell>{formatDate(anak.tanggal_lahir)}</TableCell>
                                            <TableCell>
                                                <div className="flex gap-2">
                                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleViewData(anak)}>
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleEditData(anak)}>
                                                        <Pencil className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        className="h-8 w-8 p-0 text-red-600"
                                                        onClick={() => handleDeleteData(anak.id)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={8} className="text-center text-gray-500">
                                            Belum ada data anak. Klik tombol "Tambah Data Anak" untuk menambah data.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {pagination && (
                            <div className="flex items-center justify-between pt-4">
                                <p className="text-sm text-gray-700">
                                    Menampilkan {pagination.from || 0} - {pagination.to || 0} dari {pagination.total} data
                                </p>
                                <div className="flex space-x-2">
                                    {pagination.current_page > 1 && (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page - 1}`}>
                                                <ChevronLeft className="h-4 w-4" />
                                                Previous
                                            </a>
                                        </Button>
                                    )}
                                    {pagination.current_page < pagination.last_page && (
                                        <Button variant="outline" size="sm" asChild>
                                            <a href={`?page=${pagination.current_page + 1}`}>
                                                Next
                                                <ChevronRight className="h-4 w-4" />
                                            </a>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Modal View Detail */}
                {viewingAnak && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 max-h-[90vh] w-full max-w-4xl overflow-y-auto border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-xl font-bold text-blue-900">Detail Data Anak</CardTitle>
                                <Button variant="ghost" size="sm" onClick={() => setViewingAnak(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Progress Proses Pengangkatan */}
                                {viewingAnak.status_anak.toLowerCase() === 'proses pengangkatan' &&
                                    (() => {
                                        // Field dummy tahap_pengangkatan, backend isi sesuai status COTA
                                        const tahap = viewingAnak.tahap_pengangkatan || 'menunggu_verifikasi';
                                        const steps = [
                                            { key: 'menunggu_verifikasi', label: 'Pengajuan (Menunggu Verifikasi Berkas)' },
                                            { key: 'proses_verifikasi', label: 'Verifikasi Berkas oleh Dinsos' },
                                            { key: 'sidang', label: 'Sidang Pengangkatan' },
                                            { key: 'selesai', label: 'Selesai' },
                                        ];
                                        const currentIdx = steps.findIndex((s) => s.key === tahap);
                                        return (
                                            <div className="mb-4">
                                                <Label className="font-medium text-blue-700">Progress Proses Pengangkatan</Label>
                                                <Progress value={((currentIdx + 1) / steps.length) * 100} />
                                                <div className="mt-1 flex justify-between text-xs text-gray-600">
                                                    {steps.map((step, idx) => (
                                                        <span key={step.key} className={idx === currentIdx ? 'font-bold text-blue-700' : ''}>
                                                            {step.label}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>
                                        );
                                    })()}
                                {/* Identitas Diri */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Identitas Diri</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label className="font-medium text-blue-700">Nama Lengkap</Label>
                                            <p className="text-gray-900">{viewingAnak.nama_lengkap}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">NIK</Label>
                                            <p className="text-gray-900">{viewingAnak.nik}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Tempat Lahir</Label>
                                            <p className="text-gray-900">{viewingAnak.tempat_lahir}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Tanggal Lahir</Label>
                                            <p className="text-gray-900">{formatDate(viewingAnak.tanggal_lahir)}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Usia</Label>
                                            <p className="text-gray-900">{viewingAnak.usia} tahun</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Jenis Kelamin</Label>
                                            <p className="text-gray-900 capitalize">{viewingAnak.jenis_kelamin}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Pendidikan</Label>
                                            <p className="text-gray-900">{viewingAnak.pendidikan}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Status Anak</Label>
                                            <div className="mt-1">{getStatusBadge(viewingAnak.status_anak, viewingAnak.alasan_tidak_aktif)}</div>
                                        </div>
                                        {viewingAnak.foto_anak && (
                                            <div className="md:col-span-2">
                                                <Label className="font-medium text-blue-700">Foto Anak</Label>
                                                <div className="mt-2">
                                                    <img
                                                        src={`/storage/${viewingAnak.foto_anak}`}
                                                        alt="Foto anak"
                                                        className="h-32 w-32 rounded-lg border object-cover"
                                                    />
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Identitas Orang Tua */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Identitas Orang Tua</h3>
                                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                        {/* Data Ayah */}
                                        <div>
                                            <h4 className="mb-3 font-medium text-blue-700">Data Ayah</h4>
                                            <div className="space-y-2">
                                                <div>
                                                    <Label className="text-sm text-blue-600">Nama</Label>
                                                    <p className="text-gray-900">{viewingAnak.nama_ayah || '-'}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-sm text-blue-600">Usia</Label>
                                                    <p className="text-gray-900">{viewingAnak.usia_ayah ? `${viewingAnak.usia_ayah} tahun` : '-'}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-sm text-blue-600">Pekerjaan</Label>
                                                    <p className="text-gray-900">{viewingAnak.pekerjaan_ayah || '-'}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-sm text-blue-600">Alamat</Label>
                                                    <p className="text-gray-900">{viewingAnak.alamat_ayah || '-'}</p>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Data Ibu */}
                                        <div>
                                            <h4 className="mb-3 font-medium text-blue-700">Data Ibu</h4>
                                            <div className="space-y-2">
                                                <div>
                                                    <Label className="text-sm text-blue-600">Nama</Label>
                                                    <p className="text-gray-900">{viewingAnak.nama_ibu || '-'}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-sm text-blue-600">Usia</Label>
                                                    <p className="text-gray-900">{viewingAnak.usia_ibu ? `${viewingAnak.usia_ibu} tahun` : '-'}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-sm text-blue-600">Pekerjaan</Label>
                                                    <p className="text-gray-900">{viewingAnak.pekerjaan_ibu || '-'}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-sm text-blue-600">Alamat</Label>
                                                    <p className="text-gray-900">{viewingAnak.alamat_ibu || '-'}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Konfirmasi Hapus */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-red-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader>
                                <CardTitle className="text-xl font-bold text-red-600">Konfirmasi Hapus</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-700">Apakah Anda yakin ingin menghapus data anak ini? Tindakan ini tidak dapat dibatalkan.</p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                                        Batal
                                    </Button>
                                    <Button variant="destructive" onClick={confirmDelete}>
                                        Hapus
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Edit Data Anak */}
                <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                    <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle className="text-blue-900">Edit Data Anak</DialogTitle>
                        </DialogHeader>

                        {errorMsg && <div className="mb-4 rounded border border-red-200 bg-red-100 px-4 py-2 text-sm text-red-700">{errorMsg}</div>}

                        <form onSubmit={handleSubmitEdit} className="space-y-6">
                            {/* Identitas Diri */}
                            <div>
                                <h3 className="mb-4 text-lg font-semibold text-blue-800">Identitas Diri</h3>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="edit_nama_lengkap" className="text-blue-800">
                                            Nama Lengkap *
                                        </Label>
                                        <Input
                                            id="edit_nama_lengkap"
                                            name="nama_lengkap"
                                            value={form.nama_lengkap}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_tempat_lahir" className="text-blue-800">
                                            Tempat Lahir *
                                        </Label>
                                        <Input
                                            id="edit_tempat_lahir"
                                            name="tempat_lahir"
                                            value={form.tempat_lahir}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_tanggal_lahir" className="text-blue-800">
                                            Tanggal Lahir *
                                        </Label>
                                        <Input
                                            id="edit_tanggal_lahir"
                                            name="tanggal_lahir"
                                            type="date"
                                            value={form.tanggal_lahir}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_nik" className="text-blue-800">
                                            NIK *
                                        </Label>
                                        <Input
                                            id="edit_nik"
                                            name="nik"
                                            value={form.nik}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_usia" className="text-blue-800">
                                            Usia *
                                        </Label>
                                        <Input
                                            id="edit_usia"
                                            name="usia"
                                            type="number"
                                            value={form.usia}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_jenis_kelamin" className="text-blue-800">
                                            Jenis Kelamin *
                                        </Label>
                                        <Select
                                            name="jenis_kelamin"
                                            value={form.jenis_kelamin}
                                            onValueChange={(value) => handleSelectChange('jenis_kelamin', value)}
                                        >
                                            <SelectTrigger className="border-blue-200">
                                                <SelectValue placeholder="Pilih jenis kelamin" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="laki-laki">Laki-laki</SelectItem>
                                                <SelectItem value="perempuan">Perempuan</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_pendidikan" className="text-blue-800">
                                            Pendidikan *
                                        </Label>
                                        <Select
                                            name="pendidikan"
                                            value={form.pendidikan}
                                            onValueChange={(value) => handleSelectChange('pendidikan', value)}
                                        >
                                            <SelectTrigger className="border-blue-200">
                                                <SelectValue placeholder="Pilih pendidikan" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="Belum Sekolah">Belum Sekolah</SelectItem>
                                                <SelectItem value="TK">TK</SelectItem>
                                                <SelectItem value="SD">SD</SelectItem>
                                                <SelectItem value="SMP">SMP</SelectItem>
                                                <SelectItem value="SMA">SMA</SelectItem>
                                                <SelectItem value="Perguruan Tinggi">Perguruan Tinggi</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="edit_status_anak" className="text-blue-800">
                                            Status Anak *
                                        </Label>
                                        <Select
                                            name="status_anak"
                                            value={form.status_anak}
                                            onValueChange={(value) => handleSelectChange('status_anak', value)}
                                        >
                                            <SelectTrigger className="border-blue-200">
                                                <SelectValue placeholder="Pilih status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="aktif">Aktif</SelectItem>
                                                <SelectItem value="proses pengangkatan">Proses Pengangkatan</SelectItem>
                                                <SelectItem value="tidak aktif">Tidak Aktif</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </div>

                            {/* Data Orang Tua */}
                            <div>
                                <h3 className="mb-4 text-lg font-semibold text-blue-800">Data Orang Tua</h3>
                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                                    {/* Data Ayah */}
                                    <div>
                                        <h4 className="text-md mb-3 font-medium text-blue-700">Data Ayah</h4>
                                        <div className="space-y-3">
                                            <div>
                                                <Label htmlFor="edit_nama_ayah" className="text-blue-800">
                                                    Nama Ayah
                                                </Label>
                                                <Input
                                                    id="edit_nama_ayah"
                                                    name="nama_ayah"
                                                    value={form.nama_ayah}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="edit_usia_ayah" className="text-blue-800">
                                                    Usia Ayah
                                                </Label>
                                                <Input
                                                    id="edit_usia_ayah"
                                                    name="usia_ayah"
                                                    type="number"
                                                    value={form.usia_ayah}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="edit_pekerjaan_ayah" className="text-blue-800">
                                                    Pekerjaan Ayah
                                                </Label>
                                                <Input
                                                    id="edit_pekerjaan_ayah"
                                                    name="pekerjaan_ayah"
                                                    value={form.pekerjaan_ayah}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="edit_alamat_ayah" className="text-blue-800">
                                                    Alamat Ayah
                                                </Label>
                                                <Textarea
                                                    id="edit_alamat_ayah"
                                                    name="alamat_ayah"
                                                    value={form.alamat_ayah}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                    rows={3}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {/* Data Ibu */}
                                    <div>
                                        <h4 className="text-md mb-3 font-medium text-blue-700">Data Ibu</h4>
                                        <div className="space-y-3">
                                            <div>
                                                <Label htmlFor="edit_nama_ibu" className="text-blue-800">
                                                    Nama Ibu
                                                </Label>
                                                <Input
                                                    id="edit_nama_ibu"
                                                    name="nama_ibu"
                                                    value={form.nama_ibu}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="edit_usia_ibu" className="text-blue-800">
                                                    Usia Ibu
                                                </Label>
                                                <Input
                                                    id="edit_usia_ibu"
                                                    name="usia_ibu"
                                                    type="number"
                                                    value={form.usia_ibu}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="edit_pekerjaan_ibu" className="text-blue-800">
                                                    Pekerjaan Ibu
                                                </Label>
                                                <Input
                                                    id="edit_pekerjaan_ibu"
                                                    name="pekerjaan_ibu"
                                                    value={form.pekerjaan_ibu}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                />
                                            </div>
                                            <div>
                                                <Label htmlFor="edit_alamat_ibu" className="text-blue-800">
                                                    Alamat Ibu
                                                </Label>
                                                <Textarea
                                                    id="edit_alamat_ibu"
                                                    name="alamat_ibu"
                                                    value={form.alamat_ibu}
                                                    onChange={handleInputChange}
                                                    className="border-blue-200"
                                                    rows={3}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Buttons */}
                            <div className="flex justify-end gap-3 pt-4">
                                <Button type="button" variant="outline" onClick={handleBatalEdit}>
                                    Batal
                                </Button>
                                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                                    Perbarui Data
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </PantiLayout>
    );
}
