import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Eye, Pencil, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Data Anak',
        href: '/panti/dataanak',
    },
];

export default function DataAnak({ user }: Props) {
    // Dummy data anak asuh (20 data)
    const dataAnak = Array.from({ length: 20 }, (_, i) => ({
        id: i + 1,
        nama: `Anak <PERSON> ${i + 1}`,
        usia: 10 + (i % 7),
        jenis<PERSON><PERSON><PERSON>: i % 2 === 0 ? 'Laki-laki' : 'Perempuan',
        status: i % 3 === 0 ? 'aktif' : i % 3 === 1 ? 'proses pengangkatan' : 'tidak aktif',
    }));
    const [currentPage, setCurrentPage] = useState(1);
    const perPage = 10;
    const totalPage = Math.ceil(dataAnak.length / perPage);
    const pagedData = dataAnak.slice((currentPage - 1) * perPage, currentPage * perPage);
    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Anak Asuh" />
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight">Data Anak Asuh</h1>
                    <Button variant="default" size="sm" className="gap-2 bg-blue-600 text-white hover:bg-blue-700">
                        <Plus className="h-4 w-4" />
                        Tambah Data Anak
                    </Button>
                </div>
                <Card>
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-12 text-center">No</TableHead>
                                    <TableHead>Nama</TableHead>
                                    <TableHead>Usia</TableHead>
                                    <TableHead>Jenis Kelamin</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="w-40 text-center">Aksi</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {pagedData.map((anak, idx) => (
                                    <TableRow key={anak.id}>
                                        <TableCell className="text-center">{(currentPage - 1) * perPage + idx + 1}</TableCell>
                                        <TableCell>{anak.nama}</TableCell>
                                        <TableCell>{anak.usia} tahun</TableCell>
                                        <TableCell>{anak.jenisKelamin}</TableCell>
                                        <TableCell>
                                            <span
                                                className={
                                                    anak.status === 'aktif'
                                                        ? 'rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-600'
                                                        : anak.status === 'proses pengangkatan'
                                                          ? 'rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800'
                                                          : 'rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-700'
                                                }
                                            >
                                                {anak.status.charAt(0).toUpperCase() + anak.status.slice(1)}
                                            </span>
                                        </TableCell>
                                        <TableCell className="flex justify-center gap-2">
                                            <Button
                                                size="icon"
                                                variant="outline"
                                                className="h-8 w-8 border-blue-500 text-blue-600 hover:bg-blue-50"
                                                title="Edit"
                                            >
                                                <Pencil className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                size="icon"
                                                variant="outline"
                                                className="h-8 w-8 border-blue-500 text-blue-600 hover:bg-blue-50"
                                                title="Lihat"
                                            >
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                            <Button size="icon" variant="destructive" className="h-8 w-8" title="Hapus">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                        {/* Pagination */}
                        {totalPage > 1 && (
                            <div className="flex items-center justify-end gap-2 rounded-b-xl border-t bg-muted/50 p-4">
                                <Button
                                    size="sm"
                                    variant="outline"
                                    className="border-blue-500 text-blue-600 hover:bg-blue-50"
                                    disabled={currentPage === 1}
                                    onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                                >
                                    Prev
                                </Button>
                                {Array.from({ length: totalPage }, (_, i) => (
                                    <Button
                                        key={i}
                                        size="sm"
                                        variant={currentPage === i + 1 ? 'default' : 'outline'}
                                        className={
                                            currentPage === i + 1
                                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                                : 'border-blue-500 text-blue-600 hover:bg-blue-50'
                                        }
                                        onClick={() => setCurrentPage(i + 1)}
                                    >
                                        {i + 1}
                                    </Button>
                                ))}
                                <Button
                                    size="sm"
                                    variant="outline"
                                    className="border-blue-500 text-blue-600 hover:bg-blue-50"
                                    disabled={currentPage === totalPage}
                                    onClick={() => setCurrentPage((p) => Math.min(totalPage, p + 1))}
                                >
                                    Next
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </PantiLayout>
    );
}
