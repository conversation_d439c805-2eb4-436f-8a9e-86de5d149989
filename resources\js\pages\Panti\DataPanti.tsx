import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { FileText } from 'lucide-react';
import { ChangeEvent, FormEvent, useEffect, useState } from 'react';

const dokumenList = [
    'NPWP',
    'Akta Pendirian Lembaga',
    'Surat Permohonan Pendirian Panti',
    'SK Pengurus',
    'Struktur Organisasi',
    'Dokumen Perizinan Lokasi',
    'Surat Rekomendasi RT/RW/<PERSON>an',
    '<PERSON><PERSON><PERSON>',
    'Foto Bang<PERSON>n dan <PERSON>',
    'Daftar Inventaris',
    'Dokumen Pengurus/<PERSON>',
    'Laporan Kegiatan Terakhir',
    'Laporan <PERSON>uang<PERSON>',
];

// Daftar Kabupaten/Kota di Provinsi Riau
const kabupatenRiau = [
    'Pekanbaru',
    'Dumai',
    'Bengkalis',
    'Indragiri Hilir',
    'Indragiri Hulu',
    'Kampar',
    'Kepulauan Meranti',
    'Kuantan Singingi',
    'Pelalawan',
    'Rokan Hilir',
    'Rokan Hulu',
    'Siak',
];

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Data Panti',
        href: '/panti/datapanti',
    },
];

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    panti?: {
        id: number;
        nama: string;
        alamat: string;
        kabupaten?: string;
        telepon: string;
        email: string;
        pimpinan: string;
        npwp: string;
        status: string;
    };
    documents?: Record<
        string,
        {
            id: number;
            nama_dokumen: string;
            file_path: string;
            status: string;
        }
    >;
}

interface FormState {
    [key: string]: string;
    nama: string;
    alamat: string;
    kabupaten: string;
    telp: string;
    email: string;
    pimpinan: string;
    npwp: string;
}

export default function DataPanti({ user, panti, documents = {} }: Props) {
    const [editMode, setEditMode] = useState(false);
    const [form, setForm] = useState<FormState>({
        nama: panti?.nama || '',
        alamat: panti?.alamat || '',
        kabupaten: panti?.kabupaten || '',
        telp: panti?.telepon || '',
        email: panti?.email || '',
        pimpinan: panti?.pimpinan || '',
        npwp: panti?.npwp || '',
    });
    const [uploading, setUploading] = useState<Record<string, boolean>>({});
    const [files, setFiles] = useState<Record<string, File | undefined>>({});
    const [uploadedFiles, setUploadedFiles] = useState<Record<string, string>>(
        Object.fromEntries(Object.entries(documents).map(([key, doc]) => [key, doc.file_path])),
    );
    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [successMsg, setSuccessMsg] = useState<string | null>(null);

    // Update uploadedFiles when documents prop changes
    useEffect(() => {
        setUploadedFiles(Object.fromEntries(Object.entries(documents).map(([key, doc]) => [key, doc.file_path])));
    }, [documents]);

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const handleSelectChange = (name: string, value: string) => {
        setForm({ ...form, [name]: value });
    };

    const handleTambahData = () => {
        console.log('Tombol Tambah Data diklik');
        setEditMode(true);
        console.log('Edit mode set to:', true);
    };

    const handleBatal = () => {
        setEditMode(false);
        // Clear any selected files that haven't been uploaded
        setFiles({});
        // Reset file inputs
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach((input: any) => {
            input.value = '';
        });
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleSimpan = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorMsg(null);
        setSuccessMsg(null);
        router.post('/panti/datapanti', form, {
            onSuccess: () => {
                setEditMode(false);
                setSuccessMsg('Data berhasil disimpan!');
                // Refresh to get updated panti data
                router.reload({ only: ['panti'] });
            },
            onError: (errors: any) => {
                setErrorMsg('Gagal menyimpan data. Pastikan semua field sudah benar.');
            },
            onFinish: () => {
                // Optional: bisa reset loading state di sini jika ada
            },
        });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>, docName: string) => {
        if (e.target.files && e.target.files[0]) {
            setFiles({ ...files, [docName]: e.target.files[0] });
        }
    };

    const handleRemoveNewFile = (docName: string) => {
        setFiles({ ...files, [docName]: undefined });
        // Reset the file input
        const fileInput = document.querySelector(`input[type="file"][data-doc="${docName}"]`) as HTMLInputElement;
        if (fileInput) {
            fileInput.value = '';
        }
    };

    const handleUpload = (docName: string) => {
        if (!files[docName]) return;
        const data = new FormData();
        data.append('nama_dokumen', docName);
        data.append('file', files[docName] as File);
        setUploading((prev) => ({ ...prev, [docName]: true }));
        router.post('/panti/datapanti/upload', data, {
            forceFormData: true,
            onSuccess: () => {
                setSuccessMsg(`Dokumen ${docName} berhasil diupload!`);
                // Clear the file input for this document
                setFiles((prev) => ({ ...prev, [docName]: undefined }));
                // Refresh the page to get updated document data
                router.reload({ only: ['documents'] });
            },
            onError: (errors) => {
                setErrorMsg('Gagal mengupload dokumen. Pastikan file sesuai format yang diizinkan.');
            },
            onFinish: () => setUploading((prev) => ({ ...prev, [docName]: false })),
        });
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Panti" />
            <div className="w-full px-4 py-8 md:px-12 lg:px-24">
                {/* Header */}
                <div className="mb-6 flex items-center justify-between">
                    <h1 className="text-2xl font-bold text-blue-900">Identitas Panti</h1>
                    {!editMode && (
                        <Button className="bg-blue-600 text-white" onClick={handleTambahData}>
                            Tambah Data
                        </Button>
                    )}
                </div>

                {/* Form Identitas */}
                <Card className="mb-8 w-full border-blue-200">
                    <CardHeader>
                        <CardTitle className="text-blue-900">Form Identitas Panti</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {errorMsg && <div className="mb-2 rounded border border-red-200 bg-red-100 px-4 py-2 text-sm text-red-700">{errorMsg}</div>}
                        {successMsg && (
                            <div className="mb-2 rounded border border-green-200 bg-green-100 px-4 py-2 text-sm text-green-700">{successMsg}</div>
                        )}
                        <form className="grid grid-cols-1 gap-6 md:grid-cols-2" onSubmit={handleSimpan}>
                            <div>
                                <Label htmlFor="nama" className="text-blue-800">
                                    Nama Panti Asuhan
                                </Label>
                                <Input
                                    id="nama"
                                    name="nama"
                                    className="border-blue-200"
                                    value={form.nama}
                                    onChange={handleInputChange}
                                    disabled={!editMode}
                                />
                            </div>
                            <div>
                                <Label htmlFor="alamat" className="text-blue-800">
                                    Alamat Lengkap
                                </Label>
                                <Input
                                    id="alamat"
                                    name="alamat"
                                    className="border-blue-200"
                                    value={form.alamat}
                                    onChange={handleInputChange}
                                    disabled={!editMode}
                                />
                            </div>
                            <div>
                                <Label htmlFor="kabupaten" className="text-blue-800">
                                    Kabupaten/Kota
                                </Label>
                                <Select value={form.kabupaten} onValueChange={(value) => handleSelectChange('kabupaten', value)} disabled={!editMode}>
                                    <SelectTrigger className="border-blue-200">
                                        <SelectValue placeholder="Pilih Kabupaten/Kota" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {kabupatenRiau.map((kabupaten) => (
                                            <SelectItem key={kabupaten} value={kabupaten}>
                                                {kabupaten}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="telp" className="text-blue-800">
                                    Nomor Telepon
                                </Label>
                                <Input
                                    id="telp"
                                    name="telp"
                                    className="border-blue-200"
                                    value={form.telp}
                                    onChange={handleInputChange}
                                    disabled={!editMode}
                                />
                            </div>
                            <div>
                                <Label htmlFor="email" className="text-blue-800">
                                    Email
                                </Label>
                                <Input
                                    id="email"
                                    name="email"
                                    className="border-blue-200"
                                    value={form.email}
                                    onChange={handleInputChange}
                                    disabled={!editMode}
                                />
                            </div>
                            <div>
                                <Label htmlFor="pimpinan" className="text-blue-800">
                                    Nama Pimpinan Panti
                                </Label>
                                <Input
                                    id="pimpinan"
                                    name="pimpinan"
                                    className="border-blue-200"
                                    value={form.pimpinan}
                                    onChange={handleInputChange}
                                    disabled={!editMode}
                                />
                            </div>
                            <div>
                                <Label htmlFor="npwp" className="text-blue-800">
                                    NPWP
                                </Label>
                                <Input
                                    id="npwp"
                                    name="npwp"
                                    className="border-blue-200"
                                    value={form.npwp}
                                    onChange={handleInputChange}
                                    disabled={!editMode}
                                />
                            </div>
                            {editMode && (
                                <div className="col-span-2 flex justify-end gap-2">
                                    <Button type="button" variant="outline" onClick={handleBatal} className="border-gray-300 text-gray-600">
                                        Batal
                                    </Button>
                                    <Button type="submit" className="bg-blue-600 text-white">
                                        Simpan
                                    </Button>
                                </div>
                            )}
                        </form>
                    </CardContent>
                </Card>

                {/* Dokumen Resmi */}
                <div className="mb-4 flex items-center justify-between">
                    <h2 className="text-xl font-bold text-blue-900">Dokumen Resmi</h2>
                    <div className="text-sm text-gray-600">
                        {Object.keys(uploadedFiles).length} dari {dokumenList.length} dokumen telah diupload
                    </div>
                </div>

                {!editMode && Object.keys(uploadedFiles).length === 0 && (
                    <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <p className="text-sm text-blue-700">
                            <strong>Info:</strong> Untuk mengupload dokumen, silakan klik tombol "Tambah Data" terlebih dahulu.
                        </p>
                    </div>
                )}

                {!editMode && Object.keys(uploadedFiles).length > 0 && (
                    <div className="mb-4 rounded-lg border border-green-200 bg-green-50 p-4">
                        <p className="text-sm text-green-700">
                            <strong>Status:</strong> Anda telah mengupload {Object.keys(uploadedFiles).length} dokumen. Klik "Tambah Data" untuk
                            menambah atau mengganti dokumen.
                        </p>
                    </div>
                )}
                <Card className="w-full border-blue-200">
                    <CardContent>
                        <ul className="divide-y divide-blue-100">
                            {dokumenList.map((doc, idx) => (
                                <li key={idx} className="py-4">
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                            <span className="text-blue-500">
                                                <FileText className="h-5 w-5" />
                                            </span>
                                            <div>
                                                <span className="font-medium text-blue-800">{doc}</span>
                                                {uploadedFiles[doc] && (
                                                    <div className="mt-1 flex items-center gap-1">
                                                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                                        <span className="text-xs text-green-600">Sudah diupload</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        {editMode && (
                                            <div className="flex items-center gap-2">
                                                <input
                                                    type="file"
                                                    data-doc={doc}
                                                    className="block text-xs text-gray-500 file:mr-2 file:rounded file:border-0 file:bg-blue-100 file:px-2 file:py-1 file:text-blue-700"
                                                    onChange={(e) => handleFileChange(e, doc)}
                                                    accept=".pdf,.jpg,.jpeg,.png"
                                                />
                                                <Button
                                                    type="button"
                                                    className="bg-blue-600 px-3 py-1 text-xs text-white"
                                                    onClick={() => handleUpload(doc)}
                                                    disabled={uploading[doc] || !files[doc]}
                                                >
                                                    {uploading[doc] ? 'Uploading...' : 'Upload'}
                                                </Button>
                                                {files[doc] && (
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        className="px-2 py-1 text-xs"
                                                        onClick={() => handleRemoveNewFile(doc)}
                                                    >
                                                        Batal
                                                    </Button>
                                                )}
                                            </div>
                                        )}
                                        {!editMode && uploadedFiles[doc] && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => window.open(`/storage/${uploadedFiles[doc]}`, '_blank')}
                                                className="text-xs"
                                            >
                                                Lihat File
                                            </Button>
                                        )}
                                    </div>

                                    {/* File Preview */}
                                    {editMode && (uploadedFiles[doc] || files[doc]) && (
                                        <div className="mt-3 rounded-lg border border-gray-200 bg-gray-50 p-3">
                                            <div className="mb-2 flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-700">
                                                    {files[doc] ? 'File baru dipilih:' : 'File terupload:'}
                                                </span>
                                                {uploadedFiles[doc] && !files[doc] && (
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => window.open(`/storage/${uploadedFiles[doc]}`, '_blank')}
                                                        className="text-xs"
                                                    >
                                                        Lihat Full
                                                    </Button>
                                                )}
                                            </div>

                                            {(() => {
                                                let previewSrc = '';
                                                let fileName = '';
                                                let isNewFile = false;

                                                if (files[doc]) {
                                                    // New file selected but not uploaded yet
                                                    previewSrc = URL.createObjectURL(files[doc]);
                                                    fileName = files[doc].name;
                                                    isNewFile = true;
                                                } else if (uploadedFiles[doc]) {
                                                    // Previously uploaded file
                                                    previewSrc = `/storage/${uploadedFiles[doc]}`;
                                                    fileName = uploadedFiles[doc].split('/').pop() || '';
                                                    isNewFile = false;
                                                }

                                                if (!previewSrc) return null;

                                                const isImage = fileName.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/i);
                                                const isPdf = fileName.toLowerCase().endsWith('.pdf');

                                                if (isImage) {
                                                    return (
                                                        <div className="relative">
                                                            <img
                                                                src={previewSrc}
                                                                alt={`Preview ${doc}`}
                                                                className="max-h-32 max-w-full rounded border object-cover"
                                                                onError={(e) => {
                                                                    e.currentTarget.style.display = 'none';
                                                                }}
                                                            />
                                                            {isNewFile && (
                                                                <div className="absolute -top-1 -right-1 rounded-full bg-green-500 px-1 py-0.5 text-xs text-white">
                                                                    Baru
                                                                </div>
                                                            )}
                                                        </div>
                                                    );
                                                } else if (isPdf) {
                                                    return (
                                                        <div className="flex items-center space-x-2">
                                                            <FileText className="h-8 w-8 text-red-500" />
                                                            <div className="flex-1">
                                                                <p className="text-sm font-medium text-gray-700">PDF Document</p>
                                                                <p className="text-xs text-gray-500">{fileName}</p>
                                                            </div>
                                                            {isNewFile && (
                                                                <span className="rounded bg-green-500 px-2 py-1 text-xs text-white">Baru</span>
                                                            )}
                                                        </div>
                                                    );
                                                } else {
                                                    return (
                                                        <div className="flex items-center space-x-2">
                                                            <FileText className="h-8 w-8 text-gray-500" />
                                                            <div className="flex-1">
                                                                <p className="text-sm font-medium text-gray-700">Document</p>
                                                                <p className="text-xs text-gray-500">{fileName}</p>
                                                            </div>
                                                            {isNewFile && (
                                                                <span className="rounded bg-green-500 px-2 py-1 text-xs text-white">Baru</span>
                                                            )}
                                                        </div>
                                                    );
                                                }
                                            })()}

                                            {files[doc] && uploadedFiles[doc] && (
                                                <div className="mt-2 rounded bg-amber-50 p-2 text-xs text-amber-600">
                                                    ⚠️ File baru dipilih akan menggantikan file yang sudah ada setelah diupload
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </CardContent>
                </Card>
            </div>
        </PantiLayout>
    );
}
