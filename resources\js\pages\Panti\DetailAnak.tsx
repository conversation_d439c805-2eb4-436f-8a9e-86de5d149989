import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import PantiLayout from '@/layouts/panti-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, User, Calendar, MapPin, Phone, GraduationCap, Heart } from 'lucide-react';
import Progress from '@/components/ui/progress';

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
}

interface CalonOrangTuaAsuh {
    id: number;
    status: string;
    jadwalSidang?: {
        id: number;
        status: string;
        tanggal_sidang: string;
        jam_sidang: string;
    };
}

interface Anak {
    id: number;
    user_id: number;
    nama_lengkap: string;
    tempat_lahir: string;
    tanggal_lahir: string;
    nik: string;
    usia: number;
    jenis_kelamin: string;
    pendidikan: string;
    status_anak: string;
    alasan_tidak_aktif?: string;
    foto_anak?: string;
    nama_ayah?: string;
    usia_ayah?: number;
    pekerjaan_ayah?: string;
    alamat_ayah?: string;
    nama_ibu?: string;
    usia_ibu?: number;
    pekerjaan_ibu?: string;
    alamat_ibu?: string;
    created_at: string;
    updated_at: string;
    tahap_pengangkatan?: string;
    calonOrangTuaAsuh?: CalonOrangTuaAsuh;
}

interface Props {
    user: User;
    anak: Anak;
}

const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'aktif':
            return 'bg-green-100 text-green-800';
        case 'proses pengangkatan':
            return 'bg-blue-100 text-blue-800';
        case 'tidak aktif':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

export default function DetailAnak({ user, anak }: Props) {
    return (
        <PantiLayout user={user}>
            <Head title={`Detail Anak - ${anak.nama_lengkap}`} />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href="/panti/dataanak">
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Kembali
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Detail Data Anak</h1>
                            <p className="text-gray-600">Informasi lengkap data anak asuh</p>
                        </div>
                    </div>
                    <Link href={`/panti/dataanak/${anak.id}/edit`}>
                        <Button className="bg-blue-600 hover:bg-blue-700">
                            Edit Data
                        </Button>
                    </Link>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Foto dan Info Utama */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Foto & Status
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Foto Anak */}
                                <div className="flex justify-center">
                                    {anak.foto_anak ? (
                                        <img
                                            src={`/storage/${anak.foto_anak}`}
                                            alt={anak.nama_lengkap}
                                            className="w-32 h-32 rounded-full object-cover border-4 border-gray-200"
                                        />
                                    ) : (
                                        <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center">
                                            <User className="h-16 w-16 text-gray-400" />
                                        </div>
                                    )}
                                </div>

                                {/* Status */}
                                <div className="text-center space-y-2">
                                    <h3 className="font-semibold text-lg">{anak.nama_lengkap}</h3>
                                    <Badge className={getStatusBadgeColor(anak.status_anak)}>
                                        {anak.status_anak}
                                    </Badge>
                                    {anak.alasan_tidak_aktif && (
                                        <p className="text-sm text-gray-600 mt-2">
                                            <strong>Alasan:</strong> {anak.alasan_tidak_aktif}
                                        </p>
                                    )}
                                </div>

                                {/* Progress Bar untuk Proses Pengangkatan */}
                                {anak.status_anak === 'proses pengangkatan' && anak.tahap_pengangkatan && (
                                    <div className="mt-4">
                                        <h4 className="font-medium mb-2">Progress Pengangkatan</h4>
                                        <Progress tahap={anak.tahap_pengangkatan} />
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Data Pribadi */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Informasi Pribadi */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Informasi Pribadi
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Nama Lengkap</label>
                                        <p className="text-gray-900">{anak.nama_lengkap}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">NIK</label>
                                        <p className="text-gray-900">{anak.nik}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Tempat Lahir</label>
                                        <p className="text-gray-900">{anak.tempat_lahir}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Tanggal Lahir</label>
                                        <p className="text-gray-900">{formatDate(anak.tanggal_lahir)}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Usia</label>
                                        <p className="text-gray-900">{anak.usia} tahun</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Jenis Kelamin</label>
                                        <p className="text-gray-900 capitalize">{anak.jenis_kelamin}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Pendidikan</label>
                                        <p className="text-gray-900">{anak.pendidikan}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Data Orang Tua */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Data Ayah */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Data Ayah
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Nama</label>
                                        <p className="text-gray-900">{anak.nama_ayah || '-'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Usia</label>
                                        <p className="text-gray-900">{anak.usia_ayah ? `${anak.usia_ayah} tahun` : '-'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Pekerjaan</label>
                                        <p className="text-gray-900">{anak.pekerjaan_ayah || '-'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Alamat</label>
                                        <p className="text-gray-900">{anak.alamat_ayah || '-'}</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Data Ibu */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Heart className="h-5 w-5" />
                                        Data Ibu
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Nama</label>
                                        <p className="text-gray-900">{anak.nama_ibu || '-'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Usia</label>
                                        <p className="text-gray-900">{anak.usia_ibu ? `${anak.usia_ibu} tahun` : '-'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Pekerjaan</label>
                                        <p className="text-gray-900">{anak.pekerjaan_ibu || '-'}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Alamat</label>
                                        <p className="text-gray-900">{anak.alamat_ibu || '-'}</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Informasi Sistem */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Informasi Sistem
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Tanggal Dibuat</label>
                                        <p className="text-gray-900">{formatDate(anak.created_at)}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Terakhir Diupdate</label>
                                        <p className="text-gray-900">{formatDate(anak.updated_at)}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </PantiLayout>
    );
}
