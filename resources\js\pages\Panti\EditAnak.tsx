import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import PantiLayout from '@/layouts/panti-layout';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Save, User } from 'lucide-react';
import React, { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    role: string;
}

interface Anak {
    id: number;
    user_id: number;
    nama_lengkap: string;
    tempat_lahir: string;
    tanggal_lahir: string;
    nik: string;
    usia: number;
    jenis_kelamin: string;
    pendidikan: string;
    status_anak: string;
    alasan_tidak_aktif?: string;
    foto_anak?: string;
    nama_ayah?: string;
    usia_ayah?: number;
    pekerjaan_ayah?: string;
    alamat_ayah?: string;
    nama_ibu?: string;
    usia_ibu?: number;
    pekerjaan_ibu?: string;
    alamat_ibu?: string;
}

interface Props {
    user: User;
    anak: Anak;
}

interface FormState {
    nama_lengkap: string;
    tempat_lahir: string;
    tanggal_lahir: string;
    nik: string;
    usia: number;
    jenis_kelamin: string;
    pendidikan: string;
    status_anak: string;
    alasan_tidak_aktif: string;
    nama_ayah: string;
    usia_ayah: number;
    pekerjaan_ayah: string;
    alamat_ayah: string;
    nama_ibu: string;
    usia_ibu: number;
    pekerjaan_ibu: string;
    alamat_ibu: string;
    foto_anak?: File;
}

export default function EditAnak({ user, anak }: Props) {
    const [form, setForm] = useState<FormState>({
        nama_lengkap: anak.nama_lengkap,
        tempat_lahir: anak.tempat_lahir,
        tanggal_lahir: anak.tanggal_lahir.split('T')[0], // Format untuk input date
        nik: anak.nik,
        usia: anak.usia,
        jenis_kelamin: anak.jenis_kelamin,
        pendidikan: anak.pendidikan,
        status_anak: anak.status_anak,
        alasan_tidak_aktif: anak.alasan_tidak_aktif || '',
        nama_ayah: anak.nama_ayah || '',
        usia_ayah: anak.usia_ayah || 0,
        pekerjaan_ayah: anak.pekerjaan_ayah || '',
        alamat_ayah: anak.alamat_ayah || '',
        nama_ibu: anak.nama_ibu || '',
        usia_ibu: anak.usia_ibu || 0,
        pekerjaan_ibu: anak.pekerjaan_ibu || '',
        alamat_ibu: anak.alamat_ibu || '',
    });

    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [successMsg, setSuccessMsg] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (field: keyof FormState, value: string | number | File) => {
        setForm((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setErrorMsg(null);
        setSuccessMsg(null);

        const formData = new FormData();

        // Append all form fields
        Object.entries(form).forEach(([key, value]) => {
            if (key === 'foto_anak' && value instanceof File) {
                formData.append(key, value);
            } else if (value !== undefined && value !== null) {
                formData.append(key, value.toString());
            }
        });

        router.put(`/panti/dataanak/${anak.id}`, formData, {
            forceFormData: true,
            onSuccess: () => {
                setSuccessMsg('Data anak berhasil diperbarui!');
                setIsSubmitting(false);
                // Redirect ke halaman detail setelah 2 detik
                setTimeout(() => {
                    router.visit(`/panti/dataanak/${anak.id}`);
                }, 2000);
            },
            onError: (errors: any) => {
                console.error('Form errors:', errors);
                setIsSubmitting(false);
                if (errors && typeof errors === 'object') {
                    const errorMessages = Object.values(errors).flat().join(', ');
                    setErrorMsg(`Gagal memperbarui data: ${errorMessages}`);
                } else {
                    setErrorMsg('Gagal memperbarui data. Pastikan semua field wajib sudah diisi dengan benar.');
                }
            },
        });
    };

    return (
        <PantiLayout user={user}>
            <Head title={`Edit Anak - ${anak.nama_lengkap}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Link href={`/panti/dataanak/${anak.id}`}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Edit Data Anak</h1>
                            <p className="text-gray-600">Perbarui informasi data anak asuh</p>
                        </div>
                    </div>
                </div>

                {/* Alert Messages */}
                {errorMsg && <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">{errorMsg}</div>}
                {successMsg && <div className="rounded border border-green-200 bg-green-50 px-4 py-3 text-green-700">{successMsg}</div>}

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Informasi Pribadi */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Informasi Pribadi
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div>
                                    <Label htmlFor="nama_lengkap">Nama Lengkap *</Label>
                                    <Input
                                        id="nama_lengkap"
                                        value={form.nama_lengkap}
                                        onChange={(e) => handleInputChange('nama_lengkap', e.target.value)}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="nik">NIK *</Label>
                                    <Input
                                        id="nik"
                                        value={form.nik}
                                        onChange={(e) => handleInputChange('nik', e.target.value)}
                                        maxLength={16}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="tempat_lahir">Tempat Lahir *</Label>
                                    <Input
                                        id="tempat_lahir"
                                        value={form.tempat_lahir}
                                        onChange={(e) => handleInputChange('tempat_lahir', e.target.value)}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="tanggal_lahir">Tanggal Lahir *</Label>
                                    <Input
                                        id="tanggal_lahir"
                                        type="date"
                                        value={form.tanggal_lahir}
                                        onChange={(e) => handleInputChange('tanggal_lahir', e.target.value)}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="usia">Usia *</Label>
                                    <Input
                                        id="usia"
                                        type="number"
                                        value={form.usia}
                                        onChange={(e) => handleInputChange('usia', parseInt(e.target.value) || 0)}
                                        min={0}
                                        required
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="jenis_kelamin">Jenis Kelamin *</Label>
                                    <Select value={form.jenis_kelamin} onValueChange={(value) => handleInputChange('jenis_kelamin', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih jenis kelamin" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="laki-laki">Laki-laki</SelectItem>
                                            <SelectItem value="perempuan">Perempuan</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="pendidikan">Pendidikan *</Label>
                                    <Select value={form.pendidikan} onValueChange={(value) => handleInputChange('pendidikan', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih pendidikan" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="Belum sekolah">Belum sekolah</SelectItem>
                                            <SelectItem value="TK">TK</SelectItem>
                                            <SelectItem value="SD">SD</SelectItem>
                                            <SelectItem value="SMP">SMP</SelectItem>
                                            <SelectItem value="SMA">SMA</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="status_anak">Status Anak *</Label>
                                    <Select value={form.status_anak} onValueChange={(value) => handleInputChange('status_anak', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="aktif">Aktif</SelectItem>
                                            <SelectItem value="proses pengangkatan">Proses Pengangkatan</SelectItem>
                                            <SelectItem value="tidak aktif">Tidak Aktif</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {form.status_anak === 'tidak aktif' && (
                                <div>
                                    <Label htmlFor="alasan_tidak_aktif">Alasan Tidak Aktif *</Label>
                                    <Textarea
                                        id="alasan_tidak_aktif"
                                        value={form.alasan_tidak_aktif}
                                        onChange={(e) => handleInputChange('alasan_tidak_aktif', e.target.value)}
                                        placeholder="Masukkan alasan anak tidak aktif"
                                        required={form.status_anak === 'tidak aktif'}
                                    />
                                </div>
                            )}

                            <div>
                                <Label htmlFor="foto_anak">Foto Anak</Label>
                                <Input
                                    id="foto_anak"
                                    type="file"
                                    accept="image/jpeg,image/jpg,image/png"
                                    onChange={(e) => {
                                        const file = e.target.files?.[0];
                                        if (file) handleInputChange('foto_anak', file);
                                    }}
                                />
                                <p className="mt-1 text-sm text-gray-500">Format: JPEG, JPG, PNG. Maksimal 2MB.</p>
                                {anak.foto_anak && (
                                    <div className="mt-2">
                                        <p className="text-sm text-gray-600">Foto saat ini:</p>
                                        <img
                                            src={`/storage/${anak.foto_anak}`}
                                            alt="Foto anak saat ini"
                                            className="mt-1 h-20 w-20 rounded border object-cover"
                                        />
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Data Orang Tua */}
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                        {/* Data Ayah */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Data Ayah
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="nama_ayah">Nama Ayah</Label>
                                    <Input id="nama_ayah" value={form.nama_ayah} onChange={(e) => handleInputChange('nama_ayah', e.target.value)} />
                                </div>
                                <div>
                                    <Label htmlFor="usia_ayah">Usia Ayah</Label>
                                    <Input
                                        id="usia_ayah"
                                        type="number"
                                        value={form.usia_ayah}
                                        onChange={(e) => handleInputChange('usia_ayah', parseInt(e.target.value) || 0)}
                                        min={0}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="pekerjaan_ayah">Pekerjaan Ayah</Label>
                                    <Input
                                        id="pekerjaan_ayah"
                                        value={form.pekerjaan_ayah}
                                        onChange={(e) => handleInputChange('pekerjaan_ayah', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="alamat_ayah">Alamat Ayah</Label>
                                    <Textarea
                                        id="alamat_ayah"
                                        value={form.alamat_ayah}
                                        onChange={(e) => handleInputChange('alamat_ayah', e.target.value)}
                                        placeholder="Masukkan alamat ayah"
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Data Ibu */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Data Ibu
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="nama_ibu">Nama Ibu</Label>
                                    <Input id="nama_ibu" value={form.nama_ibu} onChange={(e) => handleInputChange('nama_ibu', e.target.value)} />
                                </div>
                                <div>
                                    <Label htmlFor="usia_ibu">Usia Ibu</Label>
                                    <Input
                                        id="usia_ibu"
                                        type="number"
                                        value={form.usia_ibu}
                                        onChange={(e) => handleInputChange('usia_ibu', parseInt(e.target.value) || 0)}
                                        min={0}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="pekerjaan_ibu">Pekerjaan Ibu</Label>
                                    <Input
                                        id="pekerjaan_ibu"
                                        value={form.pekerjaan_ibu}
                                        onChange={(e) => handleInputChange('pekerjaan_ibu', e.target.value)}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="alamat_ibu">Alamat Ibu</Label>
                                    <Textarea
                                        id="alamat_ibu"
                                        value={form.alamat_ibu}
                                        onChange={(e) => handleInputChange('alamat_ibu', e.target.value)}
                                        placeholder="Masukkan alamat ibu"
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end gap-4">
                        <Link href={`/panti/dataanak/${anak.id}`}>
                            <Button type="button" variant="outline">
                                Batal
                            </Button>
                        </Link>
                        <Button type="submit" disabled={isSubmitting} className="bg-blue-600 hover:bg-blue-700">
                            <Save className="mr-2 h-4 w-4" />
                            {isSubmitting ? 'Menyimpan...' : 'Simpan Perubahan'}
                        </Button>
                    </div>
                </form>
            </div>
        </PantiLayout>
    );
}
