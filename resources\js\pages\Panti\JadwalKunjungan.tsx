import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, router, useForm } from '@inertiajs/react';
import { Calendar, CheckCircle, ChevronLeft, ChevronRight, Eye, FileText, XCircle } from 'lucide-react';
import { useState } from 'react';

interface JadwalKunjungan {
    id: number;
    panti_id: number;
    panti?: {
        id: number;
        nama: string;
        alamat: string;
    };
    tanggal_kunjungan: string;
    waktu_kunjungan: string;
    tim_anggota_1: string;
    tim_anggota_2: string;
    tim_anggota_3: string;
    surat_perjalanan_dinas: string;
    deskripsi_kegiatan: string;
    status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
    created_at: string;
    updated_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    jadwalKunjungan?: JadwalKunjungan[];
    pagination: Pagination;
    flash?: {
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Jadwal Kunjungan',
        href: '/panti/jadwalkunjungan',
    },
];

export default function JadwalKunjungan({ user, jadwalKunjungan = [], pagination, flash }: Props) {
    const [selectedJadwal, setSelectedJadwal] = useState<JadwalKunjungan | null>(null);
    const [isDetailOpen, setIsDetailOpen] = useState(false);
    const [isFileViewOpen, setIsFileViewOpen] = useState(false);
    const { post } = useForm();

    const handleViewDetail = (jadwal: JadwalKunjungan) => {
        setSelectedJadwal(jadwal);
        setIsDetailOpen(true);
    };

    const handleViewFile = (jadwal: JadwalKunjungan) => {
        setSelectedJadwal(jadwal);
        setIsFileViewOpen(true);
    };

    const handleConfirmJadwal = (jadwalId: number) => {
        // Send to backend in real implementation
        post(`/panti/jadwalkunjungan/${jadwalId}/confirm`, {
            onSuccess: () => {
                // Success message will be shown via flash message
            },
            onError: (errors) => {
                console.error('Error confirming jadwal:', errors);
            },
        });
    };

    const handleRejectJadwal = (jadwalId: number) => {
        // Send to backend in real implementation
        post(`/panti/jadwalkunjungan/${jadwalId}/reject`, {
            onSuccess: () => {
                // Success message will be shown via flash message
            },
            onError: (errors) => {
                console.error('Error rejecting jadwal:', errors);
            },
        });
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Jadwal Kunjungan" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-blue-900">Jadwal Kunjungan</h1>
                        <p className="mt-1 text-blue-600">Kelola jadwal kunjungan dari Dinas Sosial Provinsi Riau</p>
                    </div>
                </div>

                {/* Flash Messages */}
                {flash?.success && (
                    <div className="rounded-md border border-green-200 bg-green-50 p-4">
                        <div className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-green-800">{flash.success}</p>
                            </div>
                        </div>
                    </div>
                )}
                {flash?.error && (
                    <div className="rounded-md border border-red-200 bg-red-50 p-4">
                        <div className="flex items-center">
                            <XCircle className="h-4 w-4 text-red-400" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-red-800">{flash.error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Content */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Calendar className="h-5 w-5" />
                            <span>Daftar Jadwal Kunjungan</span>
                        </CardTitle>
                        <CardDescription>Jadwal kunjungan monitoring dan evaluasi dari Dinas Sosial Provinsi</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="w-12">No</TableHead>
                                        <TableHead>Panti Tujuan</TableHead>
                                        <TableHead>Tanggal Kunjungan</TableHead>
                                        <TableHead>Waktu</TableHead>
                                        <TableHead>Tim Kunjungan</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-center">Aksi</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {jadwalKunjungan.length > 0 ? (
                                        jadwalKunjungan.map((item, index) => (
                                            <TableRow key={item.id}>
                                                <TableCell className="font-medium">{index + 1}</TableCell>
                                                <TableCell className="font-medium">{item.panti?.nama || `Panti ID: ${item.panti_id}`}</TableCell>
                                                <TableCell>
                                                    {new Date(item.tanggal_kunjungan).toLocaleDateString('id-ID', {
                                                        day: '2-digit',
                                                        month: '2-digit',
                                                        year: 'numeric',
                                                    })}
                                                </TableCell>
                                                <TableCell>{item.waktu_kunjungan}</TableCell>
                                                <TableCell>
                                                    <div className="space-y-1 text-sm">
                                                        <div>• {item.tim_anggota_1}</div>
                                                        <div>• {item.tim_anggota_2}</div>
                                                        <div>• {item.tim_anggota_3}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <span
                                                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                                            item.status === 'confirmed'
                                                                ? 'bg-green-100 text-green-800'
                                                                : item.status === 'scheduled'
                                                                  ? 'bg-yellow-100 text-yellow-800'
                                                                  : item.status === 'completed'
                                                                    ? 'bg-blue-100 text-blue-800'
                                                                    : 'bg-red-100 text-red-800'
                                                        }`}
                                                    >
                                                        {item.status === 'scheduled'
                                                            ? 'Menunggu Konfirmasi'
                                                            : item.status === 'confirmed'
                                                              ? 'Dikonfirmasi'
                                                              : item.status === 'completed'
                                                                ? 'Selesai'
                                                                : 'Ditolak'}
                                                    </span>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex justify-center space-x-2">
                                                        <Button variant="outline" size="sm" onClick={() => handleViewDetail(item)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                        <Button variant="outline" size="sm" onClick={() => handleViewFile(item)}>
                                                            <FileText className="h-4 w-4" />
                                                        </Button>
                                                        {item.status === 'scheduled' && (
                                                            <>
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-green-600 hover:text-green-700"
                                                                    onClick={() => handleConfirmJadwal(item.id)}
                                                                >
                                                                    <CheckCircle className="h-4 w-4" />
                                                                </Button>
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-600 hover:text-red-700"
                                                                    onClick={() => handleRejectJadwal(item.id)}
                                                                >
                                                                    <XCircle className="h-4 w-4" />
                                                                </Button>
                                                            </>
                                                        )}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={7} className="py-8 text-center text-gray-500">
                                                Belum ada jadwal kunjungan
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        {pagination && (
                            <div className="mt-4 flex items-center justify-between px-4 pb-4">
                                <div className="text-sm text-gray-700">
                                    Menampilkan {pagination.from || 0} hingga {pagination.to || 0} dari {pagination.total} data
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                        disabled={pagination.current_page === 1}
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        Sebelumnya
                                    </Button>
                                    <span className="flex items-center px-3 text-sm text-gray-600">
                                        Halaman {pagination.current_page} dari {Math.max(pagination.last_page, 1)}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                        disabled={pagination.current_page === pagination.last_page}
                                    >
                                        Selanjutnya
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Detail Modal */}
                <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>Detail Jadwal Kunjungan</DialogTitle>
                            <DialogDescription>Informasi lengkap jadwal kunjungan</DialogDescription>
                        </DialogHeader>
                        {selectedJadwal && (
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <h4 className="font-semibold text-gray-900">Tanggal Kunjungan</h4>
                                        <p className="text-gray-600">
                                            {new Date(selectedJadwal.tanggal_kunjungan).toLocaleDateString('id-ID', {
                                                day: '2-digit',
                                                month: 'long',
                                                year: 'numeric',
                                            })}
                                        </p>
                                    </div>
                                    <div>
                                        <h4 className="font-semibold text-gray-900">Waktu Kunjungan</h4>
                                        <p className="text-gray-600">{selectedJadwal.waktu_kunjungan}</p>
                                    </div>
                                </div>
                                <div>
                                    <h4 className="font-semibold text-gray-900">Tim Kunjungan</h4>
                                    <div className="mt-2 space-y-1">
                                        <p className="text-gray-600">1. {selectedJadwal.tim_anggota_1}</p>
                                        <p className="text-gray-600">2. {selectedJadwal.tim_anggota_2}</p>
                                        <p className="text-gray-600">3. {selectedJadwal.tim_anggota_3}</p>
                                    </div>
                                </div>
                                <div>
                                    <h4 className="font-semibold text-gray-900">Deskripsi Kegiatan</h4>
                                    <p className="mt-2 text-gray-600">{selectedJadwal.deskripsi_kegiatan}</p>
                                </div>
                                <div className="flex justify-end space-x-2 pt-4">
                                    <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                                        Tutup
                                    </Button>
                                    {selectedJadwal.status === 'scheduled' && (
                                        <Button
                                            className="bg-green-600 hover:bg-green-700"
                                            onClick={() => {
                                                handleConfirmJadwal(selectedJadwal.id);
                                                setIsDetailOpen(false);
                                            }}
                                        >
                                            Konfirmasi Jadwal
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>

                {/* File View Modal */}
                <Dialog open={isFileViewOpen} onOpenChange={setIsFileViewOpen}>
                    <DialogContent className="max-h-[90vh] max-w-4xl">
                        <DialogHeader>
                            <DialogTitle>Surat Perjalanan Dinas</DialogTitle>
                            <DialogDescription>Dokumen surat perjalanan dinas untuk kunjungan</DialogDescription>
                        </DialogHeader>
                        {selectedJadwal && (
                            <div className="space-y-4">
                                <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center">
                                    <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                    <p className="mt-2 text-sm text-gray-500">Dokumen: {selectedJadwal.surat_perjalanan_dinas || 'Tidak ada file'}</p>
                                    <p className="mt-1 text-xs text-gray-400">Dalam implementasi nyata, file akan ditampilkan di sini</p>
                                </div>
                                <div className="flex justify-end space-x-2">
                                    <Button variant="outline" onClick={() => setIsFileViewOpen(false)}>
                                        Tutup
                                    </Button>
                                    <Button>Download File</Button>
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
            </div>
        </PantiLayout>
    );
}
