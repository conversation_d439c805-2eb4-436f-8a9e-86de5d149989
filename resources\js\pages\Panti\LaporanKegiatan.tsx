import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Calendar, ChevronLeft, ChevronRight, Clock, Eye, MapPin, Plus, Trash2, Users, X } from 'lucide-react';
import { ChangeEvent, FormEvent, useState } from 'react';

interface LaporanKegiatan {
    id: number;
    nama_kegiatan: string;
    jenis_kegiatan: string;
    tanggal_pelaksanaan: string;
    waktu_mulai: string;
    waktu_selesai: string;
    lokasi_pelaksanaan: string;
    jumlah_peserta: number;
    penanggung_jawab: string;
    deskripsi_kegiatan: string;
    hasil_kegiatan: string;
    kendala_tantangan?: string;
    foto_kegiatan?: string;
    laporan_kegiatan?: string;
    admin_feedback?: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    laporans: LaporanKegiatan[];
    pagination: Pagination;
}

interface FormState {
    [key: string]: string | number | File | undefined;
    nama_kegiatan: string;
    jenis_kegiatan: string;
    tanggal_pelaksanaan: string;
    waktu_mulai: string;
    waktu_selesai: string;
    lokasi_pelaksanaan: string;
    jumlah_peserta: number;
    penanggung_jawab: string;
    deskripsi_kegiatan: string;
    hasil_kegiatan: string;
    kendala_tantangan: string;
    foto_kegiatan?: File;
    laporan_kegiatan?: File;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Laporan Kegiatan',
        href: '/panti/laporankegiatan',
    },
];

export default function LaporanKegiatan({ user, laporans, pagination }: Props) {
    const [showForm, setShowForm] = useState(false);
    const [viewingLaporan, setViewingLaporan] = useState<LaporanKegiatan | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);
    const [showImageModal, setShowImageModal] = useState(false);
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [form, setForm] = useState<FormState>({
        nama_kegiatan: '',
        jenis_kegiatan: '',
        tanggal_pelaksanaan: '',
        waktu_mulai: '',
        waktu_selesai: '',
        lokasi_pelaksanaan: '',
        jumlah_peserta: 0,
        penanggung_jawab: '',
        deskripsi_kegiatan: '',
        hasil_kegiatan: '',
        kendala_tantangan: '',
    });
    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [successMsg, setSuccessMsg] = useState<string | null>(null);
    const [filterKategori, setFilterKategori] = useState('Semua');

    // Remove client-side pagination as we now use server-side
    const filteredData = filterKategori === 'Semua' ? laporans : laporans.filter((laporan) => laporan.jenis_kegiatan === filterKategori);

    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setForm({
            ...form,
            [name]: type === 'number' ? Number(value) : value,
        });
    };

    const handleSelectChange = (name: string, value: string) => {
        setForm({ ...form, [name]: value });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name } = e.target;
        if (e.target.files && e.target.files[0]) {
            setForm({ ...form, [name]: e.target.files[0] });
        }
    };

    const handleTambahKegiatan = () => {
        setShowForm(true);
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleBatal = () => {
        setShowForm(false);
        setForm({
            nama_kegiatan: '',
            jenis_kegiatan: '',
            tanggal_pelaksanaan: '',
            waktu_mulai: '',
            waktu_selesai: '',
            lokasi_pelaksanaan: '',
            jumlah_peserta: 0,
            penanggung_jawab: '',
            deskripsi_kegiatan: '',
            hasil_kegiatan: '',
            kendala_tantangan: '',
        });
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorMsg(null);
        setSuccessMsg(null);

        const formData = new FormData();
        Object.keys(form).forEach((key) => {
            const value = form[key];
            if (value !== undefined && value !== '') {
                if (value instanceof File) {
                    formData.append(key, value);
                } else {
                    formData.append(key, String(value));
                }
            }
        });

        router.post('/panti/laporankegiatan', formData, {
            forceFormData: true,
            onSuccess: () => {
                setShowForm(false);
                setSuccessMsg('Laporan kegiatan berhasil ditambahkan!');
                handleBatal();
            },
            onError: (errors: any) => {
                setErrorMsg('Gagal menyimpan laporan. Pastikan semua field wajib sudah diisi dengan benar.');
                console.error('Form errors:', errors);
            },
        });
    };

    const handleViewLaporan = (laporan: LaporanKegiatan) => {
        setViewingLaporan(laporan);
    };

    const handleDeleteLaporan = (id: number) => {
        setShowDeleteConfirm(id);
    };

    const handleViewImage = (imagePath: string) => {
        setSelectedImage(imagePath);
        setShowImageModal(true);
    };

    const handleCloseImageModal = () => {
        setShowImageModal(false);
        setSelectedImage(null);
    };

    const confirmDelete = () => {
        if (showDeleteConfirm) {
            router.delete(`/panti/laporankegiatan/${showDeleteConfirm}`, {
                onSuccess: () => {
                    setSuccessMsg('Laporan kegiatan berhasil dihapus!');
                    setShowDeleteConfirm(null);
                },
                onError: () => {
                    setErrorMsg('Gagal menghapus laporan kegiatan.');
                    setShowDeleteConfirm(null);
                },
            });
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
        });
    };

    const formatTime = (timeString: string) => {
        return timeString.substring(0, 5); // HH:MM format
    };

    const getKategoriColor = (kategori: string) => {
        const colors: { [key: string]: string } = {
            'pendidikan dan pelatihan': 'bg-blue-100 text-blue-800',
            'kegiatan sosial': 'bg-green-100 text-green-800',
            kesehatan: 'bg-red-100 text-red-800',
            'olahraga & rekreasi': 'bg-orange-100 text-orange-800',
            keagamaan: 'bg-purple-100 text-purple-800',
            'seni & budaya': 'bg-pink-100 text-pink-800',
            keterampilan: 'bg-yellow-100 text-yellow-800',
            lainnya: 'bg-gray-100 text-gray-800',
        };
        return colors[kategori] || 'bg-gray-100 text-gray-800';
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Laporan Kegiatan" />
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight text-blue-900">Laporan Kegiatan</h1>
                    {!showForm && (
                        <Button variant="default" size="sm" className="gap-2 bg-blue-600 text-white hover:bg-blue-700" onClick={handleTambahKegiatan}>
                            <Plus className="h-4 w-4" />
                            Tambah Kegiatan
                        </Button>
                    )}
                </div>

                {/* Form Tambah Kegiatan */}
                {showForm && (
                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900">Form Tambah Laporan Kegiatan</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {errorMsg && (
                                <div className="mb-4 rounded border border-red-200 bg-red-100 px-4 py-2 text-sm text-red-700">{errorMsg}</div>
                            )}
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Informasi Dasar */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Informasi Dasar Kegiatan</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="nama_kegiatan" className="text-blue-800">
                                                Nama Kegiatan *
                                            </Label>
                                            <Input
                                                id="nama_kegiatan"
                                                name="nama_kegiatan"
                                                value={form.nama_kegiatan}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="jenis_kegiatan" className="text-blue-800">
                                                Jenis Kegiatan *
                                            </Label>
                                            <Select
                                                value={form.jenis_kegiatan}
                                                onValueChange={(value) => handleSelectChange('jenis_kegiatan', value)}
                                            >
                                                <SelectTrigger className="border-blue-200">
                                                    <SelectValue placeholder="Pilih jenis kegiatan" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="pendidikan dan pelatihan">Pendidikan dan Pelatihan</SelectItem>
                                                    <SelectItem value="kegiatan sosial">Kegiatan Sosial</SelectItem>
                                                    <SelectItem value="kesehatan">Kesehatan</SelectItem>
                                                    <SelectItem value="olahraga & rekreasi">Olahraga & Rekreasi</SelectItem>
                                                    <SelectItem value="keagamaan">Keagamaan</SelectItem>
                                                    <SelectItem value="seni & budaya">Seni & Budaya</SelectItem>
                                                    <SelectItem value="keterampilan">Keterampilan</SelectItem>
                                                    <SelectItem value="lainnya">Lainnya</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div>
                                            <Label htmlFor="tanggal_pelaksanaan" className="text-blue-800">
                                                Tanggal Pelaksanaan *
                                            </Label>
                                            <Input
                                                id="tanggal_pelaksanaan"
                                                name="tanggal_pelaksanaan"
                                                type="date"
                                                value={form.tanggal_pelaksanaan}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="lokasi_pelaksanaan" className="text-blue-800">
                                                Lokasi Pelaksanaan *
                                            </Label>
                                            <Input
                                                id="lokasi_pelaksanaan"
                                                name="lokasi_pelaksanaan"
                                                value={form.lokasi_pelaksanaan}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="waktu_mulai" className="text-blue-800">
                                                Waktu Mulai *
                                            </Label>
                                            <Input
                                                id="waktu_mulai"
                                                name="waktu_mulai"
                                                type="time"
                                                value={form.waktu_mulai}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="waktu_selesai" className="text-blue-800">
                                                Waktu Selesai *
                                            </Label>
                                            <Input
                                                id="waktu_selesai"
                                                name="waktu_selesai"
                                                type="time"
                                                value={form.waktu_selesai}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="jumlah_peserta" className="text-blue-800">
                                                Jumlah Peserta *
                                            </Label>
                                            <Input
                                                id="jumlah_peserta"
                                                name="jumlah_peserta"
                                                type="number"
                                                value={form.jumlah_peserta}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                min="1"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="penanggung_jawab" className="text-blue-800">
                                                Penanggung Jawab *
                                            </Label>
                                            <Input
                                                id="penanggung_jawab"
                                                name="penanggung_jawab"
                                                value={form.penanggung_jawab}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Deskripsi dan Hasil */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Deskripsi dan Hasil Kegiatan</h3>
                                    <div className="grid grid-cols-1 gap-4">
                                        <div>
                                            <Label htmlFor="deskripsi_kegiatan" className="text-blue-800">
                                                Deskripsi Kegiatan *
                                            </Label>
                                            <Textarea
                                                id="deskripsi_kegiatan"
                                                name="deskripsi_kegiatan"
                                                value={form.deskripsi_kegiatan}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                rows={3}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="hasil_kegiatan" className="text-blue-800">
                                                Hasil Kegiatan *
                                            </Label>
                                            <Textarea
                                                id="hasil_kegiatan"
                                                name="hasil_kegiatan"
                                                value={form.hasil_kegiatan}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                rows={3}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="kendala_tantangan" className="text-blue-800">
                                                Kendala dan Tantangan
                                            </Label>
                                            <Textarea
                                                id="kendala_tantangan"
                                                name="kendala_tantangan"
                                                value={form.kendala_tantangan}
                                                onChange={handleInputChange}
                                                className="border-blue-200"
                                                rows={3}
                                                placeholder="Opsional - kendala yang dihadapi selama kegiatan"
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Upload Files */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Dokumen Pendukung</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="foto_kegiatan" className="text-blue-800">
                                                Foto Kegiatan
                                            </Label>
                                            <Input
                                                id="foto_kegiatan"
                                                name="foto_kegiatan"
                                                type="file"
                                                accept="image/jpeg,image/jpg,image/png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Format: JPG, PNG. Maks 2MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="laporan_kegiatan" className="text-blue-800">
                                                Laporan Kegiatan
                                            </Label>
                                            <Input
                                                id="laporan_kegiatan"
                                                name="laporan_kegiatan"
                                                type="file"
                                                accept=".pdf,.doc,.docx"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Format: PDF, DOC, DOCX. Maks 5MB</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-end gap-2">
                                    <Button type="button" variant="outline" onClick={handleBatal} className="border-blue-200">
                                        Batal
                                    </Button>
                                    <Button type="submit" className="bg-blue-600 text-white hover:bg-blue-700">
                                        Simpan Laporan
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}

                {/* Success Message */}
                {successMsg && <div className="rounded border border-green-200 bg-green-100 px-4 py-2 text-sm text-green-700">{successMsg}</div>}

                {/* Filter dan Stats */}
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                            <Label className="text-blue-800">Filter Kategori:</Label>
                            <Select value={filterKategori} onValueChange={setFilterKategori}>
                                <SelectTrigger className="w-48 border-blue-200">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Semua">Semua Kategori</SelectItem>
                                    <SelectItem value="pendidikan dan pelatihan">Pendidikan dan Pelatihan</SelectItem>
                                    <SelectItem value="kegiatan sosial">Kegiatan Sosial</SelectItem>
                                    <SelectItem value="kesehatan">Kesehatan</SelectItem>
                                    <SelectItem value="olahraga & rekreasi">Olahraga & Rekreasi</SelectItem>
                                    <SelectItem value="keagamaan">Keagamaan</SelectItem>
                                    <SelectItem value="seni & budaya">Seni & Budaya</SelectItem>
                                    <SelectItem value="keterampilan">Keterampilan</SelectItem>
                                    <SelectItem value="lainnya">Lainnya</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>Total: {filteredData.length} kegiatan</span>
                    </div>
                </div>

                {/* Cards Grid */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {filteredData.length > 0 ? (
                        filteredData.map((laporan: LaporanKegiatan) => (
                            <Card key={laporan.id} className="border-blue-200 transition-shadow hover:shadow-lg">
                                <CardHeader className="pb-4">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <CardTitle className="mb-2 text-lg text-blue-900">{laporan.nama_kegiatan}</CardTitle>
                                            <Badge className={getKategoriColor(laporan.jenis_kegiatan)}>{laporan.jenis_kegiatan}</Badge>
                                        </div>
                                        <div className="flex gap-1">
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleViewLaporan(laporan)}>
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-8 w-8 p-0 text-red-600"
                                                onClick={() => handleDeleteLaporan(laporan.id)}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                            {/* Button Cetak Laporan */}
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="h-8 border-blue-300 px-2 text-blue-700"
                                                title="Cetak Laporan Kegiatan"
                                                onClick={() => {
                                                    if (laporan.laporan_kegiatan) {
                                                        window.open(`/storage/${laporan.laporan_kegiatan}`, '_blank');
                                                    } else {
                                                        window.print();
                                                    }
                                                }}
                                            >
                                                Cetak
                                            </Button>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <Calendar className="h-4 w-4" />
                                        <span>{formatDate(laporan.tanggal_pelaksanaan)}</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <Clock className="h-4 w-4" />
                                        <span>
                                            {formatTime(laporan.waktu_mulai)} - {formatTime(laporan.waktu_selesai)}
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <MapPin className="h-4 w-4" />
                                        <span>{laporan.lokasi_pelaksanaan}</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <Users className="h-4 w-4" />
                                        <span>{laporan.jumlah_peserta} peserta</span>
                                    </div>
                                    <p className="line-clamp-3 text-sm text-gray-700">{laporan.deskripsi_kegiatan}</p>
                                </CardContent>
                            </Card>
                        ))
                    ) : (
                        <div className="col-span-full py-8 text-center">
                            <p className="text-gray-500">
                                {filterKategori === 'Semua'
                                    ? 'Belum ada laporan kegiatan. Klik tombol "Tambah Kegiatan" untuk menambah laporan.'
                                    : `Tidak ada kegiatan untuk kategori "${filterKategori}".`}
                            </p>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                {pagination && (
                    <div className="flex items-center justify-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                            disabled={pagination.current_page === 1}
                            className="border-blue-200"
                        >
                            <ChevronLeft className="h-4 w-4" />
                            Sebelumnya
                        </Button>
                        <span className="text-sm text-gray-600">
                            Halaman {pagination.current_page} dari {Math.max(pagination.last_page, 1)}
                        </span>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                            disabled={pagination.current_page === pagination.last_page}
                            className="border-blue-200"
                        >
                            Selanjutnya
                            <ChevronRight className="h-4 w-4" />
                        </Button>
                    </div>
                )}

                {/* Modal View Detail */}
                {viewingLaporan && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 max-h-[90vh] w-full max-w-4xl overflow-y-auto border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-xl font-bold text-blue-900">Detail Laporan Kegiatan</CardTitle>
                                <Button variant="ghost" size="sm" onClick={() => setViewingLaporan(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Informasi Utama */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Informasi Kegiatan</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label className="font-medium text-blue-700">Nama Kegiatan</Label>
                                            <p className="text-gray-900">{viewingLaporan.nama_kegiatan}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Jenis Kegiatan</Label>
                                            <p className="text-gray-900">{viewingLaporan.jenis_kegiatan}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Tanggal Pelaksanaan</Label>
                                            <p className="text-gray-900">{formatDate(viewingLaporan.tanggal_pelaksanaan)}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Waktu</Label>
                                            <p className="text-gray-900">
                                                {formatTime(viewingLaporan.waktu_mulai)} - {formatTime(viewingLaporan.waktu_selesai)}
                                            </p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Lokasi</Label>
                                            <p className="text-gray-900">{viewingLaporan.lokasi_pelaksanaan}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Jumlah Peserta</Label>
                                            <p className="text-gray-900">{viewingLaporan.jumlah_peserta} orang</p>
                                        </div>
                                        <div className="md:col-span-2">
                                            <Label className="font-medium text-blue-700">Penanggung Jawab</Label>
                                            <p className="text-gray-900">{viewingLaporan.penanggung_jawab}</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Deskripsi dan Hasil */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Deskripsi dan Hasil</h3>
                                    <div className="space-y-4">
                                        <div>
                                            <Label className="font-medium text-blue-700">Deskripsi Kegiatan</Label>
                                            <p className="mt-1 text-gray-900">{viewingLaporan.deskripsi_kegiatan}</p>
                                        </div>
                                        <div>
                                            <Label className="font-medium text-blue-700">Hasil Kegiatan</Label>
                                            <p className="mt-1 text-gray-900">{viewingLaporan.hasil_kegiatan}</p>
                                        </div>
                                        {viewingLaporan.kendala_tantangan && (
                                            <div>
                                                <Label className="font-medium text-blue-700">Kendala dan Tantangan</Label>
                                                <p className="mt-1 text-gray-900">{viewingLaporan.kendala_tantangan}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* File Attachments */}
                                <div>
                                    <h3 className="mb-4 text-lg font-semibold text-blue-800">Dokumen Pendukung</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        {viewingLaporan.foto_kegiatan && (
                                            <div>
                                                <Label className="font-medium text-blue-700">Foto Kegiatan</Label>
                                                <div className="relative mt-2">
                                                    <img
                                                        src={`/storage/${viewingLaporan.foto_kegiatan}`}
                                                        alt="Foto kegiatan"
                                                        className="h-48 w-full rounded-lg border object-cover"
                                                    />
                                                    <div className="absolute top-2 right-2">
                                                        <Button
                                                            size="sm"
                                                            onClick={() => handleViewImage(viewingLaporan.foto_kegiatan!)}
                                                            className="bg-white/90 text-blue-600 shadow-md hover:bg-white hover:text-blue-700"
                                                        >
                                                            <Eye className="mr-1 h-4 w-4" />
                                                            View
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        {viewingLaporan.laporan_kegiatan && (
                                            <div>
                                                <Label className="font-medium text-blue-700">Laporan Kegiatan</Label>
                                                <div className="mt-2">
                                                    <a
                                                        href={`/storage/${viewingLaporan.laporan_kegiatan}`}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="inline-flex items-center gap-2 rounded-lg bg-blue-100 px-3 py-2 text-blue-700 hover:bg-blue-200"
                                                    >
                                                        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                                            <path
                                                                fillRule="evenodd"
                                                                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                                                clipRule="evenodd"
                                                            />
                                                        </svg>
                                                        Lihat Dokumen
                                                    </a>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Admin Feedback */}
                                {viewingLaporan.admin_feedback && (
                                    <div>
                                        <h3 className="mb-2 text-lg font-semibold text-blue-800">Feedback dari Dinas Sosial</h3>
                                        <div className="rounded-md border border-blue-200 bg-blue-50 p-3 text-blue-900">
                                            {viewingLaporan.admin_feedback}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Konfirmasi Hapus */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-red-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader>
                                <CardTitle className="text-xl font-bold text-red-600">Konfirmasi Hapus</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-700">
                                    Apakah Anda yakin ingin menghapus laporan kegiatan ini? Tindakan ini tidak dapat dibatalkan.
                                </p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                                        Batal
                                    </Button>
                                    <Button variant="destructive" onClick={confirmDelete}>
                                        Hapus
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal View Foto Kegiatan */}
                {showImageModal && selectedImage && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm" onClick={handleCloseImageModal}>
                        <div className="relative max-h-[90vh] max-w-[90vw] p-4" onClick={(e) => e.stopPropagation()}>
                            <Card className="border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-xl font-bold text-blue-900">Foto Kegiatan</CardTitle>
                                    <Button variant="ghost" size="sm" onClick={handleCloseImageModal} className="h-8 w-8 p-0">
                                        <X className="h-4 w-4" />
                                    </Button>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex justify-center">
                                        <img
                                            src={`/storage/${selectedImage}`}
                                            alt="Foto kegiatan"
                                            className="max-h-[70vh] max-w-full rounded-lg border object-contain shadow-lg"
                                        />
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                )}
            </div>
        </PantiLayout>
    );
}
