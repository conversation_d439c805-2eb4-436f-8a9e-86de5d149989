import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem, Pagination } from '@/types';
import { Head, router } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, Eye, Info, Plus, Trash2, X } from 'lucide-react';
import { ChangeEvent, FormEvent, useState } from 'react';

interface PengajuanDana {
    id: number;
    tanggal_pengajuan: string;
    tujuan_penggunaan: string;
    periode_mulai: string;
    periode_selesai: string;
    total_dana: number;
    deskripsi_kebutuhan: string;
    file_proposal?: string;
    file_rekening?: string;
    file_ktp?: string;
    file_foto_kegiatan?: string;
    file_rab?: string;
    status: string;
    catatan_admin?: string;
    created_at: string;
    updated_at: string;
    file_laporan_penggunaan?: string;
    tanggal_upload_laporan?: string;
    status_laporan?: string;
    catatan_laporan?: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pengajuans: PengajuanDana[];
    pagination: Pagination;
}

interface FormState {
    [key: string]: string | number | File | undefined;
    tanggal_pengajuan: string;
    tujuan_penggunaan: string;
    periode_mulai: string;
    periode_selesai: string;
    total_dana: number;
    deskripsi_kebutuhan: string;
    file_proposal?: File;
    file_rekening?: File;
    file_ktp?: File;
    file_foto_kegiatan?: File;
    file_rab?: File;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Pendanaan',
        href: '/panti/pendanaan',
    },
];

export default function Pendanaan({ user, pengajuans, pagination }: Props) {
    const [showForm, setShowForm] = useState(false);
    const [viewingPengajuan, setViewingPengajuan] = useState<PengajuanDana | null>(null);
    const [showInfoModal, setShowInfoModal] = useState<PengajuanDana | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);
    const [filterStatus, setFilterStatus] = useState<string>('all');
    const [filterTujuan, setFilterTujuan] = useState<string>('all');
    const [showLaporanModal, setShowLaporanModal] = useState<PengajuanDana | null>(null);
    const [laporanFile, setLaporanFile] = useState<File | null>(null);
    const [isUploadingLaporan, setIsUploadingLaporan] = useState(false);
    const [laporanError, setLaporanError] = useState<string | null>(null);
    const [laporanSuccess, setLaporanSuccess] = useState<string | null>(null);
    const [form, setForm] = useState<FormState>({
        tanggal_pengajuan: '',
        tujuan_penggunaan: '',
        periode_mulai: '',
        periode_selesai: '',
        total_dana: 0,
        deskripsi_kebutuhan: '',
    });
    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [successMsg, setSuccessMsg] = useState<string | null>(null);

    // Filter data berdasarkan status pengajuan dan tujuan penggunaan
    const statusFiltered = filterStatus && filterStatus !== 'all' ? pengajuans.filter((p) => p.status === filterStatus) : pengajuans;
    const tujuanFiltered =
        filterTujuan && filterTujuan !== 'all' ? statusFiltered.filter((p) => p.tujuan_penggunaan === filterTujuan) : statusFiltered;

    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setForm({
            ...form,
            [name]: type === 'number' ? Number(value) : value,
        });
    };

    const handleSelectChange = (name: string, value: string) => {
        setForm({ ...form, [name]: value });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setForm({ ...form, [e.target.name]: e.target.files[0] });
        }
    };

    const handleTambahData = () => {
        setShowForm(true);
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleBatal = () => {
        setShowForm(false);
        setForm({
            tanggal_pengajuan: '',
            tujuan_penggunaan: '',
            periode_mulai: '',
            periode_selesai: '',
            total_dana: 0,
            deskripsi_kebutuhan: '',
        });
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorMsg(null);
        setSuccessMsg(null);

        const formData = new FormData();
        Object.keys(form).forEach((key) => {
            const value = form[key];
            if (value !== undefined && value !== '') {
                if (value instanceof File) {
                    formData.append(key, value);
                } else {
                    formData.append(key, String(value));
                }
            }
        });

        router.post('/panti/pendanaan', formData, {
            forceFormData: true,
            onSuccess: () => {
                setShowForm(false);
                setSuccessMsg('Pengajuan dana berhasil disubmit!');
                handleBatal();
            },
            onError: (errors: any) => {
                setErrorMsg('Gagal menyimpan pengajuan. Pastikan semua field wajib sudah diisi dengan benar.');
                console.error('Form errors:', errors);
            },
        });
    };

    const handleViewData = (pengajuan: PengajuanDana) => {
        setViewingPengajuan(pengajuan);
    };

    const handleInfoData = (pengajuan: PengajuanDana) => {
        setShowInfoModal(pengajuan);
    };

    const handleLaporanData = (pengajuan: PengajuanDana) => {
        setShowLaporanModal(pengajuan);
        setLaporanFile(null);
        setLaporanError(null);
        setLaporanSuccess(null);
    };

    const handleDirectUpload = (file: File, pengajuanId: number) => {
        // Reset states
        setLaporanError(null);
        setLaporanSuccess(null);

        // Validasi file size (10MB = 10 * 1024 * 1024 bytes)
        if (file.size > 10 * 1024 * 1024) {
            setLaporanError('Ukuran file terlalu besar. Maksimal 10MB.');
            return;
        }

        // Validasi file type
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (!allowedTypes.includes(file.type)) {
            setLaporanError('Format file tidak didukung. Gunakan PDF, DOC, atau DOCX.');
            return;
        }

        setIsUploadingLaporan(true);

        const formData = new FormData();
        formData.append('file_laporan_penggunaan', file);

        // Menggunakan router.post dari Inertia yang sudah menangani CSRF token
        router.post(`/panti/pendanaan/${pengajuanId}/laporan`, formData, {
            forceFormData: true,
            preserveState: true,
            preserveScroll: true,
            onSuccess: (page) => {
                setLaporanSuccess('Laporan penggunaan dana berhasil diupload!');
                setTimeout(() => {
                    setLaporanError(null);
                    setLaporanSuccess(null);
                    window.location.reload();
                }, 2000);
            },
            onError: (errors: any) => {
                console.error('Upload error:', errors);

                if (errors && typeof errors === 'object') {
                    // Handle validation errors
                    if (errors.file_laporan_penggunaan) {
                        setLaporanError(
                            Array.isArray(errors.file_laporan_penggunaan) ? errors.file_laporan_penggunaan[0] : errors.file_laporan_penggunaan,
                        );
                    } else if (errors.error) {
                        setLaporanError(Array.isArray(errors.error) ? errors.error[0] : errors.error);
                    } else if (errors.message) {
                        setLaporanError(errors.message);
                    } else {
                        const errorMessages = Object.values(errors).flat().join(', ');
                        setLaporanError(`Gagal mengupload laporan: ${errorMessages}`);
                    }
                } else if (typeof errors === 'string') {
                    setLaporanError(errors);
                } else {
                    setLaporanError('Gagal mengupload laporan. Silakan coba lagi.');
                }
            },
            onFinish: () => {
                setIsUploadingLaporan(false);
            },
        });
    };

    const handleDeleteData = (id: number) => {
        setShowDeleteConfirm(id);
    };

    const handleSubmitLaporan = () => {
        if (!showLaporanModal || !laporanFile) {
            setLaporanError('Silakan pilih file laporan terlebih dahulu.');
            return;
        }

        // Validasi file size (10MB = 10 * 1024 * 1024 bytes)
        if (laporanFile.size > 10 * 1024 * 1024) {
            setLaporanError('Ukuran file terlalu besar. Maksimal 10MB.');
            return;
        }

        // Validasi file type
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (!allowedTypes.includes(laporanFile.type)) {
            setLaporanError('Format file tidak didukung. Gunakan PDF, DOC, atau DOCX.');
            return;
        }

        setIsUploadingLaporan(true);
        setLaporanError(null);
        setLaporanSuccess(null);

        const formData = new FormData();
        formData.append('file_laporan_penggunaan', laporanFile);

        fetch(`/panti/pendanaan/${showLaporanModal.id}/laporan`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                Accept: 'application/json',
            },
        })
            .then(async (response) => {
                const data = await response.json();

                if (response.ok && data.success) {
                    setLaporanSuccess(data.message || 'Laporan penggunaan dana berhasil diupload!');
                    setTimeout(() => {
                        setShowLaporanModal(null);
                        setLaporanFile(null);
                        window.location.reload();
                    }, 2000);
                } else {
                    throw new Error(data.error || data.message || 'Upload failed');
                }
            })
            .catch((error) => {
                console.error('Upload error:', error);
                setLaporanError(error.message || 'Gagal mengupload laporan. Silakan coba lagi.');
            })
            .finally(() => {
                setIsUploadingLaporan(false);
            });
    };

    const confirmDelete = () => {
        if (showDeleteConfirm) {
            router.delete(`/panti/pendanaan/${showDeleteConfirm}`, {
                onSuccess: () => {
                    setSuccessMsg('Pengajuan dana berhasil dihapus!');
                    setShowDeleteConfirm(null);
                },
                onError: () => {
                    setErrorMsg('Gagal menghapus pengajuan dana.');
                    setShowDeleteConfirm(null);
                },
            });
        }
    };

    const formatRupiah = (nominal: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(nominal);
    };

    const formatTanggal = (tanggal: string) => {
        return new Date(tanggal).toLocaleDateString('id-ID', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'diterima':
                return 'rounded bg-green-100 px-2 py-1 text-xs font-medium text-green-600';
            case 'ditolak':
                return 'rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-700';
            default:
                return 'rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-600';
        }
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Pengajuan Dana" />
            <div className="flex flex-col gap-6 p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <h1 className="text-2xl font-bold tracking-tight text-blue-900">Data Pengajuan Dana</h1>
                    <div className="flex flex-col gap-2 md:flex-row md:items-center">
                        {/* Filter Status Pengajuan */}
                        <Select
                            value={filterStatus}
                            onValueChange={(value) => {
                                setFilterStatus(value);
                            }}
                        >
                            <SelectTrigger className="w-48 border-blue-200">
                                <SelectValue placeholder="Filter status pengajuan" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Semua Status</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                                <SelectItem value="diterima">Diterima</SelectItem>
                                <SelectItem value="ditolak">Ditolak</SelectItem>
                            </SelectContent>
                        </Select>
                        {/* Filter Tujuan Penggunaan */}
                        <Select
                            value={filterTujuan}
                            onValueChange={(value) => {
                                setFilterTujuan(value);
                            }}
                        >
                            <SelectTrigger className="w-48 border-blue-200">
                                <SelectValue placeholder="Filter tujuan penggunaan" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Semua Tujuan</SelectItem>
                                <SelectItem value="pendidikan">Pendidikan</SelectItem>
                                <SelectItem value="kesehatan">Kesehatan</SelectItem>
                                <SelectItem value="makanan pokok">Makanan Pokok</SelectItem>
                                <SelectItem value="renovasi">Renovasi</SelectItem>
                                <SelectItem value="kegiatan sosial">Kegiatan Sosial</SelectItem>
                                <SelectItem value="lainnya">Lainnya</SelectItem>
                            </SelectContent>
                        </Select>
                        {!showForm && (
                            <Button variant="default" size="sm" className="gap-2 bg-blue-600 text-white hover:bg-blue-700" onClick={handleTambahData}>
                                <Plus className="h-4 w-4" />
                                Tambah Data Pengajuan
                            </Button>
                        )}
                    </div>
                </div>

                {/* Form Pengajuan Dana */}
                {showForm && (
                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900">Form Pengajuan Dana</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {errorMsg && (
                                <div className="mb-4 rounded border border-red-200 bg-red-100 px-4 py-2 text-sm text-red-700">{errorMsg}</div>
                            )}
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="tanggal_pengajuan" className="text-blue-800">
                                            Tanggal Pengajuan *
                                        </Label>
                                        <Input
                                            id="tanggal_pengajuan"
                                            name="tanggal_pengajuan"
                                            type="date"
                                            value={form.tanggal_pengajuan}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="tujuan_penggunaan" className="text-blue-800">
                                            Tujuan Penggunaan Dana *
                                        </Label>
                                        <Select
                                            name="tujuan_penggunaan"
                                            value={form.tujuan_penggunaan}
                                            onValueChange={(value) => handleSelectChange('tujuan_penggunaan', value)}
                                        >
                                            <SelectTrigger className="border-blue-200">
                                                <SelectValue placeholder="Pilih tujuan penggunaan" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="pendidikan">Pendidikan</SelectItem>
                                                <SelectItem value="kesehatan">Kesehatan</SelectItem>
                                                <SelectItem value="makanan pokok">Makanan Pokok</SelectItem>
                                                <SelectItem value="renovasi">Renovasi</SelectItem>
                                                <SelectItem value="kegiatan sosial">Kegiatan Sosial</SelectItem>
                                                <SelectItem value="lainnya">Lainnya</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="periode_mulai" className="text-blue-800">
                                            Periode Mulai (MM/YY) *
                                        </Label>
                                        <Input
                                            id="periode_mulai"
                                            name="periode_mulai"
                                            placeholder="03/25"
                                            value={form.periode_mulai}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            pattern="^\d{2}\/\d{2}$"
                                            title="Format: MM/YY (contoh: 03/25)"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="periode_selesai" className="text-blue-800">
                                            Periode Selesai (MM/YY) *
                                        </Label>
                                        <Input
                                            id="periode_selesai"
                                            name="periode_selesai"
                                            placeholder="12/25"
                                            value={form.periode_selesai}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            pattern="^\d{2}\/\d{2}$"
                                            title="Format: MM/YY (contoh: 12/25)"
                                            required
                                        />
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="total_dana" className="text-blue-800">
                                        Total Dana Diajukan (Rp) *
                                    </Label>
                                    <Input
                                        id="total_dana"
                                        name="total_dana"
                                        type="number"
                                        min="0"
                                        step="1000"
                                        placeholder="5000000"
                                        value={form.total_dana}
                                        onChange={handleInputChange}
                                        className="border-blue-200"
                                        required
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="deskripsi_kebutuhan" className="text-blue-800">
                                        Deskripsi Singkat Kebutuhan Dana *
                                    </Label>
                                    <Textarea
                                        id="deskripsi_kebutuhan"
                                        name="deskripsi_kebutuhan"
                                        placeholder="Jelaskan secara singkat kebutuhan dana..."
                                        value={form.deskripsi_kebutuhan}
                                        onChange={handleInputChange}
                                        className="border-blue-200"
                                        rows={4}
                                        maxLength={1000}
                                        required
                                    />
                                    <p className="mt-1 text-sm text-gray-500">{form.deskripsi_kebutuhan.length}/1000 karakter</p>
                                </div>

                                {/* File Uploads */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-blue-800">Dokumen Pendukung</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="file_proposal" className="text-blue-800">
                                                Proposal Pengajuan (PDF/DOC) *
                                            </Label>
                                            <Input
                                                id="file_proposal"
                                                name="file_proposal"
                                                type="file"
                                                accept=".pdf,.doc,.docx"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 5MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_rekening" className="text-blue-800">
                                                Rekening Bank Atas Nama Panti *
                                            </Label>
                                            <Input
                                                id="file_rekening"
                                                name="file_rekening"
                                                type="file"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 2MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_ktp" className="text-blue-800">
                                                KTP Pemilik Panti *
                                            </Label>
                                            <Input
                                                id="file_ktp"
                                                name="file_ktp"
                                                type="file"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 2MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_foto_kegiatan" className="text-blue-800">
                                                Foto Kegiatan (Opsional)
                                            </Label>
                                            <Input
                                                id="file_foto_kegiatan"
                                                name="file_foto_kegiatan"
                                                type="file"
                                                accept=".jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 2MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_rab" className="text-blue-800">
                                                RAB (Rencana Anggaran Biaya) *
                                            </Label>
                                            <Input
                                                id="file_rab"
                                                name="file_rab"
                                                type="file"
                                                accept=".pdf,.doc,.docx,.xls,.xlsx"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Format: PDF, DOC, DOCX, XLS, XLSX. Maksimal 5MB</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-end gap-2">
                                    <Button type="button" variant="outline" onClick={handleBatal} className="border-blue-200">
                                        Batal
                                    </Button>
                                    <Button type="submit" className="bg-blue-600 text-white hover:bg-blue-700">
                                        Submit Pengajuan
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}

                {/* Success Message */}
                {successMsg && <div className="rounded border border-green-200 bg-green-100 px-4 py-2 text-sm text-green-700">{successMsg}</div>}

                {/* Tabel Data Pengajuan */}
                <Card className="border-blue-200">
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-12 text-center text-blue-800">No</TableHead>
                                    <TableHead className="text-blue-800">Tanggal Pengajuan</TableHead>
                                    <TableHead className="text-blue-800">Tujuan Penggunaan</TableHead>
                                    <TableHead className="text-blue-800">Total Dana Diajukan</TableHead>
                                    <TableHead className="text-blue-800">Status Pengajuan</TableHead>
                                    <TableHead className="w-32 text-center text-blue-800">Aksi</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {tujuanFiltered.length > 0 ? (
                                    tujuanFiltered.map((pengajuan: PengajuanDana, idx: number) => (
                                        <TableRow key={pengajuan.id}>
                                            <TableCell className="text-center">{pagination.from + idx}</TableCell>
                                            <TableCell>{formatTanggal(pengajuan.tanggal_pengajuan)}</TableCell>
                                            <TableCell className="capitalize">{pengajuan.tujuan_penggunaan}</TableCell>
                                            <TableCell className="font-medium">{formatRupiah(pengajuan.total_dana)}</TableCell>
                                            <TableCell>
                                                <span className={getStatusBadge(pengajuan.status)}>
                                                    {pengajuan.status.charAt(0).toUpperCase() + pengajuan.status.slice(1)}
                                                </span>
                                            </TableCell>
                                            <TableCell className="flex justify-center gap-2">
                                                <Button
                                                    size="icon"
                                                    variant="outline"
                                                    className="h-8 w-8 border-blue-500 text-blue-600 hover:bg-blue-50"
                                                    title="Lihat"
                                                    onClick={() => handleViewData(pengajuan)}
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    size="icon"
                                                    variant="outline"
                                                    className="h-8 w-8 border-green-500 text-green-600 hover:bg-green-50"
                                                    title="Info Status"
                                                    onClick={() => handleInfoData(pengajuan)}
                                                >
                                                    <Info className="h-4 w-4" />
                                                </Button>
                                                {pengajuan.status === 'pending' && (
                                                    <Button
                                                        size="icon"
                                                        variant="destructive"
                                                        className="h-8 w-8"
                                                        title="Hapus"
                                                        onClick={() => handleDeleteData(pengajuan.id)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={6} className="text-center text-gray-500">
                                            Belum ada pengajuan dana. Klik tombol "Tambah Data Pengajuan" untuk membuat pengajuan baru.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {pagination && (
                            <div className="mt-4 flex items-center justify-between px-4 pb-4">
                                <div className="text-sm text-gray-700">
                                    Menampilkan {pagination.from || 0} hingga {pagination.to || 0} dari {pagination.total} data
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page - 1 })}
                                        disabled={pagination.current_page === 1}
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        Sebelumnya
                                    </Button>
                                    <span className="flex items-center px-3 text-sm text-gray-600">
                                        Halaman {pagination.current_page} dari {Math.max(pagination.last_page, 1)}
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(window.location.pathname, { page: pagination.current_page + 1 })}
                                        disabled={pagination.current_page === pagination.last_page}
                                    >
                                        Selanjutnya
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Modal View Detail */}
                {viewingPengajuan && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 max-h-[90vh] w-full max-w-4xl overflow-y-auto border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-xl font-bold text-blue-900">Detail Pengajuan Dana</CardTitle>
                                <Button variant="ghost" size="sm" onClick={() => setViewingPengajuan(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label className="font-medium text-blue-700">Tanggal Pengajuan</Label>
                                        <p className="text-gray-900">{formatTanggal(viewingPengajuan.tanggal_pengajuan)}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Status</Label>
                                        <div className="mt-1">
                                            <span className={getStatusBadge(viewingPengajuan.status)}>
                                                {viewingPengajuan.status.charAt(0).toUpperCase() + viewingPengajuan.status.slice(1)}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Tujuan Penggunaan Dana</Label>
                                        <p className="text-gray-900 capitalize">{viewingPengajuan.tujuan_penggunaan}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Total Dana Diajukan</Label>
                                        <p className="font-semibold text-gray-900">{formatRupiah(viewingPengajuan.total_dana)}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Periode Mulai</Label>
                                        <p className="text-gray-900">{viewingPengajuan.periode_mulai}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Periode Selesai</Label>
                                        <p className="text-gray-900">{viewingPengajuan.periode_selesai}</p>
                                    </div>
                                </div>

                                <div>
                                    <Label className="font-medium text-blue-700">Deskripsi Kebutuhan Dana</Label>
                                    <p className="mt-2 whitespace-pre-wrap text-gray-900">{viewingPengajuan.deskripsi_kebutuhan}</p>
                                </div>

                                <div>
                                    <Label className="font-medium text-blue-700">Dokumen Pendukung</Label>
                                    <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-2">
                                        {viewingPengajuan.file_proposal && (
                                            <div>
                                                <p className="text-sm text-gray-600">Proposal Pengajuan:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_proposal}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Dokumen
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_rekening && (
                                            <div>
                                                <p className="text-sm text-gray-600">Rekening Bank:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_rekening}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Dokumen
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_ktp && (
                                            <div>
                                                <p className="text-sm text-gray-600">KTP Pemilik:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_ktp}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Dokumen
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_foto_kegiatan && (
                                            <div>
                                                <p className="text-sm text-gray-600">Foto Kegiatan:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_foto_kegiatan}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Foto
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_rab && (
                                            <div>
                                                <p className="text-sm text-gray-600">RAB (Rencana Anggaran Biaya):</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_rab}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Download RAB
                                                </a>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {viewingPengajuan.catatan_admin && (
                                    <div>
                                        <Label className="font-medium text-blue-700">Catatan Admin</Label>
                                        <p className="mt-2 whitespace-pre-wrap text-gray-900">{viewingPengajuan.catatan_admin}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Info Status */}
                {showInfoModal && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-green-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between pb-4">
                                <CardTitle className="text-xl font-bold text-green-600">Info Status Pengajuan</CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowInfoModal(null)} className="h-8 w-8 hover:bg-green-50">
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label className="font-medium text-green-700">Status</Label>
                                    <p className="mt-1 text-lg font-semibold text-gray-900">
                                        {showInfoModal.status.charAt(0).toUpperCase() + showInfoModal.status.slice(1)}
                                    </p>
                                </div>

                                {/* Pesan status info */}
                                {showInfoModal.status === 'diterima' && (
                                    <div>
                                        <Label className="font-medium text-green-700">Status</Label>
                                        <p className="mt-2 rounded-lg bg-green-50 p-3 font-semibold text-green-900">
                                            Selamat, pengajuan Anda diterima.
                                        </p>
                                        {showInfoModal.catatan_admin && (
                                            <div className="mt-2">
                                                <Label className="font-medium text-green-700">Catatan Admin</Label>
                                                <p className="mt-1 whitespace-pre-wrap text-gray-900">{showInfoModal.catatan_admin}</p>
                                            </div>
                                        )}

                                        {/* Status Laporan */}
                                        <div className="mt-4 border-t pt-4">
                                            <Label className="font-medium text-blue-700">Status Laporan Penggunaan Dana</Label>
                                            {showInfoModal.status_laporan === 'belum_upload' && (
                                                <div className="mt-2">
                                                    <p className="mb-3 text-sm text-orange-600">
                                                        Silakan upload laporan penggunaan dana untuk melengkapi proses pencairan.
                                                    </p>
                                                    <div className="space-y-2">
                                                        <input
                                                            type="file"
                                                            accept=".pdf,.doc,.docx"
                                                            onChange={(e) => {
                                                                const file = e.target.files?.[0];
                                                                if (file && showInfoModal) {
                                                                    handleDirectUpload(file, showInfoModal.id);
                                                                }
                                                            }}
                                                            className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-blue-50 file:px-4 file:py-2 file:text-sm file:font-medium file:text-blue-700 hover:file:bg-blue-100"
                                                            disabled={isUploadingLaporan}
                                                        />
                                                        <p className="text-xs text-gray-500">Format: PDF, DOC, DOCX. Maksimal 10MB.</p>
                                                        {isUploadingLaporan && <p className="text-sm text-blue-600">Mengupload laporan...</p>}
                                                        {laporanError && <p className="text-sm text-red-600">{laporanError}</p>}
                                                        {laporanSuccess && <p className="text-sm text-green-600">{laporanSuccess}</p>}
                                                    </div>
                                                </div>
                                            )}
                                            {showInfoModal.status_laporan === 'menunggu_verifikasi' && (
                                                <p className="mt-2 rounded-lg bg-yellow-50 p-3 text-yellow-900">
                                                    Laporan sedang dalam proses verifikasi oleh admin.
                                                </p>
                                            )}
                                            {showInfoModal.status_laporan === 'diterima' && (
                                                <p className="mt-2 rounded-lg bg-green-50 p-3 text-green-900">
                                                    Laporan penggunaan dana telah diterima dan diverifikasi.
                                                </p>
                                            )}
                                            {showInfoModal.status_laporan === 'ditolak' && (
                                                <div className="mt-2">
                                                    <p className="rounded-lg bg-red-50 p-3 text-red-900">
                                                        Laporan ditolak. Silakan upload ulang dengan perbaikan.
                                                    </p>
                                                    {showInfoModal.catatan_laporan && (
                                                        <div className="mt-2">
                                                            <Label className="font-medium text-red-700">Catatan Penolakan</Label>
                                                            <p className="mt-1 text-sm text-gray-900">{showInfoModal.catatan_laporan}</p>
                                                        </div>
                                                    )}
                                                    <div className="mt-2 space-y-2">
                                                        <input
                                                            type="file"
                                                            accept=".pdf,.doc,.docx"
                                                            onChange={(e) => {
                                                                const file = e.target.files?.[0];
                                                                if (file && showInfoModal) {
                                                                    handleDirectUpload(file, showInfoModal.id);
                                                                }
                                                            }}
                                                            className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-blue-50 file:px-4 file:py-2 file:text-sm file:font-medium file:text-blue-700 hover:file:bg-blue-100"
                                                            disabled={isUploadingLaporan}
                                                        />
                                                        <p className="text-xs text-gray-500">Format: PDF, DOC, DOCX. Maksimal 10MB.</p>
                                                        {isUploadingLaporan && <p className="text-sm text-blue-600">Mengupload laporan...</p>}
                                                        {laporanError && <p className="text-sm text-red-600">{laporanError}</p>}
                                                        {laporanSuccess && <p className="text-sm text-green-600">{laporanSuccess}</p>}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                                {showInfoModal.status === 'pending' && (
                                    <div>
                                        <Label className="font-medium text-yellow-700">Status</Label>
                                        <p className="mt-2 rounded-lg bg-yellow-50 p-3 font-semibold text-yellow-900">
                                            Pengajuan Anda sedang diproses.
                                        </p>
                                        {showInfoModal.catatan_admin && (
                                            <div className="mt-2">
                                                <Label className="font-medium text-yellow-700">Catatan Admin</Label>
                                                <p className="mt-1 whitespace-pre-wrap text-gray-900">{showInfoModal.catatan_admin}</p>
                                            </div>
                                        )}
                                    </div>
                                )}
                                {showInfoModal.status === 'ditolak' && (
                                    <div>
                                        <Label className="font-medium text-red-700">Status</Label>
                                        <p className="mt-2 rounded-lg bg-red-50 p-3 font-semibold text-red-900">Pengajuan Anda ditolak.</p>
                                        {showInfoModal.catatan_admin ? (
                                            <div className="mt-2">
                                                <Label className="font-medium text-red-700">Alasan Ditolak</Label>
                                                <p className="mt-1 whitespace-pre-wrap text-gray-900">{showInfoModal.catatan_admin}</p>
                                            </div>
                                        ) : (
                                            <p className="mt-2 text-gray-500 italic">Tidak ada alasan penolakan yang diberikan.</p>
                                        )}
                                    </div>
                                )}

                                <div className="flex justify-end">
                                    <Button variant="outline" onClick={() => setShowInfoModal(null)}>
                                        Tutup
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Upload Laporan */}
                {showLaporanModal && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between pb-4">
                                <CardTitle className="text-xl font-bold text-blue-600">Upload Laporan Penggunaan Dana</CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowLaporanModal(null)} className="h-8 w-8 hover:bg-blue-50">
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Error Message */}
                                {laporanError && <div className="rounded-lg bg-red-50 p-3 text-red-700">{laporanError}</div>}

                                {/* Success Message */}
                                {laporanSuccess && <div className="rounded-lg bg-green-50 p-3 text-green-700">{laporanSuccess}</div>}

                                <div>
                                    <Label className="font-medium text-blue-700">Pengajuan Dana</Label>
                                    <p className="mt-1 text-gray-900">
                                        {showLaporanModal.tujuan_penggunaan} - {formatCurrency(showLaporanModal.total_dana.toString())}
                                    </p>
                                </div>

                                <div>
                                    <Label htmlFor="laporan-file" className="font-medium text-blue-700">
                                        File Laporan Penggunaan Dana *
                                    </Label>
                                    <Input
                                        id="laporan-file"
                                        type="file"
                                        accept=".pdf,.doc,.docx"
                                        onChange={(e) => {
                                            try {
                                                const file = e.target.files?.[0];
                                                if (file) {
                                                    // Validasi file size
                                                    if (file.size > 10 * 1024 * 1024) {
                                                        setLaporanError('Ukuran file terlalu besar. Maksimal 10MB.');
                                                        e.target.value = ''; // Reset input
                                                        return;
                                                    }

                                                    // Validasi file type
                                                    const allowedTypes = [
                                                        'application/pdf',
                                                        'application/msword',
                                                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                                    ];
                                                    if (!allowedTypes.includes(file.type)) {
                                                        setLaporanError('Format file tidak didukung. Gunakan PDF, DOC, atau DOCX.');
                                                        e.target.value = ''; // Reset input
                                                        return;
                                                    }

                                                    setLaporanFile(file);
                                                    setLaporanError(null);
                                                } else {
                                                    setLaporanFile(null);
                                                }
                                            } catch (error) {
                                                console.error('Error handling file:', error);
                                                setLaporanError('Terjadi kesalahan saat memproses file.');
                                                e.target.value = ''; // Reset input
                                            }
                                        }}
                                        className="mt-1"
                                        disabled={isUploadingLaporan}
                                    />
                                    <p className="mt-1 text-xs text-gray-500">Format: PDF, DOC, DOCX. Maksimal 10MB.</p>
                                </div>

                                <div className="flex justify-end space-x-2">
                                    <Button variant="outline" onClick={() => setShowLaporanModal(null)} disabled={isUploadingLaporan}>
                                        Batal
                                    </Button>
                                    <Button
                                        onClick={handleSubmitLaporan}
                                        disabled={isUploadingLaporan || !laporanFile}
                                        className="bg-blue-600 hover:bg-blue-700"
                                    >
                                        {isUploadingLaporan ? 'Mengupload...' : 'Upload Laporan'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Konfirmasi Hapus */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-red-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader>
                                <CardTitle className="text-xl font-bold text-red-600">Konfirmasi Hapus</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-700">
                                    Apakah Anda yakin ingin menghapus pengajuan dana ini? Tindakan ini tidak dapat dibatalkan.
                                </p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                                        Batal
                                    </Button>
                                    <Button variant="destructive" onClick={confirmDelete}>
                                        Hapus
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </PantiLayout>
    );
}
