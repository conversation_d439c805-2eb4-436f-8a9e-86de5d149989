import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Eye, Plus, Trash2, X } from 'lucide-react';
import { ChangeEvent, FormEvent, useState } from 'react';

interface PengajuanDana {
    id: number;
    tanggal_pengajuan: string;
    tujuan_penggunaan: string;
    periode_mulai: string;
    periode_selesai: string;
    total_dana: number;
    deskripsi_kebutuhan: string;
    file_proposal?: string;
    file_rekening?: string;
    file_ktp?: string;
    file_foto_kegiatan?: string;
    status: string;
    catatan_admin?: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pengajuans: PengajuanDana[];
}

interface FormState {
    [key: string]: string | number | File | undefined;
    tanggal_pengajuan: string;
    tujuan_penggunaan: string;
    periode_mulai: string;
    periode_selesai: string;
    total_dana: number;
    deskripsi_kebutuhan: string;
    file_proposal?: File;
    file_rekening?: File;
    file_ktp?: File;
    file_foto_kegiatan?: File;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Pendanaan',
        href: '/panti/pendanaan',
    },
];

export default function Pendanaan({ user, pengajuans }: Props) {
    const [showForm, setShowForm] = useState(false);
    const [viewingPengajuan, setViewingPengajuan] = useState<PengajuanDana | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);
    const [form, setForm] = useState<FormState>({
        tanggal_pengajuan: '',
        tujuan_penggunaan: '',
        periode_mulai: '',
        periode_selesai: '',
        total_dana: 0,
        deskripsi_kebutuhan: '',
    });
    const [errorMsg, setErrorMsg] = useState<string | null>(null);
    const [successMsg, setSuccessMsg] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const perPage = 10;
    const totalPage = Math.ceil(pengajuans.length / perPage);
    const pagedData = pengajuans.slice((currentPage - 1) * perPage, currentPage * perPage);

    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        setForm({
            ...form,
            [name]: type === 'number' ? Number(value) : value,
        });
    };

    const handleSelectChange = (name: string, value: string) => {
        setForm({ ...form, [name]: value });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setForm({ ...form, [e.target.name]: e.target.files[0] });
        }
    };

    const handleTambahData = () => {
        setShowForm(true);
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleBatal = () => {
        setShowForm(false);
        setForm({
            tanggal_pengajuan: '',
            tujuan_penggunaan: '',
            periode_mulai: '',
            periode_selesai: '',
            total_dana: 0,
            deskripsi_kebutuhan: '',
        });
        setErrorMsg(null);
        setSuccessMsg(null);
    };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorMsg(null);
        setSuccessMsg(null);

        const formData = new FormData();
        Object.keys(form).forEach((key) => {
            const value = form[key];
            if (value !== undefined && value !== '') {
                if (value instanceof File) {
                    formData.append(key, value);
                } else {
                    formData.append(key, String(value));
                }
            }
        });

        router.post('/panti/pendanaan', formData, {
            forceFormData: true,
            onSuccess: () => {
                setShowForm(false);
                setSuccessMsg('Pengajuan dana berhasil disubmit!');
                handleBatal();
            },
            onError: (errors: any) => {
                setErrorMsg('Gagal menyimpan pengajuan. Pastikan semua field wajib sudah diisi dengan benar.');
                console.error('Form errors:', errors);
            },
        });
    };

    const handleViewData = (pengajuan: PengajuanDana) => {
        setViewingPengajuan(pengajuan);
    };

    const handleDeleteData = (id: number) => {
        setShowDeleteConfirm(id);
    };

    const confirmDelete = () => {
        if (showDeleteConfirm) {
            router.delete(`/panti/pendanaan/${showDeleteConfirm}`, {
                onSuccess: () => {
                    setSuccessMsg('Pengajuan dana berhasil dihapus!');
                    setShowDeleteConfirm(null);
                },
                onError: () => {
                    setErrorMsg('Gagal menghapus pengajuan dana.');
                    setShowDeleteConfirm(null);
                },
            });
        }
    };

    const formatRupiah = (nominal: number) => {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0,
        }).format(nominal);
    };

    const formatTanggal = (tanggal: string) => {
        return new Date(tanggal).toLocaleDateString('id-ID', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit',
        });
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'diterima':
                return 'rounded bg-green-100 px-2 py-1 text-xs font-medium text-green-600';
            case 'ditolak':
                return 'rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-700';
            default:
                return 'rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-600';
        }
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Pengajuan Dana" />
            <div className="flex flex-col gap-6 p-6">
                <div className="flex items-center justify-between">
                    <h1 className="text-2xl font-bold tracking-tight text-blue-900">Data Pengajuan Dana</h1>
                    {!showForm && (
                        <Button variant="default" size="sm" className="gap-2 bg-blue-600 text-white hover:bg-blue-700" onClick={handleTambahData}>
                            <Plus className="h-4 w-4" />
                            Tambah Data Pengajuan
                        </Button>
                    )}
                </div>

                {/* Form Pengajuan Dana */}
                {showForm && (
                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900">Form Pengajuan Dana</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {errorMsg && (
                                <div className="mb-4 rounded border border-red-200 bg-red-100 px-4 py-2 text-sm text-red-700">{errorMsg}</div>
                            )}
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="tanggal_pengajuan" className="text-blue-800">
                                            Tanggal Pengajuan *
                                        </Label>
                                        <Input
                                            id="tanggal_pengajuan"
                                            name="tanggal_pengajuan"
                                            type="date"
                                            value={form.tanggal_pengajuan}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="tujuan_penggunaan" className="text-blue-800">
                                            Tujuan Penggunaan Dana *
                                        </Label>
                                        <Select
                                            name="tujuan_penggunaan"
                                            value={form.tujuan_penggunaan}
                                            onValueChange={(value) => handleSelectChange('tujuan_penggunaan', value)}
                                        >
                                            <SelectTrigger className="border-blue-200">
                                                <SelectValue placeholder="Pilih tujuan penggunaan" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="pendidikan">Pendidikan</SelectItem>
                                                <SelectItem value="kesehatan">Kesehatan</SelectItem>
                                                <SelectItem value="makanan pokok">Makanan Pokok</SelectItem>
                                                <SelectItem value="renovasi">Renovasi</SelectItem>
                                                <SelectItem value="kegiatan sosial">Kegiatan Sosial</SelectItem>
                                                <SelectItem value="lainnya">Lainnya</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="periode_mulai" className="text-blue-800">
                                            Periode Mulai (MM/YY) *
                                        </Label>
                                        <Input
                                            id="periode_mulai"
                                            name="periode_mulai"
                                            placeholder="03/25"
                                            value={form.periode_mulai}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            pattern="^\d{2}\/\d{2}$"
                                            title="Format: MM/YY (contoh: 03/25)"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="periode_selesai" className="text-blue-800">
                                            Periode Selesai (MM/YY) *
                                        </Label>
                                        <Input
                                            id="periode_selesai"
                                            name="periode_selesai"
                                            placeholder="12/25"
                                            value={form.periode_selesai}
                                            onChange={handleInputChange}
                                            className="border-blue-200"
                                            pattern="^\d{2}\/\d{2}$"
                                            title="Format: MM/YY (contoh: 12/25)"
                                            required
                                        />
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="total_dana" className="text-blue-800">
                                        Total Dana Diajukan (Rp) *
                                    </Label>
                                    <Input
                                        id="total_dana"
                                        name="total_dana"
                                        type="number"
                                        min="0"
                                        step="1000"
                                        placeholder="5000000"
                                        value={form.total_dana}
                                        onChange={handleInputChange}
                                        className="border-blue-200"
                                        required
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="deskripsi_kebutuhan" className="text-blue-800">
                                        Deskripsi Singkat Kebutuhan Dana *
                                    </Label>
                                    <Textarea
                                        id="deskripsi_kebutuhan"
                                        name="deskripsi_kebutuhan"
                                        placeholder="Jelaskan secara singkat kebutuhan dana..."
                                        value={form.deskripsi_kebutuhan}
                                        onChange={handleInputChange}
                                        className="border-blue-200"
                                        rows={4}
                                        maxLength={1000}
                                        required
                                    />
                                    <p className="mt-1 text-sm text-gray-500">{form.deskripsi_kebutuhan.length}/1000 karakter</p>
                                </div>

                                {/* File Uploads */}
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-blue-800">Dokumen Pendukung</h3>
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="file_proposal" className="text-blue-800">
                                                Proposal Pengajuan (PDF/DOC) *
                                            </Label>
                                            <Input
                                                id="file_proposal"
                                                name="file_proposal"
                                                type="file"
                                                accept=".pdf,.doc,.docx"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 5MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_rekening" className="text-blue-800">
                                                Rekening Bank Atas Nama Panti *
                                            </Label>
                                            <Input
                                                id="file_rekening"
                                                name="file_rekening"
                                                type="file"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 2MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_ktp" className="text-blue-800">
                                                KTP Pemilik Panti *
                                            </Label>
                                            <Input
                                                id="file_ktp"
                                                name="file_ktp"
                                                type="file"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                                required
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 2MB</p>
                                        </div>
                                        <div>
                                            <Label htmlFor="file_foto_kegiatan" className="text-blue-800">
                                                Foto Kegiatan (Opsional)
                                            </Label>
                                            <Input
                                                id="file_foto_kegiatan"
                                                name="file_foto_kegiatan"
                                                type="file"
                                                accept=".jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className="border-blue-200"
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Maksimal 2MB</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-end gap-2">
                                    <Button type="button" variant="outline" onClick={handleBatal} className="border-blue-200">
                                        Batal
                                    </Button>
                                    <Button type="submit" className="bg-blue-600 text-white hover:bg-blue-700">
                                        Submit Pengajuan
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                )}

                {/* Success Message */}
                {successMsg && <div className="rounded border border-green-200 bg-green-100 px-4 py-2 text-sm text-green-700">{successMsg}</div>}

                {/* Tabel Data Pengajuan */}
                <Card className="border-blue-200">
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-12 text-center text-blue-800">No</TableHead>
                                    <TableHead className="text-blue-800">Tanggal Pengajuan</TableHead>
                                    <TableHead className="text-blue-800">Tujuan Penggunaan</TableHead>
                                    <TableHead className="text-blue-800">Total Dana Diajukan</TableHead>
                                    <TableHead className="text-blue-800">Status Pengajuan</TableHead>
                                    <TableHead className="w-32 text-center text-blue-800">Aksi</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {pagedData.length > 0 ? (
                                    pagedData.map((pengajuan, idx) => (
                                        <TableRow key={pengajuan.id}>
                                            <TableCell className="text-center">{(currentPage - 1) * perPage + idx + 1}</TableCell>
                                            <TableCell>{formatTanggal(pengajuan.tanggal_pengajuan)}</TableCell>
                                            <TableCell className="capitalize">{pengajuan.tujuan_penggunaan}</TableCell>
                                            <TableCell className="font-medium">{formatRupiah(pengajuan.total_dana)}</TableCell>
                                            <TableCell>
                                                <span className={getStatusBadge(pengajuan.status)}>
                                                    {pengajuan.status.charAt(0).toUpperCase() + pengajuan.status.slice(1)}
                                                </span>
                                            </TableCell>
                                            <TableCell className="flex justify-center gap-2">
                                                <Button
                                                    size="icon"
                                                    variant="outline"
                                                    className="h-8 w-8 border-blue-500 text-blue-600 hover:bg-blue-50"
                                                    title="Lihat"
                                                    onClick={() => handleViewData(pengajuan)}
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                                {pengajuan.status === 'pending' && (
                                                    <Button
                                                        size="icon"
                                                        variant="destructive"
                                                        className="h-8 w-8"
                                                        title="Hapus"
                                                        onClick={() => handleDeleteData(pengajuan.id)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={6} className="text-center text-gray-500">
                                            Belum ada pengajuan dana. Klik tombol "Tambah Data Pengajuan" untuk membuat pengajuan baru.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {totalPage > 1 && (
                            <div className="mt-4 flex items-center justify-between px-4 pb-4">
                                <div className="text-sm text-gray-700">
                                    Menampilkan {(currentPage - 1) * perPage + 1} hingga {Math.min(currentPage * perPage, pengajuans.length)} dari{' '}
                                    {pengajuans.length} data
                                </div>
                                <div className="flex gap-2">
                                    <Button variant="outline" size="sm" onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1}>
                                        Sebelumnya
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setCurrentPage(currentPage + 1)}
                                        disabled={currentPage === totalPage}
                                    >
                                        Selanjutnya
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Modal View Detail */}
                {viewingPengajuan && (
                    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                        <Card className="m-4 max-h-[90vh] w-full max-w-4xl overflow-y-auto">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-xl font-bold text-blue-900">Detail Pengajuan Dana</CardTitle>
                                <Button variant="ghost" size="sm" onClick={() => setViewingPengajuan(null)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label className="font-medium text-blue-700">Tanggal Pengajuan</Label>
                                        <p className="text-gray-900">{formatTanggal(viewingPengajuan.tanggal_pengajuan)}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Status</Label>
                                        <div className="mt-1">
                                            <span className={getStatusBadge(viewingPengajuan.status)}>
                                                {viewingPengajuan.status.charAt(0).toUpperCase() + viewingPengajuan.status.slice(1)}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Tujuan Penggunaan Dana</Label>
                                        <p className="text-gray-900 capitalize">{viewingPengajuan.tujuan_penggunaan}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Total Dana Diajukan</Label>
                                        <p className="font-semibold text-gray-900">{formatRupiah(viewingPengajuan.total_dana)}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Periode Mulai</Label>
                                        <p className="text-gray-900">{viewingPengajuan.periode_mulai}</p>
                                    </div>
                                    <div>
                                        <Label className="font-medium text-blue-700">Periode Selesai</Label>
                                        <p className="text-gray-900">{viewingPengajuan.periode_selesai}</p>
                                    </div>
                                </div>

                                <div>
                                    <Label className="font-medium text-blue-700">Deskripsi Kebutuhan Dana</Label>
                                    <p className="mt-2 whitespace-pre-wrap text-gray-900">{viewingPengajuan.deskripsi_kebutuhan}</p>
                                </div>

                                <div>
                                    <Label className="font-medium text-blue-700">Dokumen Pendukung</Label>
                                    <div className="mt-2 grid grid-cols-1 gap-4 md:grid-cols-2">
                                        {viewingPengajuan.file_proposal && (
                                            <div>
                                                <p className="text-sm text-gray-600">Proposal Pengajuan:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_proposal}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Dokumen
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_rekening && (
                                            <div>
                                                <p className="text-sm text-gray-600">Rekening Bank:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_rekening}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Dokumen
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_ktp && (
                                            <div>
                                                <p className="text-sm text-gray-600">KTP Pemilik:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_ktp}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Dokumen
                                                </a>
                                            </div>
                                        )}
                                        {viewingPengajuan.file_foto_kegiatan && (
                                            <div>
                                                <p className="text-sm text-gray-600">Foto Kegiatan:</p>
                                                <a
                                                    href={`/storage/${viewingPengajuan.file_foto_kegiatan}`}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline"
                                                >
                                                    Lihat Foto
                                                </a>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {viewingPengajuan.catatan_admin && (
                                    <div>
                                        <Label className="font-medium text-blue-700">Catatan Admin</Label>
                                        <p className="mt-2 whitespace-pre-wrap text-gray-900">{viewingPengajuan.catatan_admin}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Modal Konfirmasi Hapus */}
                {showDeleteConfirm && (
                    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
                        <Card className="m-4 w-full max-w-md">
                            <CardHeader>
                                <CardTitle className="text-xl font-bold text-red-600">Konfirmasi Hapus</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <p className="text-gray-700">
                                    Apakah Anda yakin ingin menghapus pengajuan dana ini? Tindakan ini tidak dapat dibatalkan.
                                </p>
                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={() => setShowDeleteConfirm(null)}>
                                        Batal
                                    </Button>
                                    <Button variant="destructive" onClick={confirmDelete}>
                                        Hapus
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </PantiLayout>
    );
}
