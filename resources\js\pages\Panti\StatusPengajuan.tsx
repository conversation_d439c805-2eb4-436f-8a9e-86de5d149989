import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import PantiLayout from '@/layouts/panti-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, useForm } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Clock, CreditCard, Download, Edit, FileText, Info, Search, Upload, X } from 'lucide-react';
import { useMemo, useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Bantuan <PERSON>',
        href: '/panti/bansos',
    },
    {
        title: 'Status Pengajuan',
        href: '/panti/statuspengajuan',
    },
];

// Interface untuk dokumen yang perlu diupload ulang
interface RejectedDocument {
    id: number;
    nama_dokumen: string;
    status_verifikasi: 'ditolak';
    alasan_penolakan: string;
    file_path?: string;
}

// Helper to get all dokumen verifikasi for a pengajuan, grouped by status
const getDokumenVerifikasi = (pengajuan: any) => {
    // Support both camelCase and snake_case from backend
    if (pengajuan.dokumenVerifikasis && Array.isArray(pengajuan.dokumenVerifikasis)) {
        return pengajuan.dokumenVerifikasis;
    }
    if (pengajuan.dokumen_verifikasis && Array.isArray(pengajuan.dokumen_verifikasis)) {
        return pengajuan.dokumen_verifikasis;
    }
    return [];
};

interface Props {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    pengajuanList: Array<any>; // dokumenVerifikasis now included
}

export default function StatusPengajuan({ user, pengajuanList }: Props) {
    const [showInfoModal, setShowInfoModal] = useState<any>(null);
    const [showPencairanModal, setShowPencairanModal] = useState(false);
    const [selectedPengajuan, setSelectedPengajuan] = useState<any>(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editPengajuan, setEditPengajuan] = useState<any>(null);
    const [uploadedFiles, setUploadedFiles] = useState<{ [key: number]: File }>({});

    // Search and pagination state
    const [searchStatus, setSearchStatus] = useState<string>('');
    const [searchText, setSearchText] = useState<string>('');
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    // Filter and paginate data
    const filteredData = useMemo(() => {
        let filtered = pengajuanList;

        // Filter by status
        if (searchStatus && searchStatus !== 'all') {
            filtered = filtered.filter((item) => item.status?.toLowerCase().includes(searchStatus.toLowerCase()));
        }

        // Filter by text search (nama panti, kabupaten_kota, tanggal)
        if (searchText.trim()) {
            const searchLower = searchText.toLowerCase();
            filtered = filtered.filter(
                (item) =>
                    item.nama_panti?.toLowerCase().includes(searchLower) ||
                    item.kabupaten_kota?.toLowerCase().includes(searchLower) ||
                    item.tanggal_pengajuan?.toLowerCase().includes(searchLower) ||
                    item.status?.toLowerCase().includes(searchLower) ||
                    item.keterangan?.toLowerCase().includes(searchLower),
            );
        }

        return filtered;
    }, [pengajuanList, searchStatus, searchText]);

    const totalPages = Math.max(1, Math.ceil(filteredData.length / itemsPerPage));
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginatedData = filteredData.slice(startIndex, startIndex + itemsPerPage);

    // Reset to first page when search changes
    const handleSearchChange = (value: string) => {
        setSearchStatus(value);
        setCurrentPage(1);
    };

    const handleTextSearchChange = (value: string) => {
        setSearchText(value);
        setCurrentPage(1);
    };

    const { data, setData, put, processing, errors, reset } = useForm({
        nama_panti: '',
        alamat: '',
        nomor_telepon: '',
        nama_ketua: '',
        jumlah_anak_asuh: 0,
        jumlah_dana_dibutuhkan: 0,
        keterangan: '',
        documents: {} as { [key: number]: File },
    });

    const getStatusColor = (status: string) => {
        switch (status?.toLowerCase()) {
            case 'diterima':
                return 'bg-green-100 text-green-700';
            case 'ditolak':
                return 'bg-red-100 text-red-700';
            case 'diproses':
            case 'pending':
                return 'bg-yellow-100 text-yellow-700';
            default:
                return 'bg-gray-100 text-gray-700';
        }
    };

    const getStatusInfo = (pengajuan: any) => {
        const status = pengajuan.status?.toLowerCase().trim();

        switch (status) {
            case 'diterima':
                return {
                    title: '✅ Pengajuan Diterima',
                    message: `Selamat! Pengajuan bantuan sosial untuk ${pengajuan.nama_panti} telah disetujui dan diterima oleh Dinas Sosial.\n\n📅 Tanggal Persetujuan: ${formatDate(pengajuan.created_at)}\n💰 Dana bantuan akan segera dicairkan ke rekening panti dalam 3-5 hari kerja.\n\n${pengajuan.keterangan ? `📝 Catatan: ${pengajuan.keterangan}` : 'Terima kasih atas kesabaran Anda dalam menunggu proses verifikasi.'}`,
                    color: 'text-green-600',
                };
            case 'ditolak':
                return {
                    title: '❌ Pengajuan Ditolak',
                    message: `Mohon maaf, pengajuan bantuan sosial untuk ${pengajuan.nama_panti} tidak dapat disetujui.\n\n📅 Tanggal Keputusan: ${formatDate(pengajuan.created_at)}\n\n❗ Alasan Penolakan:\n${pengajuan.keterangan || 'Dokumen persyaratan belum lengkap atau tidak memenuhi kriteria yang ditetapkan.'}\n\n🔄 Silakan perbaiki hal-hal yang disebutkan di atas dan ajukan kembali pengajuan Anda melalui tombol Edit.`,
                    color: 'text-red-600',
                };
            case 'diproses':
            case 'pending':
            case 'dalam proses':
            case 'menunggu':
                return {
                    title: '⏳ Sedang Diproses',
                    message: `Pengajuan bantuan sosial untuk ${pengajuan.nama_panti} sedang dalam tahap verifikasi oleh tim reviewer Dinas Sosial.\n\n📅 Tanggal Pengajuan: ${formatDate(pengajuan.created_at)}\n⏱️ Estimasi Proses: 7-14 hari kerja\n\n📞 Jika ada pertanyaan, silakan hubungi customer service kami.\n\nMohon bersabar menunggu hasil verifikasi.`,
                    color: 'text-yellow-600',
                };
            default:
                return {
                    title: '❓ Status Tidak Diketahui',
                    message: `Status pengajuan tidak dapat ditentukan (Status: "${pengajuan.status}").\n\nSilakan hubungi administrator sistem atau refresh halaman ini.\n\n📞 Jika masalah berlanjut, silakan hubungi customer service untuk bantuan lebih lanjut.`,
                    color: 'text-gray-600',
                };
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID');
    };

    const handleEdit = (pengajuan: any) => {
        setEditPengajuan(pengajuan);
        setData({
            nama_panti: pengajuan.nama_panti || '',
            alamat: pengajuan.alamat || '',
            nomor_telepon: pengajuan.nomor_telepon || '',
            nama_ketua: pengajuan.nama_ketua || '',
            jumlah_anak_asuh: pengajuan.jumlah_anak_asuh || 0,
            jumlah_dana_dibutuhkan: pengajuan.jumlah_dana_dibutuhkan || 0,
            keterangan: pengajuan.keterangan || '',
            documents: {},
        });

        setShowEditModal(true);
    };

    const handleSubmitEdit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editPengajuan) {
            // Include uploaded files in the form data
            const formData = { ...data };
            if (Object.keys(uploadedFiles).length > 0) {
                formData.documents = uploadedFiles;
            }

            put(route('panti.bansos.update', editPengajuan.id), {
                onSuccess: () => {
                    setShowEditModal(false);
                    setUploadedFiles({});
                    reset();
                    setEditPengajuan(null);
                },
                onError: (errors) => {
                    console.log(errors);
                },
            });
        }
    };

    const handleFileUpload = (documentId: number, file: File) => {
        setUploadedFiles((prev) => ({
            ...prev,
            [documentId]: file,
        }));

        // Update form data
        setData('documents', {
            ...data.documents,
            [documentId]: file,
        });
    };

    const removeUploadedFile = (documentId: number) => {
        const updatedFiles = { ...uploadedFiles };
        delete updatedFiles[documentId];
        setUploadedFiles(updatedFiles);

        // Update form data
        const updatedDocuments = { ...data.documents };
        delete updatedDocuments[documentId];
        setData('documents', updatedDocuments);
    };

    const handleInfo = (pengajuan: any) => {
        setShowInfoModal(pengajuan);
    };

    const handlePencairan = () => {
        // Ambil semua pengajuan yang diterima dan memiliki file pencairan
        const pengajuanDiterima = pengajuanList.filter((p) => p.status.toLowerCase() === 'diterima');
        setSelectedPengajuan(pengajuanDiterima);
        setShowPencairanModal(true);
    };

    const getJenisFileLabel = (jenisFile: string) => {
        const labels: { [key: string]: string } = {
            bukti_transfer: 'Bukti Transfer',
            surat_pencairan: 'Surat Pencairan',
            dokumen_pendukung: 'Dokumen Pendukung',
        };
        return labels[jenisFile] || jenisFile;
    };

    return (
        <PantiLayout breadcrumbs={breadcrumbs}>
            <Head title="Status Pengajuan Bantuan Sosial" />
            <div className="flex h-full flex-1 flex-col space-y-6 px-4 py-6 lg:px-8">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Status Pengajuan Bantuan Sosial</h1>
                        <p className="text-muted-foreground">Pantau status pengajuan bantuan sosial Anda</p>
                    </div>
                </div>

                {/* Search and Filter Section */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-4 w-4" />
                            Pencarian dan Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                                <label htmlFor="search-text" className="text-sm font-medium text-gray-700">
                                    Pencarian
                                </label>
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                                    <Input
                                        id="search-text"
                                        type="text"
                                        placeholder="Cari nama panti, kabupaten, tanggal..."
                                        value={searchText}
                                        onChange={(e) => handleTextSearchChange(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="search-status" className="text-sm font-medium text-gray-700">
                                    Filter berdasarkan Status
                                </label>
                                <Select value={searchStatus} onValueChange={handleSearchChange}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Pilih status untuk filter" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="ditolak">Ditolak</SelectItem>
                                        <SelectItem value="diterima">Diterima</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="mt-4 flex items-center justify-between">
                            <div className="text-sm text-gray-600">
                                Menampilkan {paginatedData.length} dari {filteredData.length} pengajuan
                            </div>
                            {(searchText || searchStatus) && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        setSearchText('');
                                        setSearchStatus('');
                                        setCurrentPage(1);
                                    }}
                                >
                                    <X className="mr-2 h-4 w-4" />
                                    Reset Filter
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Daftar Pengajuan Bantuan Sosial</CardTitle>
                        <CardDescription>Berikut adalah daftar semua pengajuan bantuan sosial yang telah diajukan</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-12">No</TableHead>
                                    <TableHead>Nama Panti</TableHead>
                                    <TableHead>Kabupaten/Kota</TableHead>
                                    <TableHead>Tanggal</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-center">Aksi</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paginatedData.length > 0 ? (
                                    paginatedData.map((pengajuan, index) => (
                                        <TableRow key={pengajuan.id}>
                                            <TableCell className="font-medium">{startIndex + index + 1}</TableCell>
                                            <TableCell className="font-medium">{pengajuan.nama_panti}</TableCell>
                                            <TableCell>{pengajuan.kabupaten_kota}</TableCell>
                                            <TableCell>{formatDate(pengajuan.tanggal_pengajuan || pengajuan.created_at)}</TableCell>
                                            <TableCell>
                                                <span
                                                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(pengajuan.status)}`}
                                                >
                                                    {pengajuan.status}
                                                </span>
                                            </TableCell>
                                            <TableCell className="text-center">
                                                <div className="flex items-center justify-center gap-2">
                                                    {/* Edit button only shows when status is ditolak */}
                                                    {pengajuan.status?.toLowerCase().trim() === 'ditolak' && (
                                                        <Button
                                                            size="icon"
                                                            variant="outline"
                                                            className="h-8 w-8 border-blue-500 text-blue-600 hover:bg-blue-50"
                                                            title="Edit & Ajukan Ulang"
                                                            onClick={() => handleEdit(pengajuan)}
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                    <Button
                                                        size="icon"
                                                        variant="outline"
                                                        className="h-8 w-8 border-green-500 text-green-600 hover:bg-green-50"
                                                        title="Info Status"
                                                        onClick={() => handleInfo(pengajuan)}
                                                    >
                                                        <Info className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={6} className="py-8 text-center">
                                            <div className="text-gray-500">
                                                {searchStatus && searchStatus !== 'all'
                                                    ? `Tidak ada pengajuan dengan status "${searchStatus}"`
                                                    : 'Belum ada pengajuan bantuan sosial'}
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>

                        {/* Pagination - Always Show */}
                        <div className="mt-6 flex items-center justify-between border-t pt-4">
                            <div className="text-sm text-gray-600">
                                Halaman {currentPage} dari {totalPages} | Total: {filteredData.length} pengajuan
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                    disabled={currentPage === 1 || totalPages <= 1}
                                >
                                    Sebelumnya
                                </Button>

                                {/* Page numbers - Always show at least page 1 */}
                                <div className="flex items-center gap-1">
                                    {Array.from({ length: Math.max(1, totalPages) }, (_, i) => i + 1).map((page) => (
                                        <Button
                                            key={page}
                                            variant={currentPage === page ? 'default' : 'outline'}
                                            size="sm"
                                            onClick={() => setCurrentPage(page)}
                                            className="h-8 w-8"
                                            disabled={totalPages <= 1}
                                        >
                                            {page}
                                        </Button>
                                    ))}
                                </div>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                    disabled={currentPage === totalPages || totalPages <= 1}
                                >
                                    Selanjutnya
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Bottom Action Buttons */}
                <div className="flex items-center justify-between">
                    <Button variant="outline" asChild>
                        <a href="/panti/bansos" className="flex items-center gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Kembali
                        </a>
                    </Button>

                    <Button onClick={handlePencairan} className="bg-blue-600 text-white hover:bg-blue-700">
                        <CreditCard className="mr-2 h-4 w-4" />
                        Lihat Informasi Pencairan
                    </Button>
                </div>

                {/* Info Modal */}
                {showInfoModal && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/20 backdrop-blur-sm">
                        <Card className="m-4 w-full max-w-md border border-blue-200/50 bg-white/95 shadow-2xl backdrop-blur-md">
                            <CardHeader className="flex flex-row items-center justify-between pb-4">
                                <CardTitle className={`text-xl font-bold ${getStatusInfo(showInfoModal).color}`}>
                                    {getStatusInfo(showInfoModal).title}
                                </CardTitle>
                                <Button variant="ghost" size="icon" onClick={() => setShowInfoModal(null)} className="h-8 w-8 hover:bg-blue-50">
                                    ×
                                </Button>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Nama Panti</p>
                                    <p className="text-lg font-semibold text-gray-900">{showInfoModal.nama_panti}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-gray-700">Informasi Status</p>
                                    <div className="mt-2 text-sm leading-relaxed whitespace-pre-line text-gray-700">
                                        {getStatusInfo(showInfoModal).message}
                                    </div>
                                </div>
                                <div className="flex justify-end">
                                    <Button
                                        variant="outline"
                                        onClick={() => setShowInfoModal(null)}
                                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                    >
                                        Tutup
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Pencairan Modal */}
                <Dialog open={showPencairanModal} onOpenChange={setShowPencairanModal}>
                    <DialogContent className="flex max-h-[90vh] max-w-[95vw] flex-col overflow-hidden lg:max-w-7xl">
                        <DialogHeader className="flex-shrink-0">
                            <DialogTitle className="text-xl font-bold text-gray-900">Informasi Pencairan Bantuan Sosial</DialogTitle>
                            <p className="mt-2 text-sm text-gray-600">
                                Berikut adalah daftar pengajuan yang telah disetujui beserta dokumen pencairan yang telah diupload oleh Dinas Sosial
                                Provinsi
                            </p>
                        </DialogHeader>
                        <div className="mt-4 flex-1 overflow-auto">
                            {selectedPengajuan && selectedPengajuan.length > 0 ? (
                                <div className="overflow-hidden rounded-lg border border-blue-200">
                                    <div className="overflow-x-auto">
                                        <Table className="min-w-full">
                                            <TableHeader className="sticky top-0 z-10">
                                                <TableRow className="bg-blue-50">
                                                    <TableHead className="w-16 text-center font-semibold text-blue-800">No</TableHead>
                                                    <TableHead className="min-w-[200px] font-semibold text-blue-800">Nama Panti</TableHead>
                                                    <TableHead className="min-w-[150px] font-semibold text-blue-800">Kabupaten/Kota</TableHead>
                                                    <TableHead className="min-w-[120px] font-semibold text-blue-800">Tanggal</TableHead>
                                                    <TableHead className="min-w-[250px] text-center font-semibold text-blue-800">
                                                        Dokumen Pencairan
                                                    </TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {selectedPengajuan.map((pengajuan: any, index: number) => (
                                                    <TableRow key={pengajuan.id} className="transition-colors hover:bg-blue-50">
                                                        <TableCell className="text-center font-medium text-blue-900">{index + 1}</TableCell>
                                                        <TableCell className="font-medium">{pengajuan.nama_panti}</TableCell>
                                                        <TableCell>{pengajuan.kabupaten_kota}</TableCell>
                                                        <TableCell className="whitespace-nowrap">
                                                            {formatDate(pengajuan.tanggal_pengajuan || pengajuan.created_at)}
                                                        </TableCell>
                                                        <TableCell className="text-center">
                                                            {pengajuan.file_pencairans && pengajuan.file_pencairans.length > 0 ? (
                                                                <div className="flex min-w-[250px] flex-col gap-3">
                                                                    {pengajuan.file_pencairans.map((file: any) => (
                                                                        <div
                                                                            key={file.id}
                                                                            className="flex items-center justify-between gap-2 rounded-lg bg-gray-50 p-2"
                                                                        >
                                                                            <div className="min-w-0 flex-1 text-left">
                                                                                <p
                                                                                    className="truncate text-sm font-medium text-gray-900"
                                                                                    title={file.nama_file}
                                                                                >
                                                                                    {file.nama_file}
                                                                                </p>
                                                                                <p className="text-xs text-gray-500">
                                                                                    {getJenisFileLabel(file.jenis_file)}
                                                                                </p>
                                                                            </div>
                                                                            <div className="flex flex-shrink-0 gap-1">
                                                                                <Button
                                                                                    size="sm"
                                                                                    variant="outline"
                                                                                    className="h-7 border-blue-500 px-2 text-xs text-blue-600 hover:bg-blue-50"
                                                                                    onClick={() =>
                                                                                        window.open(`/storage/${file.file_path}`, '_blank')
                                                                                    }
                                                                                    title="Lihat Dokumen"
                                                                                >
                                                                                    <FileText className="mr-1 h-3 w-3" />
                                                                                    View
                                                                                </Button>
                                                                                <Button
                                                                                    size="sm"
                                                                                    variant="outline"
                                                                                    className="h-7 border-green-500 px-2 text-xs text-green-600 hover:bg-green-50"
                                                                                    asChild
                                                                                >
                                                                                    <a
                                                                                        href={`/storage/${file.file_path}`}
                                                                                        download={file.nama_file}
                                                                                        className="flex items-center"
                                                                                        title="Download Dokumen"
                                                                                    >
                                                                                        <Download className="mr-1 h-3 w-3" />
                                                                                        Download
                                                                                    </a>
                                                                                </Button>
                                                                            </div>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            ) : (
                                                                <div className="flex flex-col items-center py-4">
                                                                    <Clock className="mb-1 h-6 w-6 text-gray-400" />
                                                                    <p className="text-center text-xs text-gray-500">
                                                                        Belum ada dokumen
                                                                        <br />
                                                                        pencairan
                                                                    </p>
                                                                </div>
                                                            )}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                </div>
                            ) : (
                                <div className="rounded-lg border-2 border-dashed border-gray-300 py-12 text-center">
                                    <CreditCard className="mx-auto mb-4 h-16 w-16 text-gray-400" />
                                    <h3 className="mb-2 text-lg font-medium text-gray-900">Belum Ada Pengajuan yang Disetujui</h3>
                                    <p className="mb-1 text-gray-500">Informasi pencairan akan tersedia setelah pengajuan disetujui</p>
                                    <p className="text-sm text-gray-400">Dokumen pencairan akan diupload oleh Dinas Sosial Provinsi</p>
                                </div>
                            )}
                        </div>

                        {/* Footer dengan Button Upload SPJ */}
                        <div className="mt-6 flex flex-shrink-0 items-center justify-between border-t pt-4">
                            <div className="text-sm text-gray-600">
                                <Info className="mr-2 inline h-4 w-4 text-blue-500" />
                                Setelah menerima pencairan, silakan upload Surat Pertanggungjawaban (SPJ)
                            </div>
                            <div className="flex gap-3">
                                <Button variant="outline" onClick={() => setShowPencairanModal(false)}>
                                    Tutup
                                </Button>
                                <Button
                                    onClick={() => {
                                        setShowPencairanModal(false);
                                        router.visit('/panti/bansos/laporan');
                                    }}
                                    className="bg-green-600 text-white hover:bg-green-700"
                                >
                                    <Upload className="mr-2 h-4 w-4" />
                                    Upload SPJ
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>

                {/* Edit Modal */}
                <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
                    <DialogContent className="max-h-[90vh] max-w-[95vw] overflow-y-auto lg:max-w-7xl">
                        <DialogHeader>
                            <DialogTitle>
                                {editPengajuan?.status?.toLowerCase().trim() === 'ditolak'
                                    ? '🔄 Perbaiki & Ajukan Ulang Pengajuan Bantuan Sosial'
                                    : 'Edit Pengajuan Bantuan Sosial'}
                            </DialogTitle>
                            {editPengajuan?.status?.toLowerCase().trim() === 'ditolak' && (
                                <p className="mt-2 text-sm text-gray-600">
                                    Perbaiki data pengajuan dan upload ulang dokumen yang ditolak untuk mendapat status terbaru dari Dinas Sosial
                                    Provinsi Riau.
                                </p>
                            )}
                        </DialogHeader>
                        <form onSubmit={handleSubmitEdit} className="space-y-6">
                            {/* Read-only Data Section for Rejected Applications */}
                            {editPengajuan?.status?.toLowerCase().trim() === 'ditolak' && (
                                <div className="space-y-6">
                                    {/* Data Panti Section - Read Only */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <Info className="h-5 w-5 text-blue-600" />
                                                Data Pengajuan Bantuan Sosial
                                            </CardTitle>
                                            <CardDescription>
                                                Data berikut hanya dapat dilihat dan tidak dapat diubah. Untuk perubahan data, silakan hubungi
                                                administrator.
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                                <div className="space-y-2">
                                                    <label className="text-sm font-medium text-gray-700">Nama Panti</label>
                                                    <div className="rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-600">
                                                        {editPengajuan.nama_panti || '-'}
                                                    </div>
                                                </div>
                                                <div className="space-y-2">
                                                    <label className="text-sm font-medium text-gray-700">Kabupaten/Kota</label>
                                                    <div className="rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-600">
                                                        {editPengajuan.kabupaten_kota || '-'}
                                                    </div>
                                                </div>
                                                <div className="space-y-2">
                                                    <label className="text-sm font-medium text-gray-700">Tanggal Pengajuan</label>
                                                    <div className="rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-600">
                                                        {formatDate(editPengajuan.tanggal_pengajuan || editPengajuan.created_at)}
                                                    </div>
                                                </div>
                                                <div className="space-y-2">
                                                    <label className="text-sm font-medium text-gray-700">Status Saat Ini</label>
                                                    <div className="rounded-md border border-gray-200 bg-gray-50 px-3 py-2">
                                                        <span
                                                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(editPengajuan.status)}`}
                                                        >
                                                            {editPengajuan.status}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Documents Status - Real Data */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <FileText className="h-5 w-5 text-green-600" />
                                                Status Dokumen Persyaratan
                                            </CardTitle>
                                            <CardDescription>Status verifikasi untuk setiap dokumen yang telah diupload</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            {/* Summary of accepted/rejected documents, with fallback and warning if data is missing */}
                                            {(() => {
                                                let dokumen = getDokumenVerifikasi(editPengajuan);
                                                if (!Array.isArray(dokumen)) dokumen = [];
                                                const diterima = dokumen.filter((d: any) => d.status_verifikasi === 'diterima').length;
                                                const ditolak = dokumen.filter((d: any) => d.status_verifikasi === 'ditolak').length;
                                                return (
                                                    <div className="mb-4 flex flex-col gap-2">
                                                        <div className="flex flex-wrap gap-4">
                                                            <div className="flex items-center gap-2 rounded bg-green-100 px-3 py-1 text-green-800">
                                                                <CheckCircle className="h-4 w-4" />
                                                                <span className="font-medium">{diterima} Diterima</span>
                                                            </div>
                                                            <div className="flex items-center gap-2 rounded bg-red-100 px-3 py-1 text-red-800">
                                                                <X className="h-4 w-4" />
                                                                <span className="font-medium">{ditolak} Ditolak</span>
                                                            </div>
                                                        </div>
                                                        {dokumen.length === 0 && (
                                                            <div className="mt-2 rounded bg-yellow-100 px-3 py-2 text-sm text-yellow-800">
                                                                Data verifikasi dokumen tidak ditemukan. Silakan hubungi admin jika Anda yakin sudah
                                                                diverifikasi oleh Dinas Sosial.
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })()}
                                            <div className="space-y-4">
                                                {getDokumenVerifikasi(editPengajuan).map((doc: any) => (
                                                    <div
                                                        key={doc.id}
                                                        className={`rounded-lg border p-4 ${doc.status_verifikasi === 'ditolak' ? 'border-red-200 bg-red-50/30' : 'border-gray-200'}`}
                                                    >
                                                        <div className="mb-3 flex items-center justify-between">
                                                            <div className="flex items-center gap-3">
                                                                <FileText className="h-4 w-4 text-gray-500" />
                                                                <span className="text-sm font-medium text-gray-900">{doc.nama_dokumen}</span>
                                                            </div>
                                                            <Badge
                                                                variant={doc.status_verifikasi === 'diterima' ? 'default' : 'destructive'}
                                                                className={
                                                                    doc.status_verifikasi === 'diterima'
                                                                        ? 'bg-green-100 text-green-800'
                                                                        : 'bg-red-100 text-red-800'
                                                                }
                                                            >
                                                                {doc.status_verifikasi === 'diterima' ? '✓ Diterima' : '✗ Ditolak'}
                                                            </Badge>
                                                        </div>

                                                        {/* Alasan penolakan dan upload ulang untuk dokumen yang ditolak */}
                                                        {doc.status_verifikasi === 'ditolak' && (
                                                            <div className="space-y-3">
                                                                <div className="rounded border-l-4 border-red-500 bg-red-100 p-3">
                                                                    <p className="text-sm text-red-700">
                                                                        <strong>Alasan Penolakan:</strong> {doc.catatan}
                                                                    </p>
                                                                </div>

                                                                {/* File Upload Area */}
                                                                <div>
                                                                    {uploadedFiles[doc.id] ? (
                                                                        <div className="flex items-center gap-2 rounded-lg border border-green-200 bg-green-50 p-3">
                                                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                                                            <span className="text-sm font-medium text-green-700">
                                                                                {uploadedFiles[doc.id].name}
                                                                            </span>
                                                                            <Button
                                                                                type="button"
                                                                                size="sm"
                                                                                variant="ghost"
                                                                                className="h-6 w-6 p-0 text-red-600 hover:bg-red-100"
                                                                                onClick={() => removeUploadedFile(doc.id)}
                                                                            >
                                                                                <X className="h-3 w-3" />
                                                                            </Button>
                                                                        </div>
                                                                    ) : (
                                                                        <div className="rounded-lg border-2 border-dashed border-gray-300 p-4 text-center transition-colors hover:border-blue-400">
                                                                            <input
                                                                                type="file"
                                                                                id={`file-reupload-${doc.id}`}
                                                                                className="hidden"
                                                                                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                                                                                onChange={(e) => {
                                                                                    const file = e.target.files?.[0];
                                                                                    if (file) {
                                                                                        handleFileUpload(doc.id, file);
                                                                                    }
                                                                                }}
                                                                            />
                                                                            <label htmlFor={`file-reupload-${doc.id}`} className="cursor-pointer">
                                                                                <Upload className="mx-auto mb-2 h-6 w-6 text-gray-400" />
                                                                                <p className="text-sm text-gray-600">
                                                                                    <span className="font-medium text-blue-600 hover:text-blue-700">
                                                                                        Upload ulang dokumen
                                                                                    </span>
                                                                                </p>
                                                                                <p className="mt-1 text-xs text-gray-500">
                                                                                    PDF, JPG, PNG, DOC, DOCX (Max. 10MB)
                                                                                </p>
                                                                            </label>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Upload Progress Summary */}
                                    {Object.keys(uploadedFiles).length > 0 && (
                                        <Card className="border-blue-200 bg-blue-50/30">
                                            <CardContent className="p-4">
                                                <div className="mb-3 flex items-center justify-between">
                                                    <span className="text-sm font-medium text-blue-900">Progress Upload Dokumen</span>
                                                    <span className="text-sm text-blue-700">
                                                        {Object.keys(uploadedFiles).length} dokumen telah diupload ulang
                                                    </span>
                                                </div>
                                                <div className="mb-2 h-2 w-full rounded-full bg-blue-200">
                                                    <div className="h-2 w-full rounded-full bg-blue-600 transition-all duration-300"></div>
                                                </div>
                                                <p className="text-xs text-blue-700">
                                                    Dokumen yang diupload ulang akan menggantikan dokumen sebelumnya yang ditolak
                                                </p>
                                            </CardContent>
                                        </Card>
                                    )}
                                </div>
                            )}

                            <div className="flex justify-end gap-2 pt-4">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setUploadedFiles({});
                                    }}
                                    disabled={processing}
                                >
                                    Batal
                                </Button>
                                <Button type="submit" disabled={processing} className="bg-blue-600 hover:bg-blue-700">
                                    {processing
                                        ? 'Menyimpan...'
                                        : editPengajuan?.status?.toLowerCase().trim() === 'ditolak'
                                          ? '🔄 Ajukan Ulang'
                                          : 'Simpan Perubahan'}
                                </Button>
                                {editPengajuan?.status?.toLowerCase().trim() === 'ditolak' && Object.keys(uploadedFiles).length > 0 && (
                                    <p className="mt-2 text-xs text-green-600">
                                        ✅ {Object.keys(uploadedFiles).length} dokumen siap untuk diajukan ulang
                                    </p>
                                )}
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </PantiLayout>
    );
}
