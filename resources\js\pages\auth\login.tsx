import { Head, Link, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import DinsosProvisnsiRiauLogoIcon from '@/components/dinsos-logo-icon';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

type LoginForm = {
    email: string;
    password: string;
    remember: boolean;
};

interface LoginProps {
    status?: string;
    canResetPassword: boolean;
}

export default function Login({ status, canResetPassword }: LoginProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
        remember: false,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('login'), {
            onFinish: () => reset('password'),
        });
    };

    return (
        <>
            <Head title="Login - Sistem Informasi Pelayanan Sosial">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
            </Head>

            <div className="flex min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
                {/* Left Side - System Info */}
                <div className="relative hidden overflow-hidden bg-gradient-to-br from-blue-600 to-blue-800 text-white lg:flex lg:flex-1">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-600/90 to-blue-800/90"></div>
                    <div className="relative z-10 flex flex-col justify-center px-12 py-16">
                        <div className="max-w-lg">
                            <div className="mb-8">
                                <div className="mb-6 inline-flex h-20 w-20 items-center justify-center rounded-full bg-white/20">
                                    <DinsosProvisnsiRiauLogoIcon className="h-16 w-16" style={{ filter: 'brightness(0) invert(1)' }} />
                                </div>
                                <h1 className="mb-4 text-4xl font-bold">Sistem Informasi Pelayanan Sosial</h1>
                                <p className="text-xl leading-relaxed text-blue-100">
                                    Platform digital yang menghubungkan semua stakeholder dalam ekosistem pelayanan sosial untuk memberikan layanan
                                    yang lebih efektif dan terpadu.
                                </p>
                            </div>

                            <div className="space-y-6">
                                <div className="flex items-start space-x-3">
                                    <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-white/20">
                                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="mb-1 font-semibold">Manajemen Data Terpadu</h3>
                                        <p className="text-sm text-blue-100">
                                            Kelola semua data panti asuhan, COTA, dan bantuan sosial dalam satu sistem
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-3">
                                    <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-white/20">
                                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="mb-1 font-semibold">Proses Otomatis</h3>
                                        <p className="text-sm text-blue-100">
                                            Otomatisasi pengajuan, verifikasi, dan monitoring untuk efisiensi maksimal
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-3">
                                    <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-white/20">
                                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="mb-1 font-semibold">Transparansi Penuh</h3>
                                        <p className="text-sm text-blue-100">Monitoring dan laporan real-time untuk transparansi dan akuntabilitas</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Side - Login Form */}
                <div className="flex flex-1 flex-col justify-center px-6 py-12 lg:px-12">
                    <div className="mx-auto w-full max-w-md">
                        <div className="mb-8 text-center">
                            <Link href="/" className="mb-6 inline-block">
                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full">
                                    <DinsosProvisnsiRiauLogoIcon className="h-14 w-14" />
                                </div>
                            </Link>
                            <h2 className="mb-2 text-3xl font-bold text-gray-900">Selamat Datang</h2>
                            <p className="text-gray-600">Masuk ke akun Anda untuk melanjutkan</p>
                        </div>

                        {status && <div className="mb-4 rounded-lg bg-green-50 p-3 text-sm font-medium text-green-600">{status}</div>}

                        <form className="space-y-6" onSubmit={submit}>
                            <div>
                                <Label htmlFor="email" className="mb-2 block text-sm font-medium text-gray-700">
                                    Email
                                </Label>
                                <Input
                                    id="email"
                                    type="email"
                                    required
                                    autoFocus
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className="w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                    placeholder="Masukkan email Anda"
                                />
                                <InputError message={errors.email} className="mt-2" />
                            </div>

                            <div>
                                <Label htmlFor="password" className="mb-2 block text-sm font-medium text-gray-700">
                                    Password
                                </Label>
                                <Input
                                    id="password"
                                    type="password"
                                    required
                                    value={data.password}
                                    onChange={(e) => setData('password', e.target.value)}
                                    className="w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                    placeholder="Masukkan password Anda"
                                />
                                <InputError message={errors.password} className="mt-2" />
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                    <Checkbox
                                        id="remember"
                                        checked={data.remember}
                                        onCheckedChange={(checked) => setData('remember', checked as boolean)}
                                        className="mr-2"
                                    />
                                    <Label htmlFor="remember" className="text-sm text-gray-600">
                                        Ingat saya
                                    </Label>
                                </div>

                                {canResetPassword && (
                                    <Link href={route('password.request')} className="text-sm text-blue-600 hover:text-blue-500 hover:underline">
                                        Lupa password?
                                    </Link>
                                )}
                            </div>

                            <Button
                                type="submit"
                                disabled={processing}
                                className="w-full rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 py-3 font-semibold text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl disabled:opacity-50"
                            >
                                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                {processing ? 'Sedang masuk...' : 'Masuk'}
                            </Button>
                        </form>

                        <div className="mt-8 text-center">
                            <p className="text-gray-600">
                                Belum punya akun?{' '}
                                <Link href={route('register')} className="font-semibold text-blue-600 hover:text-blue-500 hover:underline">
                                    Daftar sekarang
                                </Link>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
