import { Head, Link, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import DinsosProvisnsiRiauLogoIcon from '@/components/dinsos-logo-icon';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    role: string;
};

export default function Register() {
    const { data, setData, post, processing, errors, reset } = useForm<RegisterForm>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    return (
        <>
            <Head title="Register - Sistem Informasi Pelayanan Sosial">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
            </Head>

            <div className="flex min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
                {/* Left Side - Register Form */}
                <div className="flex flex-1 flex-col justify-center px-6 py-12 lg:px-12">
                    <div className="mx-auto w-full max-w-md">
                        <div className="mb-8 text-center">
                            <Link href="/" className="mb-6 inline-block">
                                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full">
                                    <DinsosProvisnsiRiauLogoIcon className="h-14 w-14" />
                                </div>
                            </Link>
                            <h2 className="mb-2 text-3xl font-bold text-gray-900">Daftar Akun</h2>
                            <p className="text-gray-600">Buat akun baru untuk mengakses sistem</p>
                        </div>

                        <form className="space-y-6" onSubmit={submit}>
                            <div>
                                <Label htmlFor="name" className="mb-2 block text-sm font-medium text-gray-700">
                                    Nama Lengkap
                                </Label>
                                <Input
                                    id="name"
                                    type="text"
                                    required
                                    autoFocus
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className="w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                    placeholder="Masukkan nama lengkap"
                                />
                                <InputError message={errors.name} className="mt-2" />
                            </div>

                            <div>
                                <Label htmlFor="email" className="mb-2 block text-sm font-medium text-gray-700">
                                    Email
                                </Label>
                                <Input
                                    id="email"
                                    type="email"
                                    required
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    className="w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                    placeholder="Masukkan email"
                                />
                                <InputError message={errors.email} className="mt-2" />
                            </div>

                            <div>
                                <Label htmlFor="role" className="mb-2 block text-sm font-medium text-gray-700">
                                    Role
                                </Label>
                                <Select value={data.role} onValueChange={(value) => setData('role', value)}>
                                    <SelectTrigger className="w-full rounded-lg border border-gray-300 px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500">
                                        <SelectValue placeholder="Pilih role Anda" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="panti_asuhan">Panti Asuhan</SelectItem>
                                        <SelectItem value="dinsos_provinsi_riau">Dinsos Provinsi Riau</SelectItem>
                                        <SelectItem value="dinsos_kabupaten_kota">Dinsos Kabupaten/Kota</SelectItem>
                                        <SelectItem value="cota">COTA</SelectItem>
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.role} className="mt-2" />
                            </div>

                            <div>
                                <Label htmlFor="password" className="mb-2 block text-sm font-medium text-gray-700">
                                    Password
                                </Label>
                                <Input
                                    id="password"
                                    type="password"
                                    required
                                    value={data.password}
                                    onChange={(e) => setData('password', e.target.value)}
                                    className="w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                    placeholder="Masukkan password"
                                />
                                <InputError message={errors.password} className="mt-2" />
                            </div>

                            <div>
                                <Label htmlFor="password_confirmation" className="mb-2 block text-sm font-medium text-gray-700">
                                    Konfirmasi Password
                                </Label>
                                <Input
                                    id="password_confirmation"
                                    type="password"
                                    required
                                    value={data.password_confirmation}
                                    onChange={(e) => setData('password_confirmation', e.target.value)}
                                    className="w-full rounded-lg border border-gray-300 px-4 py-3 transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                                    placeholder="Ulangi password"
                                />
                                <InputError message={errors.password_confirmation} className="mt-2" />
                            </div>

                            <Button
                                type="submit"
                                disabled={processing}
                                className="w-full rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 py-3 font-semibold text-white shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl disabled:opacity-50"
                            >
                                {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                                {processing ? 'Sedang mendaftar...' : 'Daftar'}
                            </Button>
                        </form>

                        <div className="mt-8 text-center">
                            <p className="text-gray-600">
                                Sudah punya akun?{' '}
                                <Link href={route('login')} className="font-semibold text-blue-600 hover:text-blue-500 hover:underline">
                                    Masuk sekarang
                                </Link>
                            </p>
                        </div>
                    </div>
                </div>

                {/* Right Side - System Info */}
                <div className="relative hidden overflow-hidden bg-gradient-to-br from-blue-600 to-blue-800 text-white lg:flex lg:flex-1">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-600/90 to-blue-800/90"></div>
                    <div className="relative z-10 flex flex-col justify-center px-12 py-16">
                        <div className="max-w-lg">
                            <div className="mb-8">
                                <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                                    <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                        />
                                    </svg>
                                </div>
                                <h1 className="mb-4 text-4xl font-bold">Sistem Informasi Pelayanan Sosial</h1>
                                <p className="text-xl leading-relaxed text-blue-100">
                                    Bergabunglah dengan platform yang menghubungkan semua stakeholder dalam ekosistem pelayanan sosial untuk
                                    memberikan layanan yang lebih efektif dan terpadu.
                                </p>
                            </div>

                            <div className="space-y-6">
                                <div className="flex items-start space-x-3">
                                    <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-white/20">
                                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="mb-1 font-semibold">Multi-Role Access</h3>
                                        <p className="text-sm text-blue-100">
                                            Akses khusus untuk Panti Asuhan, Dinsos, dan COTA sesuai kebutuhan masing-masing
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-3">
                                    <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-white/20">
                                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="mb-1 font-semibold">Keamanan Terjamin</h3>
                                        <p className="text-sm text-blue-100">
                                            Sistem keamanan berlapis untuk melindungi data sensitif dan privasi pengguna
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-3">
                                    <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-white/20">
                                        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="mb-1 font-semibold">Support 24/7</h3>
                                        <p className="text-sm text-blue-100">
                                            Tim support siap membantu Anda kapan saja untuk kelancaran operasional
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
