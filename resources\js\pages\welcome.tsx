import DinsosProvisnsiRiauLogoIcon from '@/components/dinsos-logo-icon';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Head, Link } from '@inertiajs/react';
import { Building2, FileText, Users } from 'lucide-react';

export default function Welcome() {
    return (
        <>
            <Head title="Sistem Informasi Pelayanan Sosial" />
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
                {/* Header */}
                <header className="bg-white/80 shadow-sm backdrop-blur-md">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="flex h-16 w-16 items-center justify-center rounded-lg">
                                    <DinsosProvisnsiRiauLogoIcon className="h-14 w-14" />
                                </div>
                                <h1 className="text-xl font-bold text-gray-900">SIPSOS</h1>
                            </div>
                            <div className="flex items-center space-x-4">
                                <Link href="/login">
                                    <Button variant="ghost">Masuk</Button>
                                </Link>
                                <Link href="/register">
                                    <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                                        Daftar
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    </div>
                </header>

                {/* Hero Section */}
                <main className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                    <div className="mb-16 text-center">
                        <div className="mb-16">
                            {/* <div className="mx-auto mb-6 flex h-50 w-50 items-center justify-center rounded-2xl">
                                <DinsosProvisnsiRiauLogoIcon className="h-50 w-50" />
                            </div> */}
                            <h1 className="mb-8 text-4xl font-bold text-gray-900 md:text-6xl">
                                <span className="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">Sistem Informasi</span>
                                <br />
                                <span className="bg-gradient-to-r from-blue-700 to-blue-900 bg-clip-text text-transparent">Pelayanan Sosial</span>
                            </h1>
                            <p className="mx-auto max-w-3xl text-xl leading-relaxed text-gray-600">
                                Platform digital yang menghubungkan panti asuhan dengan Dinas Sosial untuk pengelolaan bantuan sosial, pengajuan dana,
                                dan pelaporan kegiatan secara terintegrasi.
                            </p>
                        </div>

                        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
                            <Link href="/login">
                                <Button
                                    size="lg"
                                    className="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-3 hover:from-blue-700 hover:to-blue-800"
                                >
                                    <Users className="mr-2 h-5 w-5" />
                                    Masuk ke Sistem
                                </Button>
                            </Link>
                            <Link href="/register">
                                <Button size="lg" variant="outline" className="border-blue-200 px-8 py-3 text-blue-700 hover:bg-blue-50">
                                    <Building2 className="mr-2 h-5 w-5" />
                                    Daftar Sekarang
                                </Button>
                            </Link>
                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button size="lg" variant="outline" className="border-green-200 px-8 py-3 text-green-700 hover:bg-green-50">
                                        <FileText className="mr-2 h-5 w-5" />
                                        Lihat Persyaratan
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="!max-h-[95vh] !w-[95vw] !max-w-[95vw] overflow-y-auto">
                                    <DialogHeader>
                                        <DialogTitle className="text-3xl font-bold text-gray-900">Persyaratan Pengangkatan Anak</DialogTitle>
                                        <DialogDescription className="text-lg text-gray-600">
                                            Berikut adalah daftar lengkap persyaratan yang diperlukan untuk proses pengangkatan anak.
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="mt-8">
                                        <div className="space-y-6">
                                            {[
                                                'Rekomendasi di Dinas Sosial Kabupaten/Kota',
                                                'Permohonan dan Pemohon',
                                                'Surat Keterangan Berbadan Sehat dari Rumah Sakit Pemerintah',
                                                'Surat keterangan Kesehatan Jiwa dari Dokter Spesialis Jiwa dari Rumah Sakit Pemerintah (Suami/Istri)',
                                                'Surat Keterangan tentang Fungsi Organ Reproduksi (Suami/lstri) dari Dokter Spesialis Obstetric dan Gineologi Rumah Sakit Pemerintah',
                                                'Foto Copy Akta Kelahiran COTA',
                                                'Surat Keterangan Catatan Kepolisian Setempat',
                                                'Foto Copy Nikah/Kutipan Akte Nikah COTA',
                                                'Foto Copy Kartu Keluarga dan KTP COTA',
                                                'Foto Copy Akta Kelahiran CAA',
                                                'Surat Keterangan Penghasilan dari tempat bekerja COTA',
                                                'Surat Persyaratan Persetujuan CAA diatas kertas bermaterai cukup bagi anak yang telah mampu menyampaikan pendapatnya dan hasil Laporan Pekerja Sosial',
                                                'Surat Pernyataan Motivasi COTA mengangkat Anak demi kepentingan terbaik bagi anak dan Perlindungan Anak',
                                                'Surat Pernyataan COTA akan memperlakukan anak kandung dan anak angkat tanpa diskriminasi',
                                                'Surat Pernyataan bahwa COTA akan memberitahukan kepada anak angkat mengenai asal usul dan orangtua kandungnya',
                                                'Surat Pernyataan COTA tidak berhak menjadi Wali Nikah bagi anak angkat perempuan dan memberi kuasa kepada Wali Hakim',
                                                'Surat Pernyataan COTA akan memberikan hak waris/hibah atas harta kepada anak angkat sesuai ketentuan hukum islam yang berlaku di Indonesia',
                                                'Surat Pernyataan persetujuan adopsi dari pihak keluarga COTA',
                                                'Laporan Sosial Calon Anak Angkat yang dibuat oleh pekerja sosial instansi sosial setempat dan pekerja sosial Panti / Yayasan',
                                                'Surat/Berita Acara Penyerahan dan Kuasa dari Pihak ibu kandung kepada instansi sosial setempat/ COTA',
                                                'Surat/Berita acara Penyerahan dan Kuasa dari Pihak instansi sosial setempat dan pekerja sosial Panti / Yayasan',
                                                'Laporan Calon Orangtua Angkat yang dibuat oleh pekerja sosial instansi sosial setempat dan pekerja sosial/yayasan',
                                                'SK Pemberian Izin Pengasuhan Anak',
                                                'Surat Perjanjian Pengasuhan Anak Antara Panti / Yayasan dengan COTA',
                                                'Surat/Berita Acara Penyerahan Anak dari Panti/Yayasan kepada COTA',
                                                'Laporan Perkembangan Anak yang dibuat oleh Pekerja Sosial Instansi Sosial setempat dan Pekerja Sosial Panti/Yayasan',
                                                'Foto Calon Orang Tua Angkat dan Calon Anak Angkat',
                                            ].map((requirement, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-start space-x-4 rounded-lg bg-gray-50 p-4 transition-colors hover:bg-gray-100"
                                                >
                                                    <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-blue-600 text-sm font-semibold text-white">
                                                        {index + 1}
                                                    </div>
                                                    <p className="text-base leading-relaxed text-gray-700">{requirement}</p>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="mt-8 rounded-lg bg-blue-50 p-6">
                                            <p className="text-base text-blue-800">
                                                <strong>Catatan:</strong> Pastikan semua dokumen yang dibutuhkan telah lengkap dan sesuai dengan
                                                ketentuan yang berlaku. Untuk informasi lebih lanjut, silakan hubungi Dinas Sosial setempat.
                                            </p>
                                        </div>
                                    </div>
                                </DialogContent>
                            </Dialog>
                        </div>
                    </div>
                </main>
            </div>
        </>
    );
}
