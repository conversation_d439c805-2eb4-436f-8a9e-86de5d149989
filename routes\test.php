<?php

use App\Http\Controllers\PantiController;
use App\Http\Controllers\DinsosProvisnsiRiauController;
use Illuminate\Support\Facades\Route;

// Temporary test routes without auth middleware - updated after cleanup

Route::get('/test/cota/data-calon', function() {
    // Simulate being logged in as a DinsosRiau user
    $user = \App\Models\User::where('role', \App\Models\User::ROLE_DINSOS_PROVINSI_RIAU)->first();
    if (!$user) {
        return response()->json(['error' => 'No DinsosRiau user found']);
    }
    auth()->login($user);
    
    $controller = new DinsosProvisnsiRiauController();
    return $controller->lksCotaDataCalon(request());
});

Route::get('/test/cota/data-calon-count', function() {
    $count = \App\Models\CalonOrangTuaAsuh::count();
    $data = \App\Models\CalonOrangTuaAsuh::take(3)->get();
    return response()->json(['count' => $count, 'sample_data' => $data]);
});

Route::get('/test/create-dinsos-user', function() {
    $user = \App\Models\User::firstOrCreate([
        'email' => '<EMAIL>'
    ], [
        'name' => 'Admin Dinsos Riau',
        'role' => \App\Models\User::ROLE_DINSOS_PROVINSI_RIAU,
        'password' => bcrypt('password')
    ]);
    return response()->json(['user' => $user]);
});

Route::get('/test/bansos/statuspengajuan', function() {
    // Simulate being logged in as the Panti user
    $user = \App\Models\User::where('role', \App\Models\User::ROLE_PANTI_ASUHAN)->first();
    auth()->login($user);
    
    $controller = new PantiController();
    return $controller->bansosStatus(request());
});

Route::get('/test/bansos/laporan', function() {
    // Simulate being logged in as the Panti user
    $user = \App\Models\User::where('role', \App\Models\User::ROLE_PANTI_ASUHAN)->first();
    auth()->login($user);
    
    $controller = new PantiController();
    return $controller->laporanIndex(request());
});
