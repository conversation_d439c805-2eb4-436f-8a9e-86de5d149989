
<?php
// API endpoints for COTA status (for DinsosKota/DaftarCota cards)
Route::get('/dinsosprov/get-cota-tersedia', [\App\Http\Controllers\DinsosProvisnsiRiauController::class, 'getCotaTersertifikasiList']);
Route::get('/dinsosprov/get-cota-proses-verifikasi', [\App\Http\Controllers\DinsosProvisnsiRiauController::class, 'getCotaProsesVerifikasiList']);

use App\Http\Controllers\PantiController;
use App\Http\Controllers\DinsosProvisnsiRiauController;
use App\Http\Controllers\DinsosKotaController;
use App\Http\Controllers\CotaController;
use App\Models\User;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

// Role-based dashboard routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Panti Asuhan Dashboard
    Route::get('/panti/dashboard', [PantiController::class, 'dashboard'])
        ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
        ->name('panti.dashboard');
    
    // Dinsos Provinsi Riau Dashboard  
    Route::get('/dinsosriau/dashboard', [DinsosProvisnsiRiauController::class, 'dashboard'])
        ->middleware('role:' . User::ROLE_DINSOS_PROVINSI_RIAU)
        ->name('dinsosriau.dashboard');
    
    // Dinsos Kabupaten/Kota Dashboard
    Route::get('/dinsoskota/dashboard', [DinsosKotaController::class, 'dashboard'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.dashboard');
    
    // Dinsos Kabupaten/Kota Daftar COTA
    Route::get('/dinsoskota/daftarcota', [DinsosKotaController::class, 'daftarCota'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.daftarcota');
    
    // Dinsos Kabupaten/Kota Register COTA
    Route::post('/dinsoskota/daftarcota/register', [DinsosKotaController::class, 'registerCota'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.register-cota');
    
    // Dinsos Kabupaten/Kota Delete COTA
    Route::delete('/dinsoskota/daftarcota/{id}', [DinsosKotaController::class, 'destroyCota'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.delete-cota');
    
    // Dinsos Kabupaten/Kota Edit COTA
    Route::get('/dinsoskota/daftarcota/{id}/edit', [DinsosKotaController::class, 'editCota'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.edit-cota');
    
    // Dinsos Kabupaten/Kota View COTA
    Route::get('/dinsoskota/daftarcota/{id}', [DinsosKotaController::class, 'showCota'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.show-cota');
    
    // Dinsos Kabupaten/Kota Update COTA
    Route::put('/dinsoskota/daftarcota/{id}', [DinsosKotaController::class, 'updateCota'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.update-cota');
    
    // Dinsos Kabupaten/Kota Update COTA Status
    Route::put('/dinsoskota/daftarcota/{id}/status', [DinsosKotaController::class, 'updateCotaStatus'])
        ->middleware('role:' . User::ROLE_DINSOS_KABUPATEN_KOTA)
        ->name('dinsoskota.update-cota-status');
    
    // COTA Dashboard
    Route::get('/cota/dashboard', [CotaController::class, 'dashboard'])
        ->middleware('role:' . User::ROLE_COTA)
        ->name('cota.dashboard');
});

// Module-based routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Panti Asuhan Module Routes
    Route::prefix('panti')->middleware('module.access:panti')->group(function () {
        // Bantuan Sosial page for Panti Asuhan
        Route::get('/bansos', [PantiController::class, 'bansos'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos');

        // Bantuan Sosial - Pengajuan
        Route::get('/bansos/pengajuan', [PantiController::class, 'bansosCreate'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.pengajuan');

        Route::post('/bansos/pengajuan', [PantiController::class, 'bansosStore'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.store');

        // Bantuan Sosial - Status Pengajuan
        Route::get('/bansos/statuspengajuan', [PantiController::class, 'bansosStatus'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.status');

        // Status Pengajuan - Main Page
        Route::get('/statuspengajuan', [PantiController::class, 'statusPengajuan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.status.pengajuan');

        Route::get('/bansos/pengajuan/{id}/edit', [PantiController::class, 'bansosEdit'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.edit');

        Route::put('/bansos/pengajuan/{id}', [PantiController::class, 'bansosUpdate'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.update');

        // Bantuan Sosial - Laporan
        Route::get('/bansos/laporan', [PantiController::class, 'laporanIndex'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.laporan');

        Route::post('/bansos/laporan', [PantiController::class, 'laporanStore'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.laporan.store');

        Route::get('/bansos/laporan/{id}/edit', [PantiController::class, 'laporanEdit'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.laporan.edit');

        Route::put('/bansos/laporan/{id}', [PantiController::class, 'laporanUpdate'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.laporan.update');

        Route::get('/bansos/laporan/upload', [PantiController::class, 'laporanCreate'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.laporan.create');

        // Pencairan
        Route::get('/bansos/pencairan', [PantiController::class, 'bansosPencairan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.bansos.pencairan');

        // Data Panti
        Route::get('/datapanti', [PantiController::class, 'dataPanti'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.datapanti');
        
        Route::post('/datapanti', [PantiController::class, 'storeIdentitas'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.datapanti.store');
        
        Route::post('/datapanti/upload', [PantiController::class, 'uploadDokumen'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.datapanti.upload');
        
        // Data Anak
        Route::get('/dataanak', [PantiController::class, 'dataAnak'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak');
        
        Route::post('/dataanak', [PantiController::class, 'storeAnak'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak.store');
        
        Route::get('/dataanak/{id}', [PantiController::class, 'showAnak'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak.show');
        
        Route::get('/dataanak/{id}/edit', [PantiController::class, 'editAnak'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak.edit');
        
        Route::put('/dataanak/{id}', [PantiController::class, 'updateAnak'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak.update');
        
        Route::delete('/dataanak/{id}', [PantiController::class, 'destroyAnak'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak.destroy');

        Route::get('/dataanak/{id}/progress', [PantiController::class, 'getProgressStatus'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.dataanak.progress');
        
        // Pendanaan
        Route::get('/pendanaan', [PantiController::class, 'pendanaan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.pendanaan');
        
        Route::post('/pendanaan', [PantiController::class, 'storePengajuanDana'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.pendanaan.store');
        
        Route::get('/pendanaan/{id}', [PantiController::class, 'showPengajuanDana'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.pendanaan.show');
        
        Route::delete('/pendanaan/{id}', [PantiController::class, 'destroyPengajuanDana'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.pendanaan.destroy');

        Route::post('/pendanaan/{id}/laporan', [PantiController::class, 'uploadLaporanPenggunaan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.pendanaan.laporan');
        
        // Laporan Kegiatan
        Route::get('/laporankegiatan', [PantiController::class, 'laporanKegiatan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.laporankegiatan');

        // Jadwal Kunjungan
        Route::get('/jadwalkunjungan', [PantiController::class, 'jadwalKunjungan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.jadwalkunjungan');

        Route::post('/jadwalkunjungan/{id}/confirm', [PantiController::class, 'confirmJadwalKunjungan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.jadwalkunjungan.confirm');

        Route::post('/jadwalkunjungan/{id}/reject', [PantiController::class, 'rejectJadwalKunjungan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.jadwalkunjungan.reject');

        Route::post('/laporankegiatan', [PantiController::class, 'storeLaporanKegiatan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.laporankegiatan.store');

        Route::get('/laporankegiatan/{id}', [PantiController::class, 'showLaporanKegiatan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.laporankegiatan.show');

        Route::delete('/laporankegiatan/{id}', [PantiController::class, 'destroyLaporanKegiatan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN)
            ->name('panti.laporankegiatan.destroy');
        // Log print laporan kegiatan
        Route::post('/laporankegiatan/{id}/log-print', [PantiController::class, 'logPrintLaporanKegiatan'])
            ->middleware('role:' . User::ROLE_PANTI_ASUHAN);
    });
    
    // COTA Module Routes  
    Route::prefix('cota')->middleware('module.access:cota')->group(function () {
        // Add cota module routes here
    });
    
    // Bantuan Sosial Module Routes
    Route::prefix('bantuan-sosial')->middleware('module.access:bantuan_sosial')->group(function () {
        // Add bantuan sosial module routes here
    });
    
    // Dinsos Provinsi Riau LKS Routes
    Route::prefix('dinsosriau/lks')->middleware('role:' . User::ROLE_DINSOS_PROVINSI_RIAU)->group(function () {
        Route::get('/panti', [DinsosProvisnsiRiauController::class, 'lksPanti'])
            ->name('dinsosriau.lks.panti');
        
        Route::get('/bansos', [DinsosProvisnsiRiauController::class, 'lksBansos'])
            ->name('dinsosriau.lks.bansos');
            
        // Bansos Sub Routes
        Route::get('/bansos/data', [DinsosProvisnsiRiauController::class, 'lksBansosData'])
            ->name('dinsosriau.lks.bansos.data');
            
        Route::get('/bansos/data/{id}', [DinsosProvisnsiRiauController::class, 'lksBansosDataShow'])
            ->name('dinsosriau.lks.bansos.data.show');
            
        Route::get('/bansos/pencairan', [DinsosProvisnsiRiauController::class, 'lksBansosPencairan'])
            ->name('dinsosriau.lks.bansos.pencairan');

        // Upload file pencairan
        Route::post('/bansos/pencairan/upload', [DinsosProvisnsiRiauController::class, 'uploadFilePencairan'])
            ->name('dinsosriau.lks.bansos.pencairan.upload');
            
        Route::get('/bansos/pertanggungjawaban', [DinsosProvisnsiRiauController::class, 'lksBansosPertanggungjawaban'])
            ->name('dinsosriau.lks.bansos.pertanggungjawaban');
            
        Route::get('/bansos/pertanggungjawaban/{id}', [DinsosProvisnsiRiauController::class, 'lksBansosPertanggungjawabanShow'])
            ->name('dinsosriau.lks.bansos.pertanggungjawaban.show');
            
        // Bansos Status Update Routes
        Route::put('/bansos/pengajuan/{id}/status', [DinsosProvisnsiRiauController::class, 'updatePengajuanBansosStatus'])
            ->name('dinsosriau.lks.bansos.pengajuan.status');
            
        Route::put('/bansos/laporan/{id}/status', [DinsosProvisnsiRiauController::class, 'updateLaporanBansosStatus'])
            ->name('dinsosriau.lks.bansos.laporan.status');
            
        // Jadwal Evaluasi Routes
        Route::post('/bansos/jadwal-evaluasi', [DinsosProvisnsiRiauController::class, 'storeJadwalEvaluasi'])
            ->name('dinsosriau.lks.bansos.jadwal-evaluasi.store');
        Route::get('/bansos/jadwal-evaluasi', [DinsosProvisnsiRiauController::class, 'getJadwalEvaluasiList'])
            ->name('dinsosriau.lks.bansos.jadwal-evaluasi.list');
        Route::post('/bansos/jadwal-evaluasi/{id}', [DinsosProvisnsiRiauController::class, 'updateJadwalEvaluasi'])
            ->name('dinsosriau.lks.bansos.jadwal-evaluasi.update');
        
        Route::get('/cota', [DinsosProvisnsiRiauController::class, 'lksCota'])
            ->name('dinsosriau.lks.cota');
        
        // COTA Sub Routes
        Route::get('/cota/data-calon', [DinsosProvisnsiRiauController::class, 'lksCotaDataCalon'])
            ->name('dinsosriau.lks.cota.data-calon');
        
        Route::get('/cota/data-calon/{id}', [DinsosProvisnsiRiauController::class, 'lksCotaDataCalonShow'])
            ->name('dinsosriau.lks.cota.data-calon.show');
        
        Route::patch('/cota/data-calon/{id}/status', [DinsosProvisnsiRiauController::class, 'lksCotaDataCalonUpdateStatus'])
            ->name('dinsosriau.lks.cota.data-calon.status');
        
        Route::get('/cota/dokumen-persyaratan', [DinsosProvisnsiRiauController::class, 'lksCotaDokumenPersyaratan'])
            ->name('dinsosriau.lks.cota.dokumen-persyaratan');
        
        Route::get('/cota/jadwal-sidang', [DinsosProvisnsiRiauController::class, 'lksCotaJadwalSidang'])
            ->name('dinsosriau.lks.cota.jadwal-sidang');
        
        Route::post('/cota/jadwal-sidang', [DinsosProvisnsiRiauController::class, 'lksCotaJadwalSidangStore'])
            ->name('dinsosriau.lks.cota.jadwal-sidang.store');

        // Tambah route update & hapus jadwal sidang
        Route::patch('/cota/jadwal-sidang/{id}', [DinsosProvisnsiRiauController::class, 'lksCotaJadwalSidangUpdate'])
            ->name('dinsosriau.lks.cota.jadwal-sidang.update');
        Route::delete('/cota/jadwal-sidang/{id}', [DinsosProvisnsiRiauController::class, 'lksCotaJadwalSidangDestroy'])
            ->name('dinsosriau.lks.cota.jadwal-sidang.destroy');

        Route::post('/cota/assign-jadwal-sidang', [DinsosProvisnsiRiauController::class, 'assignJadwalSidangToCota'])
            ->name('dinsosriau.lks.cota.assign-jadwal-sidang');
        
        // Panti Sub Routes
        // Jadwal Kunjungan Panti (khusus Dinsos Provinsi)
        Route::get('/panti/jadwal-kunjungan', [DinsosProvisnsiRiauController::class, 'getJadwalKunjunganPanti']);
        Route::post('/panti/jadwal-kunjungan', [DinsosProvisnsiRiauController::class, 'storeJadwalKunjunganPanti']);
        Route::get('/panti/daftar', [DinsosProvisnsiRiauController::class, 'lksPantiDaftar'])
            ->name('dinsosriau.lks.panti.daftar');

        // Tambah endpoint info panti untuk modal info
        Route::get('/panti/{id}/info', [DinsosProvisnsiRiauController::class, 'getPantiInfo'])
            ->name('dinsosriau.lks.panti.info');

        Route::get('/panti/{id}/anak-asuh', [DinsosProvisnsiRiauController::class, 'getPantiAnakAsuh'])
            ->name('dinsosriau.lks.panti.anak-asuh');

        Route::put('/panti/{id}/status', [DinsosProvisnsiRiauController::class, 'lksPantiUpdateStatus'])
            ->name('dinsosriau.lks.panti.status');

        Route::get('/panti/jadwal', [DinsosProvisnsiRiauController::class, 'lksPantiJadwal'])
            ->name('dinsosriau.lks.panti.jadwal');

        Route::get('/panti/verif', [DinsosProvisnsiRiauController::class, 'lksPantiVerif'])
            ->name('dinsosriau.lks.panti.verif');

        Route::get('/panti/laporan', [DinsosProvisnsiRiauController::class, 'lksPantiLaporan'])
            ->name('dinsosriau.lks.panti.laporan');
        
        // Admin action routes
        Route::put('/panti/daftar/{id}/status', [DinsosProvisnsiRiauController::class, 'updatePantiStatus'])
            ->name('dinsosriau.lks.panti.update-status');
        
        Route::put('/panti/verif/{id}/status', [DinsosProvisnsiRiauController::class, 'updatePengajuanStatus'])
            ->name('dinsosriau.lks.panti.update-pengajuan');

        Route::put('/panti/verif/{id}/laporan', [DinsosProvisnsiRiauController::class, 'updateStatusLaporan'])
            ->name('dinsosriau.lks.panti.update-laporan');

        Route::put('/panti/laporan/{id}/feedback', [DinsosProvisnsiRiauController::class, 'updateLaporanFeedback'])
            ->name('dinsosriau.lks.panti.update-feedback');
    });
});

require __DIR__.'/settings.php';

require __DIR__.'/auth.php';
