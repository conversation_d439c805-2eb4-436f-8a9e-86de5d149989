<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\App;
use App\Models\Anak;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Alasan Tidak Aktif Feature ===\n\n";

// Test 1: Create new anak with "tidak aktif" status
echo "1. Testing creation of anak with 'tidak aktif' status:\n";
try {
    $anak = Anak::create([
        'user_id' => 1,
        'nama_lengkap' => 'Test Anak Tidak Aktif',
        'tempat_lahir' => 'Jakarta',
        'tanggal_lahir' => '2010-01-01',
        'nik' => '1234567890123456',
        'usia' => 14,
        'jenis_kelamin' => 'laki-laki',
        'pendidikan' => 'SMP',
        'status_anak' => 'tidak aktif',
        'alasan_tidak_aktif' => 'Anak sudah di tingkat perguruan tinggi',
    ]);
    
    echo "   ✓ Anak created with ID: {$anak->id}\n";
    echo "   ✓ Status: {$anak->status_anak}\n";
    echo "   ✓ Alasan: {$anak->alasan_tidak_aktif}\n";
    
} catch (Exception $e) {
    echo "   ✗ Error: " . $e->getMessage() . "\n";
}

// Test 2: Check if field exists in database
echo "\n2. Testing database field existence:\n";
try {
    $result = \Illuminate\Support\Facades\DB::select("DESCRIBE anaks");
    $fieldExists = false;
    foreach ($result as $field) {
        if ($field->Field === 'alasan_tidak_aktif') {
            $fieldExists = true;
            echo "   ✓ Field 'alasan_tidak_aktif' exists in database\n";
            echo "   ✓ Type: {$field->Type}\n";
            echo "   ✓ Null: {$field->Null}\n";
            break;
        }
    }
    
    if (!$fieldExists) {
        echo "   ✗ Field 'alasan_tidak_aktif' not found in database\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error checking database: " . $e->getMessage() . "\n";
}

// Test 3: Query existing data
echo "\n3. Testing query of existing data:\n";
try {
    $anakList = Anak::where('status_anak', 'tidak aktif')->get();
    echo "   ✓ Found " . $anakList->count() . " anak with 'tidak aktif' status\n";
    
    foreach ($anakList as $anak) {
        echo "   - {$anak->nama_lengkap}: {$anak->status_anak}";
        if ($anak->alasan_tidak_aktif) {
            echo " (Alasan: {$anak->alasan_tidak_aktif})";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error querying data: " . $e->getMessage() . "\n";
}

// Test 4: Clean up test data
echo "\n4. Cleaning up test data:\n";
try {
    $deleted = Anak::where('nama_lengkap', 'Test Anak Tidak Aktif')->delete();
    echo "   ✓ Deleted {$deleted} test record(s)\n";
} catch (Exception $e) {
    echo "   ✗ Error cleaning up: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
