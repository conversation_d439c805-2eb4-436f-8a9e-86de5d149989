<?php

require 'vendor/autoload.php';

// Bootstrap Laravel application properly
$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\PengajuanBansos;
use App\Models\FilePencairan;
use App\Models\User;

echo "=== PENGAJUAN BANSOS DATA ===" . PHP_EOL;
$pengajuan = PengajuanBansos::with('user')->get();
foreach($pengajuan as $p) {
    echo "ID: {$p->id}, User ID: {$p->user_id}, Status: {$p->status}, Nama Panti: {$p->nama_panti}" . PHP_EOL;
    if($p->user) {
        echo "  User Name: {$p->user->name}, Email: {$p->user->email}" . PHP_EOL;
    }
}

echo PHP_EOL . "=== FILE PENCAIRAN DATA ===" . PHP_EOL;
$files = FilePencairan::with('pengajuanBansos')->get();
foreach($files as $f) {
    echo "ID: {$f->id}, Pengajuan ID: {$f->pengajuan_bansos_id}, File: {$f->nama_file}" . PHP_EOL;
    if($f->pengajuanBansos) {
        echo "  Terkait dengan: {$f->pengajuanBansos->nama_panti} (Status: {$f->pengajuanBansos->status})" . PHP_EOL;
    }
}

echo PHP_EOL . "=== TESTING LOGIC ===" . PHP_EOL;
// Debug: lihat semua users dan role mereka
echo "All users and their roles:" . PHP_EOL;
$allUsers = User::all();
foreach($allUsers as $u) {
    echo "User: {$u->name} (ID: {$u->id}) - Role: {$u->role}" . PHP_EOL;
}

echo PHP_EOL . "Testing with users who have pengajuan bansos..." . PHP_EOL;
// Test untuk user yang punya pengajuan bansos
$userIds = PengajuanBansos::distinct()->pluck('user_id');
$users = User::whereIn('id', $userIds)->get();
echo "Found " . $users->count() . " users with pengajuan bansos:" . PHP_EOL;
foreach($users as $user) {
    echo "User: {$user->name} (ID: {$user->id})" . PHP_EOL;
    
    // Cari pengajuan bansos yang paling baru dengan status 'Diterima' (bukan 'disetujui')
    $latestApprovedPengajuan = PengajuanBansos::where('user_id', $user->id)
        ->where('status', 'Diterima')
        ->orderBy('created_at', 'desc')
        ->first();
        
    if ($latestApprovedPengajuan) {
        echo "  Ada pengajuan disetujui: {$latestApprovedPengajuan->nama_panti}" . PHP_EOL;
        
        // Cek apakah ada file pencairan
        $hasPencairan = FilePencairan::where('pengajuan_bansos_id', $latestApprovedPengajuan->id)
            ->exists();
            
        echo "  Ada file pencairan: " . ($hasPencairan ? 'YA' : 'TIDAK') . PHP_EOL;
        echo "  Boleh buat pengajuan baru: " . ($hasPencairan ? 'YA' : 'TIDAK') . PHP_EOL;
    } else {
        echo "  Tidak ada pengajuan yang disetujui, boleh buat pengajuan baru: YA" . PHP_EOL;
    }
    echo PHP_EOL;
}
