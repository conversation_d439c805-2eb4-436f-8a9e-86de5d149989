<?php

require 'vendor/autoload.php';

// Bootstrap Laravel application properly
$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\DokumenVerifikasi;

echo "=== CREATING SAMPLE DATA FOR TESTING ===" . PHP_EOL;

// Test untuk pengajuan ID 2
$pengajuanId = 2;

// Buat beberapa sample verifikasi dengan dokumen yang digabung
$sampleData = [
    1 => 'diterima',    // Proposal
    2 => 'diterima',    // RAB
    3 => 'diterima',    // Surat Pengesahan
    4 => 'diterima',    // Data Anak
    5 => 'diterima',    // Surat Pernyataan
    6 => 'diterima',    // NPWP
    7 => 'ditolak',     // Izin Operasional
    8 => 'diterima',    // Foto Rekening
    9 => 'diterima',    // SK Pengurus
    10 => 'diterima',   // Akta Notaris
    11 => 'diterima',   // Tanda Daftar LKS
    12 => 'diterima',   // Sarana Prasarana
    13 => 'diterima',   // Pakta Integritas
    14 => 'diterima',   // Surat Domisili
    15 => 'diterima',   // Foto KTP Pengurus (GABUNGAN)
];

$dokumenList = [
    1 => 'Proposal Bantuan Sosial',
    2 => 'RAB (Rencana Anggaran Biaya)',
    3 => 'Surat Pengesahan Kemenkumham',
    4 => 'Data Anak',
    5 => 'Surat Pernyataan Tanggung Jawab',
    6 => 'NPWP',
    7 => 'Izin Operasional',
    8 => 'Foto Rekening Bank Panti Asuhan',
    9 => 'SK Pengurus Panti',
    10 => 'Akta Notaris',
    11 => 'Tanda Daftar LKS',
    12 => 'Sarana dan Prasarana Panti',
    13 => 'Pakta Integritas',
    14 => 'Surat Keterangan Domisili',
    15 => 'Foto KTP Pengurus (Ketua, Sekretaris, Bendahara)',
];

foreach ($sampleData as $dokumenId => $status) {
    $created = DokumenVerifikasi::updateOrCreate(
        [
            'pengajuan_bansos_id' => $pengajuanId,
            'dokumen_id' => $dokumenId,
        ],
        [
            'nama_dokumen' => $dokumenList[$dokumenId],
            'status_verifikasi' => $status,
        ]
    );
    
    echo "Created/Updated: Dokumen {$dokumenId} - {$status} ({$dokumenList[$dokumenId]})" . PHP_EOL;
}

echo PHP_EOL . "=== SAMPLE DATA CREATED ===" . PHP_EOL;
echo "Total dokumen untuk pengajuan {$pengajuanId}: 15" . PHP_EOL;
echo "Status: 14 diterima, 1 ditolak" . PHP_EOL;
echo "Catatan: Foto KTP Pengurus sudah digabung menjadi satu item" . PHP_EOL;
