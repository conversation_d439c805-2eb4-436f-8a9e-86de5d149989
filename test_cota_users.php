<?php
require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Cota;

// Create unverified COTA user
$unverifiedUser = User::create([
    'name' => 'Test COTA Unverified',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'cota'
]);

$unverifiedCota = Cota::create([
    'user_id' => $unverifiedUser->id,
    'nama' => 'Test COTA Unverified',
    'nik' => '1234567890123456',
    'telepon' => '081234567890',
    'email' => '<EMAIL>',
    'kabupaten_kota' => 'Pekanbaru',
    'ktp_path' => 'test.pdf',
    'kartu_keluarga_path' => 'test.pdf',
    'surat_pernikahan_path' => 'test.pdf',
    'slip_gaji_path' => 'test.pdf',
    'surat_keterangan_sehat_path' => 'test.pdf',
    'status' => 'menunggu_verifikasi'
]);

// Create verified COTA user
$verifiedUser = User::create([
    'name' => 'Test COTA Verified',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'cota'
]);

$verifiedCota = Cota::create([
    'user_id' => $verifiedUser->id,
    'nama' => 'Test COTA Verified',
    'nik' => '1234567890123457',
    'telepon' => '081234567891',
    'email' => '<EMAIL>',
    'kabupaten_kota' => 'Pekanbaru',
    'ktp_path' => 'test.pdf',
    'kartu_keluarga_path' => 'test.pdf',
    'surat_pernikahan_path' => 'test.pdf',
    'slip_gaji_path' => 'test.pdf',
    'surat_keterangan_sehat_path' => 'test.pdf',
    'status' => 'terdaftar'
]);

echo "Test COTA users created successfully!\n";
echo "Unverified user: <EMAIL> (status: menunggu_verifikasi)\n";
echo "Verified user: <EMAIL> (status: terdaftar)\n";
