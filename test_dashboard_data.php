<?php

require 'vendor/autoload.php';

$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Test Dashboard Real-time Data ===\n\n";

try {
    // Test koneksi database
    echo "1. Testing Database Connection...\n";
    $pantiCount = \App\Models\Panti::count();
    echo "   ✓ Database connected successfully\n";
    echo "   Total Panti: {$pantiCount}\n\n";

    // Test data persebaran panti
    echo "2. Testing Panti Persebaran Data...\n";
    $pantiPersebaran = \App\Models\Panti::select('kabupaten', \Illuminate\Support\Facades\DB::raw('count(*) as jumlah'))
        ->whereNotNull('kabupaten')
        ->groupBy('kabupaten')
        ->orderBy('jumlah', 'desc')
        ->get();
    
    echo "   Panti per Kabupaten:\n";
    foreach($pantiPersebaran as $item) {
        echo "   - {$item->kabupaten}: {$item->jumlah}\n";
    }
    echo "\n";

    // Test data COTA
    echo "3. Testing COTA Data...\n";
    $cotaCount = \App\Models\CalonOrangTuaAsuh::count();
    echo "   Total COTA: {$cotaCount}\n";
    
    $cotaPerTahun = \App\Models\CalonOrangTuaAsuh::select(
            \Illuminate\Support\Facades\DB::raw('YEAR(created_at) as tahun'),
            \Illuminate\Support\Facades\DB::raw('count(*) as jumlah')
        )
        ->groupBy('tahun')
        ->orderBy('tahun')
        ->get();
    
    echo "   COTA per Tahun:\n";
    foreach($cotaPerTahun as $item) {
        echo "   - {$item->tahun}: {$item->jumlah}\n";
    }
    echo "\n";

    // Test data Panti per tahun
    echo "4. Testing Panti per Tahun Data...\n";
    $pantiPerTahun = \App\Models\Panti::select(
            \Illuminate\Support\Facades\DB::raw('YEAR(created_at) as tahun'),
            \Illuminate\Support\Facades\DB::raw('count(*) as jumlah')
        )
        ->groupBy('tahun')
        ->orderBy('tahun')
        ->get();
    
    echo "   Panti per Tahun:\n";
    foreach($pantiPerTahun as $item) {
        echo "   - {$item->tahun}: {$item->jumlah}\n";
    }
    echo "\n";

    // Test lokasi panti
    echo "5. Testing Panti Locations...\n";
    $pantiLocations = \App\Models\Panti::with('user')
        ->whereNotNull('kabupaten')
        ->get();
    
    echo "   Sample Panti Locations (first 3):\n";
    foreach($pantiLocations->take(3) as $panti) {
        $jumlahAnak = \App\Models\Anak::where('user_id', $panti->user_id)->count();
        echo "   - {$panti->nama} di {$panti->kabupaten} ({$jumlahAnak} anak)\n";
    }
    
    echo "\n=== All tests completed successfully! ===\n";
    echo "Dashboard should now display real-time data from database.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Check database configuration and ensure data exists.\n";
}
