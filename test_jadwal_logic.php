<?php
require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use App\Models\Cota;
use App\Models\JadwalSidang;

echo "Testing COTA jadwal sidang visibility logic...\n\n";

// Test unverified user
$unverifiedUser = User::where('email', '<EMAIL>')->first();
if ($unverifiedUser) {
    $cotaData = Cota::where('email', $unverifiedUser->email)->first();
    echo "Unverified COTA user status: " . $cotaData->status . "\n";
    
    $jadwalSidang = [];
    if ($cotaData && !in_array($cotaData->status, ['menunggu_verifikasi', 'ditolak'])) {
        $jadwalSidang = JadwalSidang::where('tanggal_sidang', '>=', now())
                                    ->orderBy('tanggal_sidang', 'asc')
                                    ->limit(5)
                                    ->get();
    }
    echo "Jadwal sidang visible for unverified user: " . (count($jadwalSidang) > 0 ? 'YES' : 'NO') . "\n";
    echo "Count: " . count($jadwalSidang) . "\n\n";
}

// Test verified user
$verifiedUser = User::where('email', '<EMAIL>')->first();
if ($verifiedUser) {
    $cotaData = Cota::where('email', $verifiedUser->email)->first();
    echo "Verified COTA user status: " . $cotaData->status . "\n";
    
    $jadwalSidang = [];
    if ($cotaData && !in_array($cotaData->status, ['menunggu_verifikasi', 'ditolak'])) {
        $jadwalSidang = JadwalSidang::where('tanggal_sidang', '>=', now())
                                    ->orderBy('tanggal_sidang', 'asc')
                                    ->limit(5)
                                    ->get();
    }
    echo "Jadwal sidang visible for verified user: " . (count($jadwalSidang) > 0 ? 'YES' : 'NO') . "\n";
    echo "Count: " . count($jadwalSidang) . "\n\n";
}

echo "Logic test completed!\n";
