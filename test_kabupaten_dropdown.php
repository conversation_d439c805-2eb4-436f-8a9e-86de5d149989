<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\App;
use App\Models\Panti;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Kabupaten Dropdown Feature ===\n\n";

// Test 1: Check if kabupaten field exists in database
echo "1. Testing database field existence:\n";
try {
    $result = \Illuminate\Support\Facades\DB::select("DESCRIBE pantis");
    $fieldExists = false;
    foreach ($result as $field) {
        if ($field->Field === 'kabupaten') {
            $fieldExists = true;
            echo "   ✓ Field 'kabupaten' exists in database\n";
            echo "   ✓ Type: {$field->Type}\n";
            echo "   ✓ Null: {$field->Null}\n";
            break;
        }
    }
    
    if (!$fieldExists) {
        echo "   ✗ Field 'kabupaten' not found in database\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error checking database: " . $e->getMessage() . "\n";
}

// Test 2: Test creating/updating panti with kabupaten
echo "\n2. Testing panti data creation with kabupaten:\n";
try {
    $panti = Panti::updateOrCreate(
        ['user_id' => 1],
        [
            'nama' => 'Test Panti Asuhan',
            'alamat' => 'Jl. Test No. 123',
            'kabupaten' => 'Pekanbaru',
            'telepon' => '0761-123456',
            'email' => '<EMAIL>',
            'pimpinan' => 'Test Pimpinan',
            'npwp' => '12.345.678.9-101.000',
        ]
    );
    
    echo "   ✓ Panti created/updated successfully\n";
    echo "   ✓ ID: {$panti->id}\n";
    echo "   ✓ Nama: {$panti->nama}\n";
    echo "   ✓ Kabupaten: {$panti->kabupaten}\n";
    
} catch (Exception $e) {
    echo "   ✗ Error: " . $e->getMessage() . "\n";
}

// Test 3: Query existing panti data
echo "\n3. Testing query of existing panti data:\n";
try {
    $pantiList = Panti::whereNotNull('kabupaten')->get();
    echo "   ✓ Found " . $pantiList->count() . " panti with kabupaten data\n";
    
    foreach ($pantiList as $panti) {
        echo "   - {$panti->nama}: {$panti->kabupaten}\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error querying data: " . $e->getMessage() . "\n";
}

// Test 4: Test kabupaten options
echo "\n4. Testing kabupaten options:\n";
$kabupatenRiau = [
    'Pekanbaru',
    'Dumai', 
    'Bengkalis',
    'Indragiri Hilir',
    'Indragiri Hulu',
    'Kampar',
    'Kepulauan Meranti',
    'Kuantan Singingi',
    'Pelalawan',
    'Rokan Hilir',
    'Rokan Hulu',
    'Siak',
];

echo "   ✓ Available kabupaten options (" . count($kabupatenRiau) . " total):\n";
foreach ($kabupatenRiau as $kabupaten) {
    echo "     - {$kabupaten}\n";
}

echo "\n=== Test Complete ===\n";
