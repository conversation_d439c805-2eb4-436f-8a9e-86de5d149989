<?php

require 'vendor/autoload.php';

// Bootstrap Laravel application properly
$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\PengajuanBansos;
use App\Models\DokumenVerifikasi;

echo "=== TESTING DOKUMEN VERIFIKASI ===" . PHP_EOL;

// Test dengan pengajuan ID 1
$pengajuanId = 1;
$pengajuan = PengajuanBansos::with('dokumenVerifikasis')->find($pengajuanId);

if ($pengajuan) {
    echo "Pengajuan ID: {$pengajuan->id}" . PHP_EOL;
    echo "Nama Panti: {$pengajuan->nama_panti}" . PHP_EOL;
    echo "Status: {$pengajuan->status}" . PHP_EOL;
    
    echo "Dokumen Verifikasi:" . PHP_EOL;
    if ($pengajuan->dokumenVerifikasis->count() > 0) {
        foreach ($pengajuan->dokumenVerifikasis as $dok) {
            echo "  - Dokumen ID {$dok->dokumen_id}: {$dok->nama_dokumen} - {$dok->status_verifikasi}" . PHP_EOL;
        }
    } else {
        echo "  Belum ada verifikasi dokumen" . PHP_EOL;
    }
} else {
    echo "Pengajuan tidak ditemukan" . PHP_EOL;
}

echo PHP_EOL . "=== TESTING SAMPLE DATA ===" . PHP_EOL;

// Buat sample data verifikasi untuk testing
$sampleData = [
    1 => 'diterima',
    2 => 'diterima', 
    3 => 'ditolak',
    4 => 'diterima'
];

$dokumenList = [
    1 => 'Proposal Bantuan Sosial',
    2 => 'RAB (Rencana Anggaran Biaya)',
    3 => 'Surat Pengesahan Kemenkumham',
    4 => 'Data Anak',
];

foreach ($sampleData as $dokumenId => $status) {
    $created = DokumenVerifikasi::updateOrCreate(
        [
            'pengajuan_bansos_id' => $pengajuanId,
            'dokumen_id' => $dokumenId,
        ],
        [
            'nama_dokumen' => $dokumenList[$dokumenId],
            'status_verifikasi' => $status,
        ]
    );
    
    echo "Created/Updated: Dokumen {$dokumenId} - {$status}" . PHP_EOL;
}

echo PHP_EOL . "=== VERIFIKASI HASIL ===" . PHP_EOL;

$pengajuan = PengajuanBansos::with('dokumenVerifikasis')->find($pengajuanId);
echo "Total dokumen verifikasi: " . $pengajuan->dokumenVerifikasis->count() . PHP_EOL;
foreach ($pengajuan->dokumenVerifikasis as $dok) {
    echo "  - {$dok->nama_dokumen}: {$dok->status_verifikasi}" . PHP_EOL;
}
