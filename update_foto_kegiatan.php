<?php

require_once 'vendor/autoload.php';

use App\Models\LaporanKegiatan;

// Kon<PERSON>gurasi <PERSON> untuk command line
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Update laporan kegiatan dengan foto contoh
$laporans = LaporanKegiatan::all();

foreach ($laporans as $laporan) {
    if (!$laporan->foto_kegiatan) {
        $laporan->foto_kegiatan = 'foto_kegiatan_' . $laporan->id . '.jpg';
        $laporan->save();
        echo "Updated LaporanKegiatan ID {$laporan->id} with foto_kegiatan\n";
    }
}

echo "Done updating foto_kegiatan data\n";
